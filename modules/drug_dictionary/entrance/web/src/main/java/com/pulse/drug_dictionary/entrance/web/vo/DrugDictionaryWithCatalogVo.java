package com.pulse.drug_dictionary.entrance.web.vo;

import com.pulse.drug_dictionary.entrance.web.vo.DrugDictionaryWithCatalogVo.DrugCategoryVo;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "21580a9c-6812-4423-853b-dfbbc89ffdbe|VO|DEFINITION")
public class DrugDictionaryWithCatalogVo {
    /** 通用名 */
    @AutoGenerated(locked = true, uuid = "c509a92e-45aa-43cf-9587-a0082f342abf")
    private String commonNameCode;

    /** 药品分类 */
    @Valid
    @AutoGenerated(locked = true, uuid = "f1fccf23-bdf4-4500-843d-5636f01cd96c")
    private DrugCategoryVo drugCatalog;

    /** 药品编码 */
    @AutoGenerated(locked = true, uuid = "8095cfa9-19d1-4d25-8f7c-f092ada3455d")
    private String drugCode;

    /** 药品名称 */
    @AutoGenerated(locked = true, uuid = "d07b2441-28e3-4771-9fd2-651d2ef288f9")
    private String drugName;

    /** 药物根目录 */
    @Valid
    @AutoGenerated(locked = true, uuid = "f5d4752d-040a-49e9-afa6-bd4017855f9c")
    private DrugCategoryVo drugRootCatalog;

    /** 标准编码 */
    @AutoGenerated(locked = true, uuid = "94ac0ffd-5c9c-44b7-9aff-2e2578632bc4")
    private String standardCode;

    @Setter
    @Getter
    public static class DrugCategoryVo {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "93b8e630-7c7a-41d8-b144-380718824c59")
        private String id;

        /** 分类名称 */
        @AutoGenerated(locked = true, uuid = "bdb6c1d2-986c-4ac1-a335-aa46d2b69e5c")
        private String categoryName;

        /** 标准编码 */
        @AutoGenerated(locked = true, uuid = "3af41d7e-b428-4f54-a161-c59a17057b18")
        private String standardCode;
    }
}
