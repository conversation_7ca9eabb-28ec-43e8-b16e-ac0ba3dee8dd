package com.pulse.drug_dictionary.entrance.web.vo;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.drug_dictionary.common.enums.OriginTypeEnum;
import com.pulse.drug_dictionary.common.enums.ProducerDrugTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "990596de-b27b-44db-9d25-bba69a97ee80|VO|DEFINITION")
public class DrugProducerDictionaryVo {
    /** 药品类型 */
    @AutoGenerated(locked = true, uuid = "238bef90-3f02-41b8-833a-e7495c5aec9f")
    private ProducerDrugTypeEnum drugType;

    /** 启用标志 */
    @AutoGenerated(locked = true, uuid = "3070466a-45ad-49dc-ac2f-2ea2f7172d7a")
    private Boolean enableFlag;

    /** 外配标志 */
    @AutoGenerated(locked = true, uuid = "fc34ab83-d4f1-49da-81e8-53ac8b3e3eea")
    private Boolean externalFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "88986cb1-e22f-47b7-ad5d-4c20b62dd734")
    private String id;

    /** 拼音输入码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "de2cea7b-b44a-4e74-a0be-8cf5ee2d4422")
    private InputCodeEo inputCode;

    /** 生产商名称 */
    @AutoGenerated(locked = true, uuid = "f26d6942-bca0-4fd9-8dce-4eba213766b1")
    private String producerName;

    /** 生产商简称 */
    @AutoGenerated(locked = true, uuid = "678b07ea-9106-42ea-89fe-c6acd9e4acc2")
    private String producerNameAlias;

    /** 生产商类别 */
    @AutoGenerated(locked = true, uuid = "e6a21f60-4767-4071-ae92-62dfb37214ee")
    private OriginTypeEnum producerType;

    /** 省份编码 */
    @AutoGenerated(locked = true, uuid = "840eed99-3d1e-48c6-a998-631fe87f3ae4")
    private String provinceCode;

    /** 备注 */
    @AutoGenerated(locked = true, uuid = "0bc5c647-bb46-44e9-914a-17a4c1f7578d")
    private String remark;

    /** 标准编码 */
    @AutoGenerated(locked = true, uuid = "e2f31747-424a-4bd4-9207-a1a43772ea5a")
    private String standardCode;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "9bbc24f1-337a-43f7-9baa-103950704872")
    private Date updatedAt;

    /** 修改人 */
    @AutoGenerated(locked = true, uuid = "8c7b41ef-f749-482f-bb57-fdcff7a1e3db")
    private String updatedBy;
}
