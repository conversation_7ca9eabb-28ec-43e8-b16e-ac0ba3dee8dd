package com.pulse.drug_dictionary.entrance.web.vo;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "f3e9caa3-75c5-4b0d-9a76-ad7c5b81a3fb|VO|DEFINITION")
public class DrugSpecificationVo {
    /** 主键 */
    @AutoGenerated(locked = true, uuid = "5479d1ed-77ce-4313-a780-5f41bc7e15f5")
    private String id;

    /** 包装规格 包装规格 */
    @AutoGenerated(locked = true, uuid = "923014f1-f983-4bb1-934b-81fad6c9d278")
    private String packageSpecification;

    /** 药品规格名称 */
    @AutoGenerated(locked = true, uuid = "b0455172-88b0-4b93-871e-a733be86a307")
    private String specificationDrugName;
}
