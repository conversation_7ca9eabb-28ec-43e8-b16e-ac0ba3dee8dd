package com.pulse.drug_dictionary.entrance.web.vo;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "2c13515f-54e4-4163-ac0e-821f4f3c1df7|VO|DEFINITION")
public class DrugSpecificationDictionaryWithDrugAndCatalogVo {
    /** 药品编码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "f3c59f9d-7cfe-4785-b60e-7ac266dc439d")
    private DrugDictionaryWithCatalogVo drugCode;

    /** 剂型 */
    @AutoGenerated(locked = true, uuid = "739a48f7-080a-4b71-a131-577ba5b31eb8")
    private String drugForm;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "b43cd9e5-41fe-4928-916e-9340e58ef3a4")
    private String id;

    /** 最小单位 */
    @AutoGenerated(locked = true, uuid = "1975ab2a-771b-4534-88cc-20904d4e7e8d")
    private String minUnit;
}
