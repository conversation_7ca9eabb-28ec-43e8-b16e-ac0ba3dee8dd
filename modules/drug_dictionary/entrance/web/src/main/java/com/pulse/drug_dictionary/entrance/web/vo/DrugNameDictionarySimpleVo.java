package com.pulse.drug_dictionary.entrance.web.vo;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "0dad78ff-18bf-4908-a1cd-1c6bc488c8b1|VO|DEFINITION")
public class DrugNameDictionarySimpleVo {
    /** 药品名称 */
    @AutoGenerated(locked = true, uuid = "4405eb1a-3740-495e-a0be-a9409ea3597d")
    private String drugName;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "5a7dfdc9-bc4a-4734-8b2c-f7b97cedd27a")
    private String id;

    /** 输入码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "2a9a2ba8-7d7b-4386-b547-029c3844e734")
    private InputCodeEo inputCode;
}
