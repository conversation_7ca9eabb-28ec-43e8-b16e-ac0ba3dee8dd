package com.pulse.drug_dictionary.entrance.web.vo;

import com.pulse.drug_dictionary.common.enums.DrugTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "5777a4f3-35fd-4fc2-bbfa-4c6163683a65|VO|DEFINITION")
public class DrugOriginForDropDownVo {
    /** 帐簿类别 */
    @AutoGenerated(locked = true, uuid = "923719f9-f307-429d-bd13-08ed06d7a838")
    private String accountType;

    /** 捐赠标志 赠品药品控制不能调价，药品方案检索会显示赠品标志,可能还有其他业务场景 */
    @AutoGenerated(locked = true, uuid = "5d0b763d-7500-4772-ba1d-3fbde8cc19ff")
    private Boolean donationFlag;

    @Valid
    @AutoGenerated(locked = true, uuid = "37d54e8c-698e-4f4f-be1b-a6cadab69970")
    private List<DrugDictionaryRefDrugLocationVo> drugLocationList;

    @Valid
    @AutoGenerated(locked = true, uuid = "e73995fd-2ff0-4cee-bf9c-4676db62b6d1")
    private List<DrugNameDictionarySimpleVo> drugNameForDrugOriginList;

    @Valid
    @AutoGenerated(locked = true, uuid = "3a0e165a-4a18-4ca3-accc-7c5305621478")
    private List<DrugOriginCampusDisableSimpleVo> drugOriginCampusDisableList;

    /** 药品产地 */
    @AutoGenerated(locked = true, uuid = "7b2b9950-b457-4582-a2c9-1134e7d0949b")
    private String drugOriginCode;

    @Valid
    @AutoGenerated(locked = true, uuid = "7b429ec7-421b-4210-a7de-ef6abfe9556d")
    private DrugOriginExtensionSimpleVo drugOriginExtension;

    @Valid
    @AutoGenerated(locked = true, uuid = "e7624ac3-ad72-4ae7-b2d0-f3e46b3519da")
    private List<DrugDictionaryRefDrugOriginInventoryVo> drugOriginInventoryList;

    /** 药品产地名称 */
    @AutoGenerated(locked = true, uuid = "661052dd-e112-4d03-923e-234f183b6842")
    private String drugOriginName;

    /** 药品生产商 */
    @Valid
    @AutoGenerated(locked = true, uuid = "ea20776d-a705-4485-a12c-1d168d38d510")
    private DrugProducerSimpleVo drugProducer;

    /** 药品规格 */
    @Valid
    @AutoGenerated(locked = true, uuid = "484e47ab-2bef-4f30-bc3f-74439f174b44")
    private DrugSpecificationDictionaryForDropDownVo drugSpecification;

    /** 药品类型 */
    @AutoGenerated(locked = true, uuid = "1dc0fab7-155e-42b0-89bb-5ceab5a5ba4b")
    private DrugTypeEnum drugType;

    /** 免费药品标志 免费药品控制不能调价，可能还有其他业务场景 */
    @AutoGenerated(locked = true, uuid = "7f8f01da-e761-4786-8413-ce5b6aee8ba9")
    private Boolean freeFlag;

    /** gcp药品标识 */
    @AutoGenerated(locked = true, uuid = "e962b4e0-c9a6-4bfe-b923-c621a0fcb7ba")
    private Boolean gcpFlag;

    /** 注册商标 */
    @AutoGenerated(locked = true, uuid = "ecd59976-5ca7-493c-a0af-53c431fc98cb")
    private String registerBrand;

    /** 毒理分类 */
    @AutoGenerated(locked = true, uuid = "91967e07-5f12-4837-afda-3675f9ff1902")
    private String toxicType;

    /** 中标号 */
    @AutoGenerated(locked = true, uuid = "47298689-c412-44cd-a743-1ca56ae6e416")
    private String winningNumber;
}
