package com.pulse.drug_dictionary.entrance.web.vo;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.drug_dictionary.common.enums.AntibacterialLevelEnum;
import com.pulse.drug_dictionary.common.enums.AntineoplasticLevelEnum;
import com.pulse.drug_dictionary.common.enums.DrugTypeEnum;
import com.pulse.drug_dictionary.common.enums.SkinTestLevelEnum;
import com.pulse.drug_dictionary.common.enums.SkinTestTypeEnum;
import com.pulse.drug_dictionary.common.enums.SourceTypeEnum;
import com.pulse.drug_dictionary.common.enums.SpecificationTypeEnum;
import com.pulse.drug_dictionary.entrance.web.vo.DrugDetailVo.DrugDictionaryExtensionBaseVo;
import com.pulse.pharmacy_warehouse_setting.common.enums.ManageModelEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "db3566f7-be75-448b-9add-1b8948263404|VO|DEFINITION")
public class DrugDetailVo {
    /** 通用名 */
    @AutoGenerated(locked = true, uuid = "3af763db-2ac0-4ecc-b080-62a2d1bd5d23")
    private String commonNameCode;

    /** 药品分类 */
    @AutoGenerated(locked = true, uuid = "95981b99-225c-435e-a823-6d3e30bd3e4d")
    private String drugCatalogId;

    /** 药品编码 */
    @AutoGenerated(locked = true, uuid = "de117417-f354-40ad-8847-f3fbb2fbd86d")
    private String drugCode;

    @Valid
    @AutoGenerated(locked = true, uuid = "086f2e74-1ca1-4391-9c7d-f6ed50c4a787")
    private DrugDictionaryExtensionBaseVo drugDictionaryExtension;

    /** 药品名称 */
    @AutoGenerated(locked = true, uuid = "0140e221-0b1c-46d8-afcf-243465405c8a")
    private String drugName;

    /** 药物根目录ID */
    @AutoGenerated(locked = true, uuid = "5b950af2-057f-4682-941a-ff486955ee60")
    private String drugRootCatalogId;

    @Valid
    @AutoGenerated(locked = true, uuid = "6b4d63f7-b85c-4d53-94d6-342456c7e1c0")
    private List<DrugDetailVo.DrugSpecificationDictionaryBaseVo> drugSpecificationDictionaryList;

    /** 药品类别 西药、中草药、中成药、其它 */
    @AutoGenerated(locked = true, uuid = "7a58f3cd-c25d-4223-b1e2-c3019890b723")
    private DrugTypeEnum drugType;

    /** 英文名 */
    @AutoGenerated(locked = true, uuid = "d2609157-393f-49ea-b8cd-8b73b4750528")
    private String englishName;

    /** 中药类型 */
    @AutoGenerated(locked = true, uuid = "37290baa-af78-40fe-b951-6492a24b436c")
    private String herbType;

    /** 拼音码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "cff35a93-9ea3-45d4-bfd1-a6fc2432fff7")
    private InputCodeEo inputCode;

    /** 药理根类 码表可支持自定义 */
    @AutoGenerated(locked = true, uuid = "f3f9a07c-46ff-43b1-95f3-732beaa9d67b")
    private String parentPharmacologicalType;

    /** 药理类别 码表可支持自定义 */
    @AutoGenerated(locked = true, uuid = "a9a004a4-9fde-4a6b-a030-f53a462f1c14")
    private String pharmacologicalType;

    /** 来源 医院药品，药店药品，试验药品 */
    @AutoGenerated(locked = true, uuid = "80935cc1-e17d-4823-8e36-5ad5d2036d93")
    private SourceTypeEnum sourceType;

    /** 标准编码 */
    @AutoGenerated(locked = true, uuid = "562a4c3d-2d33-4293-895e-a4125d19a891")
    private String standardCode;

    /** 毒理类别 */
    @AutoGenerated(locked = true, uuid = "dd9c0d64-e497-48d2-9552-01c5c954f6b9")
    private String toxicType;

    @Setter
    @Getter
    public static class DrugOriginExtensionBaseVo {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "f873e435-34a3-4470-ab10-7d199404a1f3")
        private String id;

        /**
         * 药械平台药品id
         * 药械平台id，药械平台对应药品主键，做显示使用（药品检索方案也会显示），方便药师查找药械平台对应药品。这个字段浙二应该没有维护（对接完药械后会有一张医院药品和平台药品的字典对照表）
         */
        @AutoGenerated(locked = true, uuid = "26352a57-c663-4747-a95a-d11efc335ede")
        private String purchasePlatformDrugId;

        /** 批准文号 入库业务中会保存该字段 */
        @AutoGenerated(locked = true, uuid = "9a23a1f6-4d9e-44c7-8995-0f17d0385e9e")
        private String qualificationCode;

        /** 招标药品标志 出/入库单字段维护 */
        @AutoGenerated(locked = true, uuid = "f47d489d-14aa-4ef5-807d-c1f64e61457f")
        private Boolean bidFlag;

        /** 招标类型 只做了维护 */
        @AutoGenerated(locked = true, uuid = "a26df257-4fca-4dbf-9345-1806041c7af2")
        private String bidType;

        /** 国家医保代码 */
        @AutoGenerated(locked = true, uuid = "b248d105-868d-4f25-92ba-393b9b00f662")
        private String nationalMedicalInsuranceCode;

        /** 国家医保名称 */
        @AutoGenerated(locked = true, uuid = "d4832725-5d94-47fe-b796-0de091c44797")
        private String nationalMedicalInsuranceName;

        /** 加价率 */
        @AutoGenerated(locked = true, uuid = "a5999908-7d7f-466b-a822-adcf89623083")
        private BigDecimal markupRate;

        /** 药品进口证号 */
        @AutoGenerated(locked = true, uuid = "d01a199d-a985-4326-803e-f07f9e231344")
        private String importLicenceNumber;
    }

    @Setter
    @Getter
    public static class DrugOriginSpecificationBaseVo {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "2a5ea71c-9068-42bb-8bd7-1ce4c21bfcf4")
        private String id;

        /** 药品规格明细id */
        @AutoGenerated(locked = true, uuid = "065947d8-f80f-49b8-bf6b-0fe434ef8a18")
        private String drugSpecificationDetailId;

        /** 规格类型 包装规格、最小规格、其他拆分规格 */
        @AutoGenerated(locked = true, uuid = "d235be28-d727-4ead-a6ea-52749879fb6f")
        private SpecificationTypeEnum specificationType;

        /** 招标进价 */
        @AutoGenerated(locked = true, uuid = "b8669cdd-dd60-42f3-91ee-282a8a5c7efc")
        private BigDecimal bidPurchasePrice;

        /** 参考零售价一 */
        @AutoGenerated(locked = true, uuid = "76df6635-6aaf-4dd9-a9a3-d95b5673737c")
        private BigDecimal referenceRetailPriceOne;

        /** 医保支付价 */
        @AutoGenerated(locked = true, uuid = "af3a7472-f97e-4d0e-ac7b-d0c597f2077c")
        private BigDecimal insurancePayPrice;

        /** 规格 */
        @AutoGenerated(locked = true, uuid = "938276cc-2c7b-4323-8a20-544db40c7c21")
        private String drugSpecification;

        /** 单位 */
        @AutoGenerated(locked = true, uuid = "795df99e-a96a-4e4b-8ef9-4606fc957409")
        private String unit;

        /** 拆分系数 当前规格可以拆分出多少个最小规格 */
        @AutoGenerated(locked = true, uuid = "b65a8ee7-3ff0-4b3e-8a85-3d1dc9946f16")
        private Long amountPerPackage;
    }

    @Setter
    @Getter
    public static class DrugProducerDictionaryBaseVo {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "d58a2be9-688f-4309-8533-57baa13d8c52")
        private String id;

        /** 生产商名称 */
        @AutoGenerated(locked = true, uuid = "d62cbf74-cfaa-4d51-9bc3-369a452f2306")
        private String producerName;
    }

    @Setter
    @Getter
    public static class DrugOriginBaseVo {
        /** 药品产地 */
        @AutoGenerated(locked = true, uuid = "1e69debe-3fdb-409f-ae85-64a541f221cc")
        private String drugOriginCode;

        /** 药品编码 */
        @AutoGenerated(locked = true, uuid = "38cb9054-c199-4e2c-991b-538fa082c7de")
        private String drugCode;

        /** 药品生产商ID */
        @Valid
        @AutoGenerated(locked = true, uuid = "abf87f3a-d8fa-426b-8e1b-0112a7879271")
        private DrugProducerDictionaryBaseVo drugProducer;

        /** 启用标志 控制商品是否可用 */
        @AutoGenerated(locked = true, uuid = "c0c58345-bd7e-494c-87ed-971aa429068d")
        private Boolean enableFlag;

        /** gcp药品标识 */
        @AutoGenerated(locked = true, uuid = "2336c4c7-f976-4638-998c-ce2391cd31dc")
        private Boolean gcpFlag;

        /** 集采标识 集采标识，通过任务控制竞品开单限制 */
        @AutoGenerated(locked = true, uuid = "1a21b2f1-7a6b-408e-aa8e-de7d4d91d178")
        private Boolean centralPurchaseFlag;

        /** GMP标志 良好生产规范 */
        @AutoGenerated(locked = true, uuid = "39fa3591-032f-41e2-ad06-a7dba5933d20")
        private Boolean gmpFlag;

        /** 集采未中标住院限用药品 住院限制用药标识（需要配置白名单才能开医嘱） */
        @AutoGenerated(locked = true, uuid = "613bb86d-6c1e-41a7-a362-1c9f6047900c")
        private Boolean bidLostInpLimitFlag;

        /** 追溯码 */
        @AutoGenerated(locked = true, uuid = "a48f890b-5f72-4808-932c-d6eddf569a00")
        private String traceabilityCode;

        /** 捐赠标志 赠品药品控制不能调价，药品方案检索会显示赠品标志,可能还有其他业务场景 */
        @AutoGenerated(locked = true, uuid = "02387166-67e9-4755-821b-cb46f365f759")
        private Boolean donationFlag;

        /** 免费药品标志 免费药品控制不能调价，可能还有其他业务场景 */
        @AutoGenerated(locked = true, uuid = "520810b2-30bb-44cf-bbc5-262d7ba61447")
        private Boolean freeFlag;

        /** 自备标志HR3-49598(415955) 自备药品处方、医嘱需要发药生成领药单，但是不记账、不扣库存 */
        @AutoGenerated(locked = true, uuid = "79dcc695-0d63-4c6b-9819-067592e0d18e")
        private Boolean selfProvideFlag;

        /** 门诊使用标志 */
        @AutoGenerated(locked = true, uuid = "05e54e2a-ac27-42a4-b177-2e0cf3695992")
        private Boolean outpUsageFlag;

        /** 住院使用标志 */
        @AutoGenerated(locked = true, uuid = "532121ea-ae34-4339-90a3-8b18d88b1a3d")
        private Boolean inpUsageFlag;

        @Valid
        @AutoGenerated(locked = true, uuid = "813c7afb-257b-4d44-8741-e7d7f51cc11d")
        private DrugOriginExtensionBaseVo drugOriginExtension;

        @Valid
        @AutoGenerated(locked = true, uuid = "f88a2573-cd33-488a-a99e-2206760bb456")
        private List<DrugDetailVo.DrugOriginSpecificationBaseVo> drugOriginSpecificationList;

        /** 互联网标志 */
        @AutoGenerated(locked = true, uuid = "f79e1a2d-392c-4aa4-b58f-2c5104b25886")
        private Boolean internetFlage;
    }

    @Setter
    @Getter
    public static class DrugSpecificationDetailBaseVo {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "3b7bb750-181d-4a6e-9ba7-4ecfdab02994")
        private String id;

        /** 规格类型 包装规格、最小规格、其他拆分规格 */
        @AutoGenerated(locked = true, uuid = "b49c2f9c-e369-4442-bca9-803416e1dfab")
        private SpecificationTypeEnum specificationType;

        /** 规格 */
        @AutoGenerated(locked = true, uuid = "14dede13-2213-4872-943d-cead5b3bdde4")
        private String specification;

        /** 单位 */
        @AutoGenerated(locked = true, uuid = "2fadd985-ffbc-4b13-9d1c-6b5786d49432")
        private String unit;

        /** 拆分系数 当前规格可以拆分出多少个最小规格 */
        @AutoGenerated(locked = true, uuid = "2f316c1e-6715-4875-b25e-8f9172a62e83")
        private Long amountPerPackage;
    }

    @Setter
    @Getter
    public static class DrugSpecificationExtensionBaseVo {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "d95cb48b-303f-4e5a-bb35-ee571ef2a0f1")
        private String id;

        /** 溶媒标识 维护皮试带药对应的溶媒时使用（0.9%的氯化钠溶液） */
        @AutoGenerated(locked = true, uuid = "10b24e3b-3447-4d12-8041-f640912b18c8")
        private Boolean solventFlag;

        /** 辅料标识 中药辅料 */
        @AutoGenerated(locked = true, uuid = "5c04827d-9b56-4473-aaa3-462d5fdcd9af")
        private Boolean materialFlag;

        /** 自制制剂标识 */
        @AutoGenerated(locked = true, uuid = "a57826d3-c493-449a-939e-442711e7f840")
        private Boolean preparationFlag;

        /** 双签名标识 */
        @AutoGenerated(locked = true, uuid = "ba644d43-0317-4edc-ab39-7351c07ec678")
        private Boolean doubleSignatureFlag;

        /** 不上传医保标识 gcp实验用药、临床研究用药都不上传医保 */
        @AutoGenerated(locked = true, uuid = "9d6bf8cd-9158-4ed3-add7-8f05f7f088a0")
        private Boolean notUploadInsuranceFlag;

        /** 易致跌倒药品标识 */
        @AutoGenerated(locked = true, uuid = "387f5ffa-696f-4c7b-b2ff-d83087dfaaa4")
        private Boolean easyFallFlag;

        /** 住院带药/教育单 住院患者出院时，打印给患者的说明 */
        @AutoGenerated(locked = true, uuid = "38bdc23d-7999-42e1-bcbc-04bb17341d59")
        private String dischargeDescription;

        /** 医生用药指导 标签打印，医生端开药提醒 */
        @AutoGenerated(locked = true, uuid = "dd351357-9357-4c2e-af98-6407a9d665b6")
        private String orderDescription;

        /** 英文教育单 */
        @AutoGenerated(locked = true, uuid = "be61b36d-1abc-491d-b238-a880a3deec7b")
        private String englishDescription;

        /** 门诊标签 门诊的标签需要展示的用药说明内容，字数一般比较少 */
        @AutoGenerated(locked = true, uuid = "c5cb7ae7-07e2-4eef-a6a7-4f2b4d40f103")
        private String outpDescription;

        /** 静配标签 在静配药房中接收时需要打印的标签说明信息 */
        @AutoGenerated(locked = true, uuid = "8f386c09-1ee6-4c97-a6f3-d5d7ff054f86")
        private String pivasDescription;

        /** 药师审方要点 */
        @AutoGenerated(locked = true, uuid = "2723b123-b09f-450c-9d49-8c6fc7aa98ae")
        private String auditDescription;

        /** 过敏源分类 诊间患者过敏史登记可以通过过敏源分类筛选对应的药品 */
        @Valid
        @AutoGenerated(locked = true, uuid = "dfdfe882-ac9e-41fa-bf31-e815dbbf807a")
        private List<String> allergyType;

        /** 其他属性 各种商品固有属性（不参与业务逻辑的） */
        @Valid
        @AutoGenerated(locked = true, uuid = "bdd58228-4463-4387-b062-c0c039c95957")
        private List<String> otherAttribute;

        /** 罕见病对照 用于数据上报，根据药品对照的罕见病目录来统计各个病种开药的数量 */
        @Valid
        @AutoGenerated(locked = true, uuid = "40ffe359-3f97-44e9-bddb-26b8bb96fb7a")
        private List<String> rareDiseaseContrast;

        /** 配置后稳定性小时 用于静配标签打印 */
        @AutoGenerated(locked = true, uuid = "890b6907-539b-4496-b221-fc723d68e069")
        private BigDecimal stabilizationHour;

        /** 包药机特殊分包 */
        @AutoGenerated(locked = true, uuid = "45572cd2-7472-482f-9ab4-34321be9a9ee")
        private String machinePackagingWay;

        /** 内部编码 */
        @AutoGenerated(locked = true, uuid = "dac2f71f-9ac1-44f7-ac9d-d13c19274ff4")
        private String internalCode;

        /** 分类标识编码 */
        @AutoGenerated(locked = true, uuid = "52427e7d-3b7a-46b8-a48d-b61919268644")
        private String classifyIdentificationCode;
    }

    @Setter
    @Getter
    public static class DrugSpecificationDictionaryBaseVo {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "164c7de1-81cb-4e52-9cc0-c8fba3442ea4")
        private String id;

        /** 药品规格名称 */
        @AutoGenerated(locked = true, uuid = "6ce1b4f7-2811-4d3d-a891-050a03cb9671")
        private String specificationDrugName;

        /** 包装规格 包装规格 */
        @AutoGenerated(locked = true, uuid = "6347c4fe-3411-477f-ad2b-9395d97d6d8b")
        private String packageSpecification;

        /** 包装单位 包装单位 */
        @AutoGenerated(locked = true, uuid = "ffd765ef-bb4c-4e5e-9c37-2103eea421a5")
        private String packageUnit;

        /** 包装量 */
        @AutoGenerated(locked = true, uuid = "b602f99f-98b9-4452-97f7-be2241dd1882")
        private Long amountPerPackage;

        /** 最小规格 最小规格 */
        @AutoGenerated(locked = true, uuid = "969674d4-4c23-44b7-8d20-f2058270876d")
        private String minSpecification;

        /** 剂量 剂量 */
        @AutoGenerated(locked = true, uuid = "dd81ca16-81a8-4470-b1b7-b63587b0486e")
        private BigDecimal dosage;

        /** 剂量单位 */
        @AutoGenerated(locked = true, uuid = "4f479091-d2c2-4025-84b0-252f4ed32e78")
        private String dosageUnit;

        /** 浓度 */
        @AutoGenerated(locked = true, uuid = "b1259b31-f0c0-43fa-b0cb-8e122280dfe0")
        private BigDecimal concentration;

        /** 体积 */
        @AutoGenerated(locked = true, uuid = "bf90ba2f-d9f5-4a2a-b424-60fd2dc66776")
        private BigDecimal volume;

        /** 体积单位 */
        @AutoGenerated(locked = true, uuid = "b83f17c7-fc95-4d6d-8e39-70c35b574bd8")
        private String volumeUnit;

        /**
         * 基药标识 非基药、基药 （1、基药是满足基本医疗需求的药物，是用于预防、诊断和治疗最重要的疾病的药物。
         * 2、非基药是相对于基药而言的，并不是满足基本医疗需求的药物，可能用于一些特定或专科的治疗。）
         */
        @AutoGenerated(locked = true, uuid = "fe2895b4-cb6f-4ab6-bae5-9195cbf71e5b")
        private Boolean basicFlag;

        /** 剂型 */
        @AutoGenerated(locked = true, uuid = "c91ec305-655f-40ea-baa8-c73a3fe13c76")
        private String drugForm;

        /** 帐簿类别 西药、成药、草药类型下得一个细分类型，比如：草药下面含有饮片和颗粒剂，在草药房发药时会根据饮片和颗粒剂区分为两个菜单进行发药 */
        @AutoGenerated(locked = true, uuid = "19f4a216-025f-4285-908c-4dff93ae50d2")
        private String accountType;

        /** 大输液标志 医生站开立医嘱时，会通过大输液标志判断流向哪个药房 */
        @AutoGenerated(locked = true, uuid = "f6aaedd4-9363-4176-9dfc-37e2fc7f5157")
        private Boolean bigInfusionFlag;

        /** 特殊剂量 */
        @AutoGenerated(locked = true, uuid = "f56a16ed-0126-41c1-9922-2b276e87780e")
        private BigDecimal specialDosage;

        /** 抗生素标识 */
        @AutoGenerated(locked = true, uuid = "bd242402-0247-468b-b469-dd7ebcd25b26")
        private Boolean antibioticFlag;

        /** 抗生素级别 非限制级、限制级、特殊使用级 */
        @AutoGenerated(locked = true, uuid = "34ff12b0-5b31-454f-b358-59009136aa17")
        private String antibioticLevel;

        /** 抗肿瘤药物级别 普通级、限制级 */
        @AutoGenerated(locked = true, uuid = "225e3473-8467-4a07-aa31-554a1066dcf0")
        private AntineoplasticLevelEnum antineoplasticLevel;

        /** 皮试等级 强制皮试，建议皮试 */
        @AutoGenerated(locked = true, uuid = "04768438-d2a3-406f-b3c5-8c9ed63d48de")
        private SkinTestLevelEnum skinTestLevel;

        /** 皮试类型 原液皮试，非原液皮试，二者皆可 */
        @AutoGenerated(locked = true, uuid = "2e6515a9-04a9-4370-b5ce-d3d1b2f4d43f")
        private SkinTestTypeEnum skinTestType;

        /** 皮试有效天数 皮试结果有效时间 */
        @AutoGenerated(locked = true, uuid = "99af8106-b3c4-4799-a58d-31d951c5bbc4")
        private Long skinTestEfficientDays;

        /** 出院带药标识 是否可开出院带药处方 */
        @AutoGenerated(locked = true, uuid = "bee6f52d-1cbf-45ec-8438-fc51268c4f9a")
        private Boolean dischargeFlag;

        /** 化疗药品标识 是否可开化疗处方 */
        @AutoGenerated(locked = true, uuid = "d0d2feb3-cd32-469a-ac1a-b60119d09c79")
        private Boolean chemotherapyFlag;

        /** EN药品标识 */
        @AutoGenerated(locked = true, uuid = "aad8d62d-659b-438f-beae-f8fd49f47fb5")
        private Boolean enFlag;

        /** TPN药品标识 */
        @AutoGenerated(locked = true, uuid = "afd531cd-3a96-4aba-af27-5c064b8397e7")
        private Boolean tpnFlag;

        /** 手术用药标识 */
        @AutoGenerated(locked = true, uuid = "9da03215-**************-5dc4582191a9")
        private Boolean surgeryFlag;

        /** 麻醉用药标识 */
        @AutoGenerated(locked = true, uuid = "80a31ee9-3f11-4fb4-bd23-190fdc74b544")
        private Boolean anesthesiaFlag;

        /** 冷藏药标识 */
        @AutoGenerated(locked = true, uuid = "88a058a9-5777-4cca-bf28-ea9ed8b7009d")
        private Boolean coldFlag;

        /** 高危药标识 */
        @AutoGenerated(locked = true, uuid = "14ec36ef-b656-4628-9c1c-f39c24693932")
        private Boolean dangerFlag;

        @Valid
        @AutoGenerated(locked = true, uuid = "71f0a4c1-1fdc-490c-bce5-58707ca1d397")
        private DrugSpecificationExtensionBaseVo drugSpecificationExtension;

        @Valid
        @AutoGenerated(locked = true, uuid = "c39fb3ee-b7cd-4f1d-8782-6b2387de05f6")
        private List<DrugDetailVo.DrugSpecificationDetailBaseVo> drugSpecificationDetailList;

        @Valid
        @AutoGenerated(locked = true, uuid = "b0a585db-83b0-42e6-ad0e-81b86ffadb60")
        private List<DrugDetailVo.DrugOriginBaseVo> drugOriginList;

        /** 拼音码 */
        @Valid
        @AutoGenerated(locked = true, uuid = "3100345f-c961-4346-bd8d-6a096ba02846")
        private InputCodeEo inputCode;

        /** 眼科剂量 */
        @AutoGenerated(locked = true, uuid = "d756f1e0-ca5e-47a0-a47f-025005898d0c")
        private Long ophthalmologyDosage;

        /** 眼科剂量单位 */
        @AutoGenerated(locked = true, uuid = "312393a7-c859-4a89-bd8e-2fed77a18110")
        private String ophthalmicDosageUnit;

        /** 最小单位 */
        @AutoGenerated(locked = true, uuid = "6b107b24-6271-4b25-bd4b-a5982a808330")
        private String minUnit;

        /** 存储条件 */
        @AutoGenerated(locked = true, uuid = "1dae0e70-a5c7-44cd-8036-d805c385d1d0")
        private String storeCondition;

        /** 每箱数量 */
        @AutoGenerated(locked = true, uuid = "c2416791-25ec-4081-8883-c8f51e7a7ae5")
        private Long amountPerBox;

        /** 每包克数 */
        @AutoGenerated(locked = true, uuid = "a04af4ec-c615-4ad1-95ec-6ce1665bd039")
        private BigDecimal gramsPerPackage;

        /** 管理模式 */
        @AutoGenerated(locked = true, uuid = "4aace87e-c018-419a-b477-006d55a4035f")
        private ManageModelEnum manageMode;

        /** 抗菌药标志 */
        @AutoGenerated(locked = true, uuid = "cf39dd26-eca5-46e9-b8bb-bdc75751843f")
        private Boolean antibacterialFlag;

        /** 抗菌药限级 */
        @AutoGenerated(locked = true, uuid = "6d0facf0-1f9a-43a3-a2c8-84d0e0146b08")
        private AntibacterialLevelEnum antibacterialLevel;

        /** 日常用量 */
        @AutoGenerated(locked = true, uuid = "bc075114-189e-4dfe-acd0-d9b4863aeb90")
        private BigDecimal dailyUsage;

        /** 标签单位 */
        @AutoGenerated(locked = true, uuid = "5af85dae-6bcd-4cd0-8726-37a2ba005a9c")
        private String labelUnit;

        /** 皮试标志 */
        @AutoGenerated(locked = true, uuid = "77e3f9b9-b881-4174-ab98-6477f2e1d069")
        private Boolean skinTestFlag;

        /** 特殊剂量单位 */
        @AutoGenerated(locked = true, uuid = "67d72003-2991-43fe-b04e-9ad41c3f5050")
        private String specialDosageUnit;
    }

    @Setter
    @Getter
    public static class DrugDictionaryExtensionBaseVo {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "90738774-9d7d-4f2f-8c58-ffec6f5b1fa5")
        @NotNull(message = "主键不能为空")
        private String id;

        /** 住院带药/教育单 住院患者出院时，打印给患者的说明 */
        @AutoGenerated(locked = true, uuid = "09fd6968-1b55-42a6-8251-2b7466faec95")
        private String dischargeDescription;

        /** 医生用药指导 标签打印，医生端开药提醒 */
        @AutoGenerated(locked = true, uuid = "94484d22-3c84-41cc-b3ea-5910fe821f39")
        private String orderDescription;

        /** 英文教育单 */
        @AutoGenerated(locked = true, uuid = "0c08144d-0b93-4288-a327-f39ca0c9ec69")
        private String englishDescription;

        /** 门诊标签 门诊的标签需要展示的用药说明内容，字数一般比较少 */
        @AutoGenerated(locked = true, uuid = "ee9f7770-35d8-48c5-abc8-37d6f6dc8120")
        private String outpDescription;

        /** 静配标签 在静配药房中接收时需要打印的标签说明信息 */
        @AutoGenerated(locked = true, uuid = "d564b7b9-40e6-4ec3-bb90-47f8678f783b")
        private String pivasDescription;

        /** 药师审方要点 */
        @AutoGenerated(locked = true, uuid = "c5cdf340-7526-41c9-bc18-feb02e65e4ab")
        private String auditDescription;
    }
}
