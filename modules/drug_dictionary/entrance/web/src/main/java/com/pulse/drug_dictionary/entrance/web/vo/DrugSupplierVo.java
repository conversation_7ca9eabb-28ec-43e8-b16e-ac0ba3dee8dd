package com.pulse.drug_dictionary.entrance.web.vo;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.drug_dictionary.common.enums.SupplierTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "de498c86-**************-c3e429e144e5|VO|DEFINITION")
public class DrugSupplierVo {
    /** 应付款标志 */
    @AutoGenerated(locked = true, uuid = "8b296cdd-0db3-41d0-8a14-32aac3a640c3")
    private Boolean accountsPayableFlag;

    /** 银行账户号码 */
    @AutoGenerated(locked = true, uuid = "ea20ea6d-0649-4305-86f3-9b55c342ded3")
    private Long bankAccountNumber;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "a6ebee50-0d80-4474-95cc-52793a9ece63")
    private Date createdAt;

    /** 创建者id */
    @AutoGenerated(locked = true, uuid = "e940bb1f-32d4-4690-8a09-4fa3a672f802")
    private String createdBy;

    /** 开户银行 */
    @AutoGenerated(locked = true, uuid = "5e8de61c-3148-4f2a-a36d-a736357f7183")
    private String depositBank;

    /** 显示标志 */
    @AutoGenerated(locked = true, uuid = "9e50f01f-a63b-4097-b28d-2c9345adb96d")
    private Boolean displayFlag;

    /** 邮箱 */
    @AutoGenerated(locked = true, uuid = "2f3228af-cbac-42b5-aab4-8aa5b89f8d5c")
    private String email;

    /** 启用标志 */
    @AutoGenerated(locked = true, uuid = "3e999cb8-1a2e-4723-895a-cf4951b44fe2")
    private Boolean enableFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "9b25dcb0-0bfa-4f92-8a18-7da8ad24d9aa")
    @NotNull(message = "主键不能为空")
    private String id;

    /** 拼音输入码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "8dd21489-0f93-481a-8c36-9085d7272408")
    private InputCodeEo inputCode;

    /** 省份编码 */
    @AutoGenerated(locked = true, uuid = "7d15d284-1605-4350-9b21-bb3cea43cd46")
    private String provinceCode;

    /** 备注 */
    @AutoGenerated(locked = true, uuid = "9886c192-f18c-49e7-a9b7-5adbb081304b")
    private String remark;

    /** 标准编码 */
    @AutoGenerated(locked = true, uuid = "503ae6cc-4870-4a3c-913a-e8b93f17bb24")
    private String standardCode;

    /** 库房编码 */
    @AutoGenerated(locked = true, uuid = "8cdb73ea-ce31-4d33-8eb7-b05baac416e7")
    private String storageCode;

    /** 供应商地址 */
    @AutoGenerated(locked = true, uuid = "ce1fc247-9f96-4b87-bb2a-dc2a76586707")
    private String supplierAddress;

    /** 联系人 */
    @AutoGenerated(locked = true, uuid = "e0ad2810-e856-4f60-a951-8d01f47a1839")
    private String supplierContactPerson;

    /** 供应商名称 */
    @AutoGenerated(locked = true, uuid = "78a88e1a-5130-49f4-9f6d-ad5a60b083e8")
    private String supplierName;

    /** 供应商简称 */
    @AutoGenerated(locked = true, uuid = "ab75ef91-a3b4-4e9e-be30-d03223106120")
    private String supplierNameAlias;

    /** 供应商电话 */
    @AutoGenerated(locked = true, uuid = "3a3f29e3-881d-4283-b6bb-58461147ac61")
    private String supplierPhone;

    /** 供应商类型 生产厂商、供应商 */
    @AutoGenerated(locked = true, uuid = "b162a7ab-ed21-46a2-8941-056ae89f6ef0")
    private SupplierTypeEnum supplierType;

    /** 税号 */
    @AutoGenerated(locked = true, uuid = "76f6d1fb-8913-4d06-bdda-a742bdc19d47")
    private String taxId;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "f079bfb8-9d78-449a-bba7-138096b9186c")
    private Date updatedAt;

    /** 修改人 */
    @AutoGenerated(locked = true, uuid = "43d8d1f3-5876-4361-8d57-68e89f1b27e4")
    private String updatedBy;
}
