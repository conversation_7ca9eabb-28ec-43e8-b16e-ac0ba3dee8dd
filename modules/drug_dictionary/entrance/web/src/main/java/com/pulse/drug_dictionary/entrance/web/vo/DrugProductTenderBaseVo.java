package com.pulse.drug_dictionary.entrance.web.vo;

import com.pulse.drug_dictionary.entrance.web.vo.DrugProductTenderBaseVo.DrugProductBaseVo;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "834034b6-718a-4312-abd9-737d1cd80fd5|VO|DEFINITION")
public class DrugProductTenderBaseVo {
    /** 扣率 */
    @AutoGenerated(locked = true, uuid = "da9901d5-6687-4acc-9bf9-309b4ef004d9")
    private BigDecimal discountRate;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "20fa395b-58ae-4ae2-a220-c33d0e4a86e0")
    private String id;

    /** 药品商品编码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "49e94046-2c3b-4711-9325-025dd94b377e")
    private DrugProductBaseVo productCode;

    /** 进价 */
    @AutoGenerated(locked = true, uuid = "bf6b6829-ff7f-4ec0-baeb-d24680d7173f")
    private BigDecimal purchasePrice;

    /** 招标编码 */
    @AutoGenerated(locked = true, uuid = "a8b7f702-4a2f-4936-a640-4328b9211c15")
    private String tenderCode;

    /** 招标单位编码 */
    @AutoGenerated(locked = true, uuid = "95780a6e-a706-4b0a-bb31-46bb38e9db01")
    private String tenderUnitCode;

    /** 招标单位名称 */
    @AutoGenerated(locked = true, uuid = "2ea2299d-c2ca-4629-b7a9-830cfd6cad75")
    private String tenderUnitName;

    @Setter
    @Getter
    public static class DrugProductBaseVo {
        /** 商品编码 */
        @AutoGenerated(locked = true, uuid = "f479a282-64b3-43d6-a22b-60e51a40ddae")
        private String drugOriginCode;

        /** 商品名称 */
        @AutoGenerated(locked = true, uuid = "6bb8b4be-e980-489f-9ef4-a0da86b08b18")
        private String drugOriginName;

        /** 全院停用标识 控制商品是否可用 */
        @AutoGenerated(locked = true, uuid = "9bf457bd-2c8f-48c3-a4ed-f4688f6bb71d")
        private Boolean enableFlag;

        /** 包装规格 冗余存 */
        @AutoGenerated(locked = true, uuid = "33dd7b92-f3e3-4aff-a7d4-57864c5a5e83")
        private String packageSpecification;

        /** 包装单位 冗余存 */
        @AutoGenerated(locked = true, uuid = "327b3caa-3845-4495-a0dc-fe200ae65b3b")
        private String packageUnit;

        /** GMP标志 良好生产规范 */
        @AutoGenerated(locked = true, uuid = "c66d5ba0-8e33-440f-b514-26a6456da473")
        private Boolean gmpFlag;
    }
}
