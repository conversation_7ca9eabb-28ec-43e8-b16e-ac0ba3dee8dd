package com.pulse.drug_dictionary.entrance.web.vo;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.drug_dictionary.common.enums.NameTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "4725aa7c-1d82-4828-b2ca-dc7903032403|VO|DEFINITION")
public class DrugAliasForEditVo {
    /** 药品代码 */
    @AutoGenerated(locked = true, uuid = "b243521a-5ce9-4c46-9426-d718deb757ad")
    private String drugCode;

    /** 药品别名 */
    @AutoGenerated(locked = true, uuid = "6a259f2e-4fe9-4397-84b4-9db70b594744")
    private String drugName;

    /** 药品产地编码 */
    @AutoGenerated(locked = true, uuid = "0e244f31-ccb8-4404-8088-bd9549a59b72")
    private String drugOriginCode;

    /** 药品名称 */
    @AutoGenerated(locked = true, uuid = "56ea630c-ce9e-4681-9499-2ef544752a64")
    private String drugOriginName;

    /** 商品规格id */
    @AutoGenerated(locked = true, uuid = "f670451d-914b-4907-8321-9321f9d5d956")
    private String drugSpecificationId;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "3ca7afc5-5ff4-420a-b7b2-dd02d5ce30f4")
    private String id;

    /** 输入码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "69c34906-dc10-400e-baa5-bc688b3dc9a8")
    private InputCodeEo inputCode;

    /** 名称类型 通用名、化学名、英文名、商品名、别名 */
    @AutoGenerated(locked = true, uuid = "7fc859e8-e3bd-4ff8-a1cc-4de4756014f0")
    private NameTypeEnum nameType;
}
