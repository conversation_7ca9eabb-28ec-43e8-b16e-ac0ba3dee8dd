package com.pulse.drug_dictionary.entrance.web.vo;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "b1fa317d-2737-4e70-b7ad-04905e29e1a8|VO|DEFINITION")
public class DrugProducerSimpleVo {
    /** 主键 */
    @AutoGenerated(locked = true, uuid = "db44bb12-02f8-41ab-a8cd-b35c32691142")
    private String id;

    /** 厂商 */
    @AutoGenerated(locked = true, uuid = "7c86e08e-a735-41ce-a442-97a49c86dc7f")
    private String producerName;
}
