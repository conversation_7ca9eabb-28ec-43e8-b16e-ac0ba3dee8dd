package com.pulse.drug_dictionary.entrance.web.vo;

import com.pulse.drug_dictionary.common.enums.OriginTypeEnum;
import com.pulse.drug_dictionary.common.enums.ProducerDrugTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "9a8d7b65-f415-4850-aac9-4745350c7322|VO|DEFINITION")
public class DrugFirmVo {
    /** 药品类型 */
    @AutoGenerated(locked = true, uuid = "106bdff8-47aa-4cfe-b553-d005bfa58660")
    private ProducerDrugTypeEnum drugType;

    /** 是否停用 */
    @AutoGenerated(locked = true, uuid = "2310adf8-841e-4a6f-bce9-94d9a11baed1")
    private Boolean enableFlag;

    /** 外配标志 */
    @AutoGenerated(locked = true, uuid = "2e856158-cc01-4b14-8f98-0d7f9dc8d139")
    private Boolean externalFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "1c07a05e-5607-4a77-b92f-08299c0b96ad")
    private String id;

    /** 厂商 */
    @AutoGenerated(locked = true, uuid = "31fe54d1-8533-4a69-a670-7bbf351cfc33")
    private String producerName;

    /** 厂商简称 */
    @AutoGenerated(locked = true, uuid = "dc926b94-0859-433b-84c8-4e77412386d2")
    private String producerNameAlias;

    /** 产地类别 */
    @AutoGenerated(locked = true, uuid = "476ec294-60b0-4fb9-937b-d2526d1a9dca")
    private OriginTypeEnum producerType;

    /** 备注 */
    @AutoGenerated(locked = true, uuid = "b82a0eb1-3274-47b2-adf9-377826db5a91")
    private String remark;

    /** 标准编码 */
    @AutoGenerated(locked = true, uuid = "14d341ec-266f-47e9-b154-c01c5434c826")
    private String standardCode;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "aba6a5f5-1db5-472d-98f6-befe96a9ef2a")
    private Date updatedAt;
}
