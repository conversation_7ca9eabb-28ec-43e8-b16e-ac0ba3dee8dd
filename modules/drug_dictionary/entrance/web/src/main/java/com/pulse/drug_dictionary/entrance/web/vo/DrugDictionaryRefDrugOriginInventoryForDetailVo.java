package com.pulse.drug_dictionary.entrance.web.vo;

import com.pulse.drug_inventory.common.enums.UseFrequencyEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "238235de-060f-498a-a8d8-51e4e75be96c|VO|DEFINITION")
public class DrugDictionaryRefDrugOriginInventoryForDetailVo {
    /** 数量 库存数量，按最小规格单位存储 */
    @AutoGenerated(locked = true, uuid = "717500ab-ade9-4106-b4d3-3b5533f3de51")
    private BigDecimal amount;

    /** 主键 */
    @Valid
    @AutoGenerated(locked = true, uuid = "c067e48f-15b7-45f7-ae51-ee538c80856d")
    private List<DrugDictionaryRefDrugOriginBatchInventoryVo> drugOriginBatchInventoryList;

    /** 药品产地编码 */
    @AutoGenerated(locked = true, uuid = "1640bdae-0472-44bc-ba93-25ecb5ba1894")
    private String drugOriginCode;

    /** 药品产地规格 */
    @Valid
    @AutoGenerated(locked = true, uuid = "f091fd32-595f-4561-b7da-2ec0bafeecfc")
    private DrugOriginSpecificationSimpleVo drugOriginSpecification;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "78b387f7-f806-4ba4-a636-59cccd3687dc")
    private String id;

    /** 在途数量 药库已出库，当前库房未入库的数量 */
    @AutoGenerated(locked = true, uuid = "2623f155-6b7b-4433-861a-32c0060a604d")
    private BigDecimal inTransitAmount;

    /** 预占数量 待发药数量 */
    @AutoGenerated(locked = true, uuid = "f3ccfd02-bab2-49a2-b985-6bd4ccc0960a")
    private BigDecimal preOccupiedAmount;

    /** 库房编码 */
    @AutoGenerated(locked = true, uuid = "7cb86cd6-ff50-457a-9f88-cf1753b1b77a")
    private String storageCode;

    /** 使用频率 */
    @AutoGenerated(locked = true, uuid = "3185abc4-43f5-4f22-9525-9f49cbe5c8ab")
    private UseFrequencyEnum useFrequency;

    /**
     * 虚库存数量 虚库存数量增减时，数量字段需要同步增减 虚库存只有药房才启用 使用场景： 1、药房急用，药库来不及出库的药品
     * 2、超发药品（开封有时效的注射剂，开药时按瓶发，但使用时可能多个患者共用一瓶，导致线下库存比线上多）
     * 3、中药饮片/颗粒剂--院内药（医院无实际库存，第三方自己管理库存，开药后通过采购、出入库平账）
     */
    @AutoGenerated(locked = true, uuid = "5868fe9b-24df-4183-88a2-1faa3b55ff2f")
    private BigDecimal virtualAmount;
}
