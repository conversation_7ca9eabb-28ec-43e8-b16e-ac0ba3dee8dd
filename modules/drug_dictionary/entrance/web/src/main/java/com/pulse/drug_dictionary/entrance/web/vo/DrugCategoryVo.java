package com.pulse.drug_dictionary.entrance.web.vo;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.drug_dictionary.common.enums.DrugTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "36af2fec-4cfb-45bb-9e4b-2a5664c12e7f|VO|DEFINITION")
public class DrugCategoryVo {
    /** 分类名称 */
    @AutoGenerated(locked = true, uuid = "9b18f769-8f79-43c4-b3a6-83044aeb05bd")
    private String categoryName;

    /** 药品类型 */
    @AutoGenerated(locked = true, uuid = "bacdd90d-2e2a-4555-b804-9e0003304e11")
    private DrugTypeEnum drugType;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "84bf8348-8fe9-4e13-b87c-ccdd649bd801")
    private String id;

    /** 拼音码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "cb45cbe3-12fd-4dd6-88f4-58f0849a27a9")
    private InputCodeEo inputCode;

    /** 作废标志 */
    @AutoGenerated(locked = true, uuid = "f7144a82-5694-4037-a118-2e5ab7e9ca73")
    private Boolean invalidFlag;

    /** 末级标志 */
    @AutoGenerated(locked = true, uuid = "4d95af50-26bd-44fd-91e8-f571f611b98b")
    private Boolean lastFlag;

    /** 上级分类 */
    @AutoGenerated(locked = true, uuid = "39b0abb1-b7f3-4d14-9080-044604b05e09")
    private String parentId;

    /** 静配批次序号 */
    @AutoGenerated(locked = true, uuid = "7ae52ebd-0d18-4085-a8c8-18d859a229fc")
    private Long pivasBatchSortNumber;

    /** 药品根类ID */
    @AutoGenerated(locked = true, uuid = "d0dbd279-754b-4d4f-9ebc-b11c2b264fad")
    private String rootId;

    /** 排序号 */
    @AutoGenerated(locked = true, uuid = "9d875d57-f6db-4b87-ba3a-5fda7062a8f0")
    private Long sortNumber;

    /** 修改人 */
    @AutoGenerated(locked = true, uuid = "78bedc1d-28ec-4471-9b68-b01e4d2c1b6a")
    private String staffId;

    /** 标准编码 */
    @AutoGenerated(locked = true, uuid = "1e4c6697-531d-49c4-95e3-782f91b7c97a")
    private String standardCode;
}
