package com.pulse.drug_dictionary.persist.mapper;

import com.pulse.drug_dictionary.persist.dos.DrugSupplierQualityEvaluation;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "915a5b82-**************-757dac90752d|ENTITY|IDAO")
public interface DrugSupplierQualityEvaluationDao {

    @AutoGenerated(locked = true, uuid = "15adcfc9-ede3-3545-8104-e4e62272d41a")
    List<DrugSupplierQualityEvaluation> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "2db97d41-0056-3d76-abf2-9a1ff3f635e3")
    DrugSupplierQualityEvaluation getById(String id);

    @AutoGenerated(locked = true, uuid = "303adae8-860b-31f2-913b-323336811d10")
    List<DrugSupplierQualityEvaluation> getBySupplierId(String supplierId);

    @AutoGenerated(locked = true, uuid = "4b97de4c-4fbb-3397-a2f4-bb6c2159c3e5")
    List<DrugSupplierQualityEvaluation> getBySupplierIds(List<String> supplierId);
}
