package com.pulse.drug_dictionary.persist.mapper;

import com.pulse.drug_dictionary.persist.dos.DrugOriginTender;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "b57353e5-4038-3a9b-b881-6d14356d35eb|ENTITY|IDAO")
public interface DrugOriginTenderDao {

    @AutoGenerated(locked = true, uuid = "166bb559-5a5a-3bd6-a883-5fdb51afd127")
    List<DrugOriginTender> getBySupplierId(String supplierId);

    @AutoGenerated(locked = true, uuid = "3ea8c645-b277-321b-b64d-0b785c93a7c6")
    List<DrugOriginTender> getBySupplierIds(List<String> supplierId);

    @AutoGenerated(locked = true, uuid = "49bbc31f-c489-378e-b1b9-c4b14ee20e81")
    List<DrugOriginTender> getByDrugOriginCodes(List<String> drugOriginCode);

    @AutoGenerated(locked = true, uuid = "49fb28e8-44ff-3569-90fb-8b6344286280")
    List<DrugOriginTender> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "70cc49d1-456b-3859-a1fd-2362ae3bec3c")
    DrugOriginTender getById(String id);

    @AutoGenerated(locked = true, uuid = "7b0c3bc7-2300-3a95-8128-5f6cdefd13f6")
    List<DrugOriginTender> getByDrugOriginCode(String drugOriginCode);
}
