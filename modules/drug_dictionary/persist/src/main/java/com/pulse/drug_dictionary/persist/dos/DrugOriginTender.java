package com.pulse.drug_dictionary.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName(value = "drug_origin_tender", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "b57353e5-4038-3a9b-b881-6d14356d35eb|ENTITY|DEFINITION")
public class DrugOriginTender {
    @AutoGenerated(locked = true, uuid = "eccedbcb-8a85-36e0-b99c-8769118e0722")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "d007e03b-b090-32ae-a044-a278fa7ffcb3")
    @TableField(value = "created_by")
    private String createdBy;

    @AutoGenerated(locked = true, uuid = "df21538f-d7f7-38d1-ad84-22e62c86d626")
    @TableField(value = "discount_rate")
    private BigDecimal discountRate;

    @AutoGenerated(locked = true, uuid = "b8ff3685-a83d-359a-bb25-5847798b4a31")
    @TableField(value = "drug_origin_code")
    private String drugOriginCode;

    @AutoGenerated(locked = true, uuid = "f93187a2-7c7f-3df2-be54-2120b01c3d50")
    @TableId(value = "id")
    private String id;

    /** 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "5be1609d-b16b-43c8-8017-8494f90ddd95")
    @TableField(value = "lock_version")
    private Long lockVersion;

    @AutoGenerated(locked = true, uuid = "be13eff8-5c2f-3458-8c8c-bd82f3df9fde")
    @TableField(value = "purchase_price")
    private BigDecimal purchasePrice;

    @AutoGenerated(locked = true, uuid = "cbd0389c-64ea-42cb-a06f-a2fae876958f")
    @TableField(value = "supplier_id")
    private String supplierId;

    @AutoGenerated(locked = true, uuid = "cec5a53b-54de-3514-9ad7-0c280089d948")
    @TableField(value = "tender_code")
    private String tenderCode;

    @AutoGenerated(locked = true, uuid = "7359ab0a-3a52-36e6-b367-d4508c2485ea")
    @TableField(value = "tender_unit_code")
    private String tenderUnitCode;

    @AutoGenerated(locked = true, uuid = "af460400-9620-36ef-983c-f7976fb75cdf")
    @TableField(value = "updated_at")
    private Date updatedAt;

    @AutoGenerated(locked = true, uuid = "ef05041b-2b74-373c-ba98-823c93ddf4f6")
    @TableField(value = "updated_by")
    private String updatedBy;

    @AutoGenerated(locked = true, uuid = "10384911-fcb6-3c31-bec7-0fd2473e50f2")
    @TableField(value = "use_staff_id")
    private String useStaffId;

    @AutoGenerated(locked = true, uuid = "67b97fa5-a532-3826-b9b9-8faae9fdb59c")
    @TableField(value = "use_status")
    private String useStatus;
}
