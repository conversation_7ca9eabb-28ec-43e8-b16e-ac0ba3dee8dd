package com.pulse.file.entrance.web.query.assembler;

import com.pulse.file.entrance.web.vo.FileMetadataBaseVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** FileMetadataBaseVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "145b1190-650b-3eb3-9e40-0bc6323421f3")
public class FileMetadataBaseVoDataAssembler {

    /** 组装FileMetadataBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "6036bfd8-4245-39de-8ed5-ecb77d559601")
    public void assembleData(Map<String, FileMetadataBaseVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装FileMetadataBaseVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "af6b737e-0c0c-379b-9436-a1addbc1a2ba")
    public void assembleDataCustomized(List<FileMetadataBaseVo> dataList) {
        // 自定义数据组装

    }
}
