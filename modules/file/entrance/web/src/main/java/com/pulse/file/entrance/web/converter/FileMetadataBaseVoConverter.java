package com.pulse.file.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.file.entrance.web.query.assembler.FileMetadataBaseVoDataAssembler;
import com.pulse.file.entrance.web.vo.FileMetadataBaseVo;
import com.pulse.file.manager.dto.FileMetadataBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到FileMetadataBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "952bc4bb-f9a3-4609-8cdd-916761499c83|VO|CONVERTER")
public class FileMetadataBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private FileMetadataBaseVoDataAssembler fileMetadataBaseVoDataAssembler;

    /** 把FileMetadataBaseDto转换成FileMetadataBaseVo */
    @AutoGenerated(locked = true, uuid = "76fbfc83-25c3-3d9c-bc01-46c86dca8658")
    public FileMetadataBaseVo convertToFileMetadataBaseVo(FileMetadataBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToFileMetadataBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把FileMetadataBaseDto转换成FileMetadataBaseVo */
    @AutoGenerated(locked = false, uuid = "952bc4bb-f9a3-4609-8cdd-916761499c83-converter-Map")
    public Map<FileMetadataBaseDto, FileMetadataBaseVo> convertToFileMetadataBaseVoMap(
            List<FileMetadataBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<FileMetadataBaseDto, FileMetadataBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            FileMetadataBaseVo vo = new FileMetadataBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setName(dto.getName());
                                            vo.setPath(dto.getPath());
                                            vo.setFileSize(dto.getFileSize());
                                            vo.setFileType(dto.getFileType());
                                            vo.setUploadBy(dto.getUploadBy());
                                            vo.setBusinessId(dto.getBusinessId());
                                            vo.setBusinessType(dto.getBusinessType());
                                            vo.setStorageType(dto.getStorageType());
                                            vo.setEncrypted(dto.getEncrypted());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把FileMetadataBaseDto转换成FileMetadataBaseVo */
    @AutoGenerated(locked = true, uuid = "952bc4bb-f9a3-4609-8cdd-916761499c83-converter-list")
    public List<FileMetadataBaseVo> convertToFileMetadataBaseVoList(
            List<FileMetadataBaseDto> dtoList) {
        return new ArrayList<>(convertToFileMetadataBaseVoMap(dtoList).values());
    }

    /** 使用默认方式组装FileMetadataBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "d371e55d-ab5a-3ac5-b84a-faf0cdc6c75b")
    public FileMetadataBaseVo convertAndAssembleData(FileMetadataBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装FileMetadataBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "eda20e24-6a90-3e9f-a706-ed284e6299fd")
    public List<FileMetadataBaseVo> convertAndAssembleDataList(List<FileMetadataBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, FileMetadataBaseVo> voMap =
                convertToFileMetadataBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        fileMetadataBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
