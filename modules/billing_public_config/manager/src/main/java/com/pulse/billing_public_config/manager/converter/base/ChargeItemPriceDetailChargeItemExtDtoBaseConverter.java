package com.pulse.billing_public_config.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.billing_public_config.manager.ChargeItemPriceDetailBaseDtoManager;
import com.pulse.billing_public_config.manager.dto.ChargeItemPriceDetailBaseDto;
import com.pulse.billing_public_config.manager.dto.ChargeItemPriceDetailChargeItemExtDto;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@AutoGenerated(locked = false, uuid = "ef1d49f8-2abe-4b3c-a892-e9ae1098df7f|DTO|BASE_CONVERTER")
public class ChargeItemPriceDetailChargeItemExtDtoBaseConverter {
    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemPriceDetailBaseDtoManager chargeItemPriceDetailBaseDtoManager;

    @AutoGenerated(locked = true)
    public ChargeItemPriceDetailChargeItemExtDto
            convertFromChargeItemPriceDetailBaseDtoToChargeItemPriceDetailChargeItemExtDto(
                    ChargeItemPriceDetailBaseDto chargeItemPriceDetailBaseDto) {
        return convertFromChargeItemPriceDetailBaseDtoToChargeItemPriceDetailChargeItemExtDto(
                        List.of(chargeItemPriceDetailBaseDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = false)
    public List<ChargeItemPriceDetailChargeItemExtDto>
            convertFromChargeItemPriceDetailBaseDtoToChargeItemPriceDetailChargeItemExtDto(
                    List<ChargeItemPriceDetailBaseDto> chargeItemPriceDetailBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(chargeItemPriceDetailBaseDtoList)) {
            return new ArrayList<>();
        }
        List<ChargeItemPriceDetailChargeItemExtDto> chargeItemPriceDetailChargeItemExtDtoList =
                new ArrayList<>();
        for (ChargeItemPriceDetailBaseDto chargeItemPriceDetailBaseDto :
                chargeItemPriceDetailBaseDtoList) {
            if (chargeItemPriceDetailBaseDto == null) {
                continue;
            }
            ChargeItemPriceDetailChargeItemExtDto chargeItemPriceDetailChargeItemExtDto =
                    new ChargeItemPriceDetailChargeItemExtDto();
            chargeItemPriceDetailChargeItemExtDto.setPerformFlag(
                    chargeItemPriceDetailBaseDto.getPerformFlag());
            chargeItemPriceDetailChargeItemExtDto.setPerformTime(
                    chargeItemPriceDetailBaseDto.getPerformTime());
            chargeItemPriceDetailChargeItemExtDto.setId(chargeItemPriceDetailBaseDto.getId());
            chargeItemPriceDetailChargeItemExtDto.setCampusId(
                    chargeItemPriceDetailBaseDto.getCampusId());
            chargeItemPriceDetailChargeItemExtDto.setPriceType(
                    chargeItemPriceDetailBaseDto.getPriceType());
            chargeItemPriceDetailChargeItemExtDto.setPrice(chargeItemPriceDetailBaseDto.getPrice());
            chargeItemPriceDetailChargeItemExtDto.setIncreaseRatio(
                    chargeItemPriceDetailBaseDto.getIncreaseRatio());
            chargeItemPriceDetailChargeItemExtDto.setStartTime(
                    chargeItemPriceDetailBaseDto.getStartTime());
            chargeItemPriceDetailChargeItemExtDto.setEndTime(
                    chargeItemPriceDetailBaseDto.getEndTime());
            chargeItemPriceDetailChargeItemExtDto.setCreatedBy(
                    chargeItemPriceDetailBaseDto.getCreatedBy());
            chargeItemPriceDetailChargeItemExtDto.setUpdateBy(
                    chargeItemPriceDetailBaseDto.getUpdateBy());
            chargeItemPriceDetailChargeItemExtDto.setAuditStatus(
                    chargeItemPriceDetailBaseDto.getAuditStatus());
            chargeItemPriceDetailChargeItemExtDto.setAuditOperatorId(
                    chargeItemPriceDetailBaseDto.getAuditOperatorId());
            chargeItemPriceDetailChargeItemExtDto.setCreatedAt(
                    chargeItemPriceDetailBaseDto.getCreatedAt());
            chargeItemPriceDetailChargeItemExtDto.setUpdatedAt(
                    chargeItemPriceDetailBaseDto.getUpdatedAt());
            chargeItemPriceDetailChargeItemExtDto.setAuditTime(
                    chargeItemPriceDetailBaseDto.getAuditTime());
            chargeItemPriceDetailChargeItemExtDto.setAuditInstruction(
                    chargeItemPriceDetailBaseDto.getAuditInstruction());
            chargeItemPriceDetailChargeItemExtDto.setBatchNumber(
                    chargeItemPriceDetailBaseDto.getBatchNumber());
            chargeItemPriceDetailChargeItemExtDto.setReason(
                    chargeItemPriceDetailBaseDto.getReason());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            chargeItemPriceDetailChargeItemExtDtoList.add(chargeItemPriceDetailChargeItemExtDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return chargeItemPriceDetailChargeItemExtDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    @AutoGenerated(locked = true)
    public ChargeItemPriceDetailBaseDto
            convertFromChargeItemPriceDetailChargeItemExtDtoToChargeItemPriceDetailBaseDto(
                    ChargeItemPriceDetailChargeItemExtDto chargeItemPriceDetailChargeItemExtDto) {
        return convertFromChargeItemPriceDetailChargeItemExtDtoToChargeItemPriceDetailBaseDto(
                        List.of(chargeItemPriceDetailChargeItemExtDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<ChargeItemPriceDetailBaseDto>
            convertFromChargeItemPriceDetailChargeItemExtDtoToChargeItemPriceDetailBaseDto(
                    List<ChargeItemPriceDetailChargeItemExtDto>
                            chargeItemPriceDetailChargeItemExtDtoList) {
        if (CollectionUtil.isEmpty(chargeItemPriceDetailChargeItemExtDtoList)) {
            return new ArrayList<>();
        }
        return chargeItemPriceDetailBaseDtoManager.getByIds(
                chargeItemPriceDetailChargeItemExtDtoList.stream()
                        .map(ChargeItemPriceDetailChargeItemExtDto::getId)
                        .collect(Collectors.toList()));
    }
}
