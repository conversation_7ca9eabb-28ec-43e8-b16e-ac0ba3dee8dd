package com.pulse.billing_public_config.manager.dto;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "0a7efebd-e3f7-4868-91d8-ec379eed53d3|DTO|DEFINITION")
public class ReckoningItemCategoryBaseDto {
    /** 核算项目分类id */
    @AutoGenerated(locked = true, uuid = "86606881-d017-41da-a210-d6dad499586a")
    private String categoryId;

    /** 类别名称 */
    @AutoGenerated(locked = true, uuid = "95bf2253-8391-420b-b875-a128fa605c72")
    private String categoryName;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "b568abed-7944-45ab-8e34-6cb6c3cc3430")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "498e2721-5696-4edd-9e38-3e101ffa7cfc")
    private String createdBy;

    /** 电子发票住院编码 */
    @AutoGenerated(locked = true, uuid = "6938ca1d-f148-406c-a07f-6a4dbe6338fd")
    private String electronicReceiptInpCode;

    /** 电子发票项目名称 */
    @AutoGenerated(locked = true, uuid = "c80b5161-eef2-4370-ba0f-b1d9bef01ff5")
    private String electronicReceiptItemName;

    /** 电子发票门诊编码 */
    @AutoGenerated(locked = true, uuid = "b93643ad-e814-429e-b585-cd5111e77d63")
    private String electronicReceiptOutpCode;

    /** 启用标志 */
    @AutoGenerated(locked = true, uuid = "42ae0303-842a-4583-827e-2524e3d952aa")
    private Boolean enableFlag;

    /** 住院收据项目 */
    @AutoGenerated(locked = true, uuid = "9ff06c1c-7844-429d-8309-4fa43e346946")
    private String inpReceiptItemCatalog;

    /** 输入代码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "84235bf9-eb0c-4dad-b67d-873c5e19cc40")
    private InputCodeEo inputCode;

    /** 乐观锁字段 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "b0b160ee-d144-4c12-aa3c-a01fab8cef26")
    private Long lockVersion;

    /** 病案首页费用排除标志 */
    @AutoGenerated(locked = true, uuid = "97826d8d-15ee-4bba-9d66-da0646d7106a")
    private Boolean medicalRecordFeeExcludeFlag;

    /** 门诊收据项目 */
    @AutoGenerated(locked = true, uuid = "f0f06539-4b28-4c29-8dea-41aca94de058")
    private String outpReceiptItemCatalog;

    /** 父分类ID */
    @AutoGenerated(locked = true, uuid = "ba4f2bc3-bc31-438d-9443-b2e7163c8022")
    private String parentCategoryId;

    /** 核算项目 */
    @AutoGenerated(locked = true, uuid = "ba1d19b6-cbcc-4b77-ab77-1e16cd9e78c4")
    private String reckoningItemCatalog;

    /** 无需审核退款标志 */
    @AutoGenerated(locked = true, uuid = "03280d64-25b5-4382-8dca-8e5da28675d6")
    private Boolean refundNoNeedAuditFlag;

    /** 排序编号 */
    @AutoGenerated(locked = true, uuid = "d1bc97e8-8dcf-418e-8794-bac27e159465")
    private Long sortNumber;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "68760564-22e2-48db-8d49-3e6adaadfa1e")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "a696aa5d-082e-4cf3-9fbe-3694e6f271d9")
    private String updatedBy;
}
