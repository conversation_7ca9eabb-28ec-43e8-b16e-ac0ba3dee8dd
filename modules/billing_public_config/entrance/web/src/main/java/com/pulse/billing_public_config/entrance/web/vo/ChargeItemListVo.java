package com.pulse.billing_public_config.entrance.web.vo;

import com.pulse.billing_public_config.common.enums.ChargePackageTypeEnum;
import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "bc0477ae-094a-4884-a11c-22e5a5dc0432|VO|DEFINITION")
public class ChargeItemListVo {
    /** 院区ID列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "a0ab4bf1-1d06-45b1-8abf-80cac41d7057")
    private List<String> campusIdList;

    /** 收费项目目录 */
    @AutoGenerated(locked = true, uuid = "15caf8ef-4a30-4870-bafc-ca0a89c1f5f1")
    private String chargeItemCategory;

    /** 项目代码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "8a040333-2aa2-4f22-9125-63c23a916f52")
    private List<ChargeItemPriceDetailBaseVo> chargeItemPriceDetailList;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "eb555679-398e-44dd-88ce-6aa228e71f14")
    private Date createdAt;

    /** 启用标志 */
    @AutoGenerated(locked = true, uuid = "ebe1ef3f-5a6c-415d-b63e-b31458bcf63f")
    private Boolean enableFlag;

    /** 输入代码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "f8ee52ed-2bf6-45fb-b79e-fe4801122e2d")
    private InputCodeEo inputCode;

    /** 医保项目等级 */
    @AutoGenerated(locked = true, uuid = "591a172a-104a-494a-8a46-5864a5c0e43a")
    private String insuranceItemLevel;

    /** 医保国家代码 */
    @AutoGenerated(locked = true, uuid = "7dcae01d-a272-47ac-b073-b03e8d58e12f")
    private String insuranceNationCode;

    /** 医保国家名称 */
    @AutoGenerated(locked = true, uuid = "d60e2533-062e-45e9-ac6c-00f1e2f7dfbb")
    private String insuranceNationName;

    /** 医保省码 */
    @AutoGenerated(locked = true, uuid = "dc682193-9502-4998-8fd7-ac084fab510c")
    private String insuranceProvinceCode;

    /** 项目分类 */
    @AutoGenerated(locked = true, uuid = "981225e5-**************-381236a39c40")
    private String itemCategory;

    /** 项目代码 */
    @AutoGenerated(locked = true, uuid = "e7204b73-cd50-4fda-9480-5386b6969a51")
    private String itemCode;

    /** 项目名称 */
    @AutoGenerated(locked = true, uuid = "3804c3fe-3b56-4644-a659-53beeb9d302f")
    private String itemName;

    /** 项目规格 */
    @AutoGenerated(locked = true, uuid = "60e8c1bb-bbf1-4f62-93b5-19125c8b983d")
    private String itemSpecification;

    /** 叶子标志 */
    @AutoGenerated(locked = true, uuid = "ebaa0c9e-1443-4130-a2e4-b422a574953b")
    private Boolean leafFlag;

    /** 组织ID */
    @AutoGenerated(locked = true, uuid = "13e09ee1-9a34-4292-9cb9-13cc12bbe169")
    private String organizationId;

    /** 套餐标识 */
    @AutoGenerated(locked = true, uuid = "5b710ba9-31d2-4c59-a0ef-d5bd2c33e98d")
    private ChargePackageTypeEnum packageType;

    /** 单价 */
    @AutoGenerated(locked = true, uuid = "db8174fc-54d5-463e-9661-b203b4ca2033")
    private BigDecimal price;

    /** 排序编号 */
    @AutoGenerated(locked = true, uuid = "e2a1da41-8a09-4847-a7d3-561628aff108")
    private Long sortNumber;

    /** 是否同步过诊疗项目标记 */
    @AutoGenerated(locked = true, uuid = "c1ba68e9-0b15-4f66-9711-f37e25c90ace")
    private Boolean synchronizedFlag;

    /** 单位 */
    @AutoGenerated(locked = true, uuid = "3da97481-72a0-47e2-aeb9-81ff0b8f1467")
    private String unit;

    /** 使用范围列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "b1558cf2-49a2-4dbd-9ddc-1a16e364ab9b")
    private List<String> useScopeList;
}
