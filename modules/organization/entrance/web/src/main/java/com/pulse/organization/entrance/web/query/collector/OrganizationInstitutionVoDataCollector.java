package com.pulse.organization.entrance.web.query.collector;

import com.pulse.organization.entrance.web.converter.InstitutionBaseVoConverter;
import com.pulse.organization.entrance.web.converter.OrganizationInstitutionVoConverter;
import com.pulse.organization.entrance.web.query.assembler.OrganizationInstitutionVoDataAssembler.OrganizationInstitutionVoDataHolder;
import com.pulse.organization.entrance.web.vo.InstitutionBaseVo;
import com.pulse.organization.manager.dto.InstitutionBaseDto;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.OrganizationInstitutionDto;
import com.pulse.organization.service.InstitutionBaseDtoService;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装OrganizationInstitutionVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "de81ba23-72dc-374f-9d62-4add0fa924be")
public class OrganizationInstitutionVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private InstitutionBaseDtoService institutionBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private InstitutionBaseVoConverter institutionBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationInstitutionVoConverter organizationInstitutionVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationInstitutionVoDataCollector organizationInstitutionVoDataCollector;

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "32c9a108-964a-36b8-ac45-be024682b36d")
    public void collectDataDefault(OrganizationInstitutionVoDataHolder dataHolder) {
        organizationInstitutionVoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 获取OrganizationInstitutionDto数据填充OrganizationInstitutionVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "91dc2953-7c61-334e-9438-133eb707cd9d")
    public void collectDataWithDtoData(
            List<OrganizationInstitutionDto> dtoList,
            OrganizationInstitutionVoDataHolder dataHolder) {
        List<InstitutionBaseDto> institutionList = new ArrayList<>();

        for (OrganizationInstitutionDto rootDto : dtoList) {
            InstitutionBaseDto institutionDto = rootDto.getInstitution();
            if (institutionDto != null) {
                institutionList.add(institutionDto);
            }
        }

        // access institution
        Map<InstitutionBaseDto, InstitutionBaseVo> institutionVoMap =
                institutionBaseVoConverter.convertToInstitutionBaseVoMap(institutionList);
        dataHolder.institution =
                institutionList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> institutionVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "f3d8e3af-216c-33b0-bb7b-ee043c3ce7fd")
    private void fillDataWhenNecessary(OrganizationInstitutionVoDataHolder dataHolder) {
        List<OrganizationBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.institution == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(OrganizationBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<InstitutionBaseDto> baseDtoList =
                    institutionBaseDtoService.getByOrganizationIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(InstitutionBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<InstitutionBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(InstitutionBaseDto::getOrganizationId));
            Map<InstitutionBaseDto, InstitutionBaseVo> dtoVoMap =
                    institutionBaseVoConverter.convertToInstitutionBaseVoMap(baseDtoList);
            Map<InstitutionBaseDto, InstitutionBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.institution =
                    rootDtoList.stream()
                            .map(OrganizationBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }
}
