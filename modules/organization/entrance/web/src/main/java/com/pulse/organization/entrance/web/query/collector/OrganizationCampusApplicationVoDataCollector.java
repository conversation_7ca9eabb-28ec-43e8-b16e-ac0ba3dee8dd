package com.pulse.organization.entrance.web.query.collector;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.application.manager.dto.ApplicationBaseDto;
import com.pulse.application.manager.dto.ApplicationInfoOrganizationDto;
import com.pulse.application.manager.dto.ApplicationOrganizationDto;
import com.pulse.organization.entrance.web.converter.ApplicationOrganizationCampusVoConverter;
import com.pulse.organization.entrance.web.converter.OrganizationCampusApplicationVoConverter;
import com.pulse.organization.entrance.web.query.assembler.OrganizationCampusApplicationVoDataAssembler.OrganizationCampusApplicationVoDataHolder;
import com.pulse.organization.entrance.web.vo.ApplicationOrganizationCampusVo;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.OrganizationCampusApplicationDto;
import com.pulse.organization.manager.facade.application.ApplicationBaseDtoServiceInOrganizationRpcAdapter;
import com.pulse.organization.manager.facade.application.ApplicationInfoOrganizationDtoServiceInOrganizationRpcAdapter;
import com.pulse.organization.manager.facade.application.ApplicationOrganizationDtoServiceInOrganizationRpcAdapter;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装OrganizationCampusApplicationVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "834dcd80-fc2e-37b1-b721-0ea12583b8c5")
public class OrganizationCampusApplicationVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private ApplicationBaseDtoServiceInOrganizationRpcAdapter
            applicationBaseDtoServiceInOrganizationRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationInfoOrganizationDtoServiceInOrganizationRpcAdapter
            applicationInfoOrganizationDtoServiceInOrganizationRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationOrganizationCampusVoConverter applicationOrganizationCampusVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationOrganizationDtoServiceInOrganizationRpcAdapter
            applicationOrganizationDtoServiceInOrganizationRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationCampusApplicationVoConverter organizationCampusApplicationVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationCampusApplicationVoDataCollector
            organizationCampusApplicationVoDataCollector;

    /** 获取OrganizationCampusApplicationDto数据填充OrganizationCampusApplicationVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "4ab2ddbe-cb51-3358-9554-30e1dad61d4b")
    public void collectDataWithDtoData(
            List<OrganizationCampusApplicationDto> dtoList,
            OrganizationCampusApplicationVoDataHolder dataHolder) {
        Map<ApplicationOrganizationDto, ApplicationInfoOrganizationDto>
                applicationInfoOrganizationListBaseDtoDtoMap = new LinkedHashMap<>();
        List<ApplicationBaseDto> applicationInfoOrganizationList2ApplicationList =
                new ArrayList<>();

        for (OrganizationCampusApplicationDto rootDto : dtoList) {
            if (CollectionUtil.isNotEmpty(rootDto.getApplicationInfoOrganizationList())) {
                for (ApplicationInfoOrganizationDto applicationInfoOrganizationListDto :
                        rootDto.getApplicationInfoOrganizationList()) {
                    ApplicationOrganizationDto applicationInfoOrganizationListBaseDto =
                            applicationOrganizationDtoServiceInOrganizationRpcAdapter
                                    .getByIds(List.of(applicationInfoOrganizationListDto.getId()))
                                    .stream()
                                    .findAny()
                                    .get();
                    applicationInfoOrganizationListBaseDto.setOrganizationId(rootDto.getId());
                    applicationInfoOrganizationListBaseDtoDtoMap.put(
                            applicationInfoOrganizationListBaseDto,
                            applicationInfoOrganizationListDto);
                    ApplicationBaseDto applicationInfoOrganizationList2ApplicationDto =
                            applicationInfoOrganizationListDto.getApplication();
                    if (applicationInfoOrganizationList2ApplicationDto != null) {
                        applicationInfoOrganizationList2ApplicationList.add(
                                applicationInfoOrganizationList2ApplicationDto);
                    }
                }
            }
        }

        // access applicationInfoOrganizationList
        Map<ApplicationInfoOrganizationDto, ApplicationOrganizationCampusVo>
                applicationInfoOrganizationListVoMap =
                        applicationOrganizationCampusVoConverter
                                .convertToApplicationOrganizationCampusVoMap(
                                        new ArrayList<>(
                                                applicationInfoOrganizationListBaseDtoDtoMap
                                                        .values()));
        dataHolder.applicationInfoOrganizationList =
                applicationInfoOrganizationListBaseDtoDtoMap.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                applicationInfoOrganizationListVoMap.get(
                                                        applicationInfoOrganizationListBaseDtoDtoMap
                                                                .get(baseDto)),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access applicationInfoOrganizationList2Application
        Map<ApplicationBaseDto, ApplicationOrganizationCampusVo.ApplicationBaseVo>
                applicationInfoOrganizationList2ApplicationVoMap =
                        applicationOrganizationCampusVoConverter.convertToApplicationBaseVoMap(
                                applicationInfoOrganizationList2ApplicationList);
        dataHolder.applicationInfoOrganizationList2Application =
                applicationInfoOrganizationList2ApplicationList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                applicationInfoOrganizationList2ApplicationVoMap
                                                        .get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "c9c57a0e-da20-3187-aefd-97d7eda7a991")
    public void collectDataDefault(OrganizationCampusApplicationVoDataHolder dataHolder) {
        organizationCampusApplicationVoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "ca57612a-1914-3f05-8cd5-b658c557b419")
    private void fillDataWhenNecessary(OrganizationCampusApplicationVoDataHolder dataHolder) {
        List<OrganizationBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.applicationInfoOrganizationList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(OrganizationBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ApplicationOrganizationDto> baseDtoList =
                    applicationOrganizationDtoServiceInOrganizationRpcAdapter
                            .getByOrganizationIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(ApplicationOrganizationDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<ApplicationOrganizationDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            ApplicationOrganizationDto::getOrganizationId));
            Map<String, ApplicationInfoOrganizationDto> applicationInfoOrganizationDtoMap =
                    applicationInfoOrganizationDtoServiceInOrganizationRpcAdapter
                            .getByOrganizationIds(
                                    baseDtoList.stream()
                                            .map(ApplicationOrganizationDto::getOrganizationId)
                                            .collect(Collectors.toList()))
                            .stream()
                            .collect(
                                    Collectors.toMap(
                                            ApplicationInfoOrganizationDto::getId,
                                            Function.identity()));
            Map<ApplicationInfoOrganizationDto, ApplicationOrganizationCampusVo> dtoVoMap =
                    applicationOrganizationCampusVoConverter
                            .convertToApplicationOrganizationCampusVoMap(
                                    new ArrayList<>(applicationInfoOrganizationDtoMap.values()));
            Map<ApplicationOrganizationDto, ApplicationOrganizationCampusVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .filter(
                                    baseDto ->
                                            applicationInfoOrganizationDtoMap.containsKey(
                                                    baseDto.getId()))
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    dtoVoMap.get(
                                                            applicationInfoOrganizationDtoMap.get(
                                                                    baseDto.getId())),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.applicationInfoOrganizationList =
                    rootDtoList.stream()
                            .map(OrganizationBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.applicationInfoOrganizationList2Application == null) {
            Set<String> ids =
                    dataHolder.applicationInfoOrganizationList.keySet().stream()
                            .map(ApplicationOrganizationDto::getApplicationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ApplicationBaseDto> baseDtoList =
                    applicationBaseDtoServiceInOrganizationRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(ApplicationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, ApplicationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            ApplicationBaseDto::getId, Function.identity()));
            Map<ApplicationBaseDto, ApplicationOrganizationCampusVo.ApplicationBaseVo> dtoVoMap =
                    applicationOrganizationCampusVoConverter.convertToApplicationBaseVoMap(
                            baseDtoList);
            Map<ApplicationBaseDto, ApplicationOrganizationCampusVo.ApplicationBaseVo>
                    baseDtoVoMap =
                            baseDtoList.stream()
                                    .collect(
                                            Collectors.toMap(
                                                    Function.identity(),
                                                    baseDto -> dtoVoMap.get(baseDto),
                                                    (o1, o2) -> o1,
                                                    LinkedHashMap::new));
            dataHolder.applicationInfoOrganizationList2Application =
                    dataHolder.applicationInfoOrganizationList.keySet().stream()
                            .map(ApplicationOrganizationDto::getApplicationId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }
}
