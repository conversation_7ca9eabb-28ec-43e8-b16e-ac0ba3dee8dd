package com.pulse.organization.entrance.web.query.assembler;

import com.pulse.organization.entrance.web.vo.OrganizationRefUserBaseVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** OrganizationRefUserBaseVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "b5b09430-e0fc-3e5b-bea0-9343e0e79914")
public class OrganizationRefUserBaseVoDataAssembler {

    /** 组装OrganizationRefUserBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "d0ca12b3-e994-3e1f-b868-dba0a501a898")
    public void assembleData(Map<String, OrganizationRefUserBaseVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装OrganizationRefUserBaseVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "d7a88cab-3cbf-328e-ac01-9f9976fb54be")
    public void assembleDataCustomized(List<OrganizationRefUserBaseVo> dataList) {
        // 自定义数据组装

    }
}
