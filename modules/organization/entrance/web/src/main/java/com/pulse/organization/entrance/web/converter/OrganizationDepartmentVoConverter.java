package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.entrance.web.query.assembler.OrganizationDepartmentVoDataAssembler;
import com.pulse.organization.entrance.web.query.assembler.OrganizationDepartmentVoDataAssembler.OrganizationDepartmentVoDataHolder;
import com.pulse.organization.entrance.web.query.collector.OrganizationDepartmentVoDataCollector;
import com.pulse.organization.entrance.web.vo.DepartmentVo;
import com.pulse.organization.entrance.web.vo.OrganizationDepartmentVo;
import com.pulse.organization.manager.dto.DepartmentDto;
import com.pulse.organization.manager.dto.OrganizationDepartmentDto;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到OrganizationDepartmentVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "1df1fec6-a0d1-4eea-b344-af2b15ef9144|VO|CONVERTER")
public class OrganizationDepartmentVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private DepartmentVoConverter departmentVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationDepartmentVoDataAssembler organizationDepartmentVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationDepartmentVoDataCollector organizationDepartmentVoDataCollector;

    /** 把OrganizationDepartmentDto转换成OrganizationDepartmentVo */
    @AutoGenerated(locked = false, uuid = "1df1fec6-a0d1-4eea-b344-af2b15ef9144-converter-Map")
    public Map<OrganizationDepartmentDto, OrganizationDepartmentVo>
            convertToOrganizationDepartmentVoMap(List<OrganizationDepartmentDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<DepartmentDto, DepartmentVo> departmentMap =
                departmentVoConverter.convertToDepartmentVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(OrganizationDepartmentDto::getDepartment)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<OrganizationDepartmentDto, OrganizationDepartmentVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            OrganizationDepartmentVo vo =
                                                    new OrganizationDepartmentVo();
                                            vo.setId(dto.getId());
                                            vo.setParentId(dto.getParentId());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setDescription(dto.getDescription());
                                            vo.setName(dto.getName());
                                            vo.setOrganizationLevel(dto.getOrganizationLevel());
                                            vo.setSortNumber(dto.getSortNumber());
                                            vo.setStatus(dto.getStatus());
                                            vo.setType(dto.getType());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setAddress(dto.getAddress());
                                            vo.setContactPerson(dto.getContactPerson());
                                            vo.setInputCode(dto.getInputCode());
                                            vo.setContactNumber(dto.getContactNumber());
                                            vo.setAbbreviation(dto.getAbbreviation());
                                            vo.setDeletedBy(dto.getDeletedBy());
                                            vo.setAlias(dto.getAlias());
                                            vo.setEnglishName(dto.getEnglishName());
                                            vo.setDepartment(
                                                    dto.getDepartment() == null
                                                            ? null
                                                            : departmentMap.get(
                                                                    dto.getDepartment()));
                                            vo.setInvalidFlag(dto.getInvalidFlag());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把OrganizationDepartmentDto转换成OrganizationDepartmentVo */
    @AutoGenerated(locked = true, uuid = "1df1fec6-a0d1-4eea-b344-af2b15ef9144-converter-list")
    public List<OrganizationDepartmentVo> convertToOrganizationDepartmentVoList(
            List<OrganizationDepartmentDto> dtoList) {
        return new ArrayList<>(convertToOrganizationDepartmentVoMap(dtoList).values());
    }

    /** 使用默认方式组装OrganizationDepartmentVo列表数据 */
    @AutoGenerated(locked = true, uuid = "3088257f-f049-3191-987c-6dd2e346536f")
    public List<OrganizationDepartmentVo> convertAndAssembleDataList(
            List<OrganizationDepartmentDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        OrganizationDepartmentVoDataHolder dataHolder = new OrganizationDepartmentVoDataHolder();
        dataHolder.setRootBaseDtoList(
                organizationBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(OrganizationDepartmentDto::getId)
                                .collect(Collectors.toList())));
        Map<String, OrganizationDepartmentVo> voMap =
                convertToOrganizationDepartmentVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        organizationDepartmentVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        organizationDepartmentVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 使用默认方式组装OrganizationDepartmentVo数据 */
    @AutoGenerated(locked = true, uuid = "47b630f8-997c-3e45-8603-2c0de1fd2887")
    public OrganizationDepartmentVo convertAndAssembleData(OrganizationDepartmentDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把OrganizationDepartmentDto转换成OrganizationDepartmentVo */
    @AutoGenerated(locked = true, uuid = "b42875b0-f157-3f6c-8d45-24e95df8404d")
    public OrganizationDepartmentVo convertToOrganizationDepartmentVo(
            OrganizationDepartmentDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToOrganizationDepartmentVoList(List.of(dto)).stream().findAny().orElse(null);
    }
}
