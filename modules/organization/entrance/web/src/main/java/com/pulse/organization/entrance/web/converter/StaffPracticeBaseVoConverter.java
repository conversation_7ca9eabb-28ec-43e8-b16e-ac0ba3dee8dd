package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.entrance.web.query.assembler.StaffPracticeBaseVoDataAssembler;
import com.pulse.organization.entrance.web.vo.StaffPracticeBaseVo;
import com.pulse.organization.manager.dto.StaffPracticeBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到StaffPracticeBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "23450689-f645-4522-9c40-f30e5f60eaf0|VO|CONVERTER")
public class StaffPracticeBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private StaffPracticeBaseVoDataAssembler staffPracticeBaseVoDataAssembler;

    /** 把StaffPracticeBaseDto转换成StaffPracticeBaseVo */
    @AutoGenerated(locked = false, uuid = "23450689-f645-4522-9c40-f30e5f60eaf0-converter-Map")
    public Map<StaffPracticeBaseDto, StaffPracticeBaseVo> convertToStaffPracticeBaseVoMap(
            List<StaffPracticeBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<StaffPracticeBaseDto, StaffPracticeBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            StaffPracticeBaseVo vo = new StaffPracticeBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setQualificationCertificateNumber(
                                                    dto.getQualificationCertificateNumber());
                                            vo.setPracticeCertificateNumber(
                                                    dto.getPracticeCertificateNumber());
                                            vo.setStartDate(dto.getStartDate());
                                            vo.setEndDate(dto.getEndDate());
                                            vo.setCategory(dto.getCategory());
                                            vo.setPracticeScope(dto.getPracticeScope());
                                            vo.setTechnicalTitle(dto.getTechnicalTitle());
                                            vo.setPracticeDepartment(dto.getPracticeDepartment());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setStaffId(dto.getStaffId());
                                            vo.setDoctorInsuranceId(dto.getDoctorInsuranceId());
                                            vo.setAnesthesiologistNumber(
                                                    dto.getAnesthesiologistNumber());
                                            vo.setRadiologistNumber(dto.getRadiologistNumber());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把StaffPracticeBaseDto转换成StaffPracticeBaseVo */
    @AutoGenerated(locked = true, uuid = "23450689-f645-4522-9c40-f30e5f60eaf0-converter-list")
    public List<StaffPracticeBaseVo> convertToStaffPracticeBaseVoList(
            List<StaffPracticeBaseDto> dtoList) {
        return new ArrayList<>(convertToStaffPracticeBaseVoMap(dtoList).values());
    }

    /** 把StaffPracticeBaseDto转换成StaffPracticeBaseVo */
    @AutoGenerated(locked = true, uuid = "7530ee05-f32e-3851-91d3-5bb1a69328d8")
    public StaffPracticeBaseVo convertToStaffPracticeBaseVo(StaffPracticeBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToStaffPracticeBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装StaffPracticeBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "d70c6144-7722-3571-b091-e3fa1bc802ec")
    public List<StaffPracticeBaseVo> convertAndAssembleDataList(
            List<StaffPracticeBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, StaffPracticeBaseVo> voMap =
                convertToStaffPracticeBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        staffPracticeBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 使用默认方式组装StaffPracticeBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "e9b62d58-6323-3bc9-bf3f-54c63500fe3e")
    public StaffPracticeBaseVo convertAndAssembleData(StaffPracticeBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }
}
