package com.pulse.organization.entrance.web.query.assembler;

import com.pulse.organization.entrance.web.vo.OrganizationSimpleVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** OrganizationSimpleVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "fd10d75a-8384-3379-b947-0beb6d514bc3")
public class OrganizationSimpleVoDataAssembler {

    /** 组装OrganizationSimpleVo数据 */
    @AutoGenerated(locked = true, uuid = "5c5e5439-b5f9-34e9-a4ce-9dad9ad8e371")
    public void assembleData(Map<String, OrganizationSimpleVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装OrganizationSimpleVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "f6119209-407b-3af1-8bc8-1225ac05e71b")
    public void assembleDataCustomized(List<OrganizationSimpleVo> dataList) {
        // 自定义数据组装

    }
}
