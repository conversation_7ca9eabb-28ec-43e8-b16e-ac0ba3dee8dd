package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.entrance.web.query.assembler.TeamWithMemberSimpleVoDataAssembler;
import com.pulse.organization.entrance.web.query.assembler.TeamWithMemberSimpleVoDataAssembler.TeamWithMemberSimpleVoDataHolder;
import com.pulse.organization.entrance.web.query.collector.TeamWithMemberSimpleVoDataCollector;
import com.pulse.organization.entrance.web.vo.TeamMemberSimpleVo;
import com.pulse.organization.entrance.web.vo.TeamWithMemberSimpleVo;
import com.pulse.organization.manager.dto.TeamMemberBaseDto;
import com.pulse.organization.manager.dto.TeamWithMemberDto;
import com.pulse.organization.service.TeamBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到TeamWithMemberSimpleVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "4a1f99f5-d67a-44c9-a865-48280e5f2164|VO|CONVERTER")
public class TeamWithMemberSimpleVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private TeamBaseDtoService teamBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TeamMemberSimpleVoConverter teamMemberSimpleVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TeamWithMemberSimpleVoDataAssembler teamWithMemberSimpleVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private TeamWithMemberSimpleVoDataCollector teamWithMemberSimpleVoDataCollector;

    /** 把TeamWithMemberDto转换成TeamWithMemberSimpleVo */
    @AutoGenerated(locked = true, uuid = "1be994c5-8fce-38a9-b00f-bf5e6d1911ab")
    public TeamWithMemberSimpleVo convertToTeamWithMemberSimpleVo(TeamWithMemberDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToTeamWithMemberSimpleVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装TeamWithMemberSimpleVo列表数据 */
    @AutoGenerated(locked = true, uuid = "2c039ed9-cc36-3854-b634-960adf14ee1c")
    public List<TeamWithMemberSimpleVo> convertAndAssembleDataList(
            List<TeamWithMemberDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        TeamWithMemberSimpleVoDataHolder dataHolder = new TeamWithMemberSimpleVoDataHolder();
        dataHolder.setRootBaseDtoList(
                teamBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(TeamWithMemberDto::getId)
                                .collect(Collectors.toList())));
        Map<String, TeamWithMemberSimpleVo> voMap =
                convertToTeamWithMemberSimpleVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        teamWithMemberSimpleVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        teamWithMemberSimpleVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把TeamWithMemberDto转换成TeamWithMemberSimpleVo */
    @AutoGenerated(locked = false, uuid = "4a1f99f5-d67a-44c9-a865-48280e5f2164-converter-Map")
    public Map<TeamWithMemberDto, TeamWithMemberSimpleVo> convertToTeamWithMemberSimpleVoMap(
            List<TeamWithMemberDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<TeamMemberBaseDto, TeamMemberSimpleVo> teamMemberListMap =
                teamMemberSimpleVoConverter.convertToTeamMemberSimpleVoMap(
                        dtoList.stream()
                                .filter(dto -> CollectionUtil.isNotEmpty(dto.getTeamMemberList()))
                                .flatMap(dto -> dto.getTeamMemberList().stream())
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<TeamWithMemberDto, TeamWithMemberSimpleVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            TeamWithMemberSimpleVo vo =
                                                    new TeamWithMemberSimpleVo();
                                            vo.setId(dto.getId());
                                            vo.setLeadOrganizationId(dto.getLeadOrganizationId());
                                            vo.setName(dto.getName());
                                            vo.setType(dto.getType());
                                            vo.setSortNumber(dto.getSortNumber());
                                            vo.setTeamMemberList(
                                                    dto.getTeamMemberList() == null
                                                            ? null
                                                            : dto.getTeamMemberList().stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    teamMemberListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把TeamWithMemberDto转换成TeamWithMemberSimpleVo */
    @AutoGenerated(locked = true, uuid = "4a1f99f5-d67a-44c9-a865-48280e5f2164-converter-list")
    public List<TeamWithMemberSimpleVo> convertToTeamWithMemberSimpleVoList(
            List<TeamWithMemberDto> dtoList) {
        return new ArrayList<>(convertToTeamWithMemberSimpleVoMap(dtoList).values());
    }

    /** 使用默认方式组装TeamWithMemberSimpleVo数据 */
    @AutoGenerated(locked = true, uuid = "9ce9b695-cdf5-32f7-b9d8-8ffcb589fd96")
    public TeamWithMemberSimpleVo convertAndAssembleData(TeamWithMemberDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }
}
