package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.entrance.web.query.assembler.OrganizationWardSimpleVoDataAssembler;
import com.pulse.organization.entrance.web.query.assembler.OrganizationWardSimpleVoDataAssembler.OrganizationWardSimpleVoDataHolder;
import com.pulse.organization.entrance.web.query.collector.OrganizationWardSimpleVoDataCollector;
import com.pulse.organization.entrance.web.vo.OrganizationWardSimpleVo;
import com.pulse.organization.entrance.web.vo.WardSimpleBaseVo;
import com.pulse.organization.manager.dto.OrganizationWardDto;
import com.pulse.organization.manager.dto.WardBaseDto;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到OrganizationWardSimpleVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "adf17ef7-1121-464f-aec5-ce715abddfa5|VO|CONVERTER")
public class OrganizationWardSimpleVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationWardSimpleVoDataAssembler organizationWardSimpleVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationWardSimpleVoDataCollector organizationWardSimpleVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private WardSimpleBaseVoConverter wardSimpleBaseVoConverter;

    /** 把OrganizationWardDto转换成OrganizationWardSimpleVo */
    @AutoGenerated(locked = true, uuid = "3884dd54-e42c-3aaf-a5ad-3f16e98a37e3")
    public OrganizationWardSimpleVo convertToOrganizationWardSimpleVo(OrganizationWardDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToOrganizationWardSimpleVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装OrganizationWardSimpleVo列表数据 */
    @AutoGenerated(locked = true, uuid = "51faf5da-4297-39d3-8a36-48ffc33152ae")
    public List<OrganizationWardSimpleVo> convertAndAssembleDataList(
            List<OrganizationWardDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        OrganizationWardSimpleVoDataHolder dataHolder = new OrganizationWardSimpleVoDataHolder();
        dataHolder.setRootBaseDtoList(
                organizationBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(OrganizationWardDto::getId)
                                .collect(Collectors.toList())));
        Map<String, OrganizationWardSimpleVo> voMap =
                convertToOrganizationWardSimpleVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        organizationWardSimpleVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        organizationWardSimpleVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把OrganizationWardDto转换成OrganizationWardSimpleVo */
    @AutoGenerated(locked = false, uuid = "adf17ef7-1121-464f-aec5-ce715abddfa5-converter-Map")
    public Map<OrganizationWardDto, OrganizationWardSimpleVo> convertToOrganizationWardSimpleVoMap(
            List<OrganizationWardDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<WardBaseDto, WardSimpleBaseVo> wardMap =
                wardSimpleBaseVoConverter.convertToWardSimpleBaseVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(OrganizationWardDto::getWard)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<OrganizationWardDto, OrganizationWardSimpleVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            OrganizationWardSimpleVo vo =
                                                    new OrganizationWardSimpleVo();
                                            vo.setId(dto.getId());
                                            vo.setName(dto.getName());
                                            vo.setOrganizationLevel(dto.getOrganizationLevel());
                                            vo.setSortNumber(dto.getSortNumber());
                                            vo.setStatus(dto.getStatus());
                                            vo.setWard(
                                                    dto.getWard() == null
                                                            ? null
                                                            : wardMap.get(dto.getWard()));
                                            vo.setInputCode(dto.getInputCode());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把OrganizationWardDto转换成OrganizationWardSimpleVo */
    @AutoGenerated(locked = true, uuid = "adf17ef7-1121-464f-aec5-ce715abddfa5-converter-list")
    public List<OrganizationWardSimpleVo> convertToOrganizationWardSimpleVoList(
            List<OrganizationWardDto> dtoList) {
        return new ArrayList<>(convertToOrganizationWardSimpleVoMap(dtoList).values());
    }

    /** 使用默认方式组装OrganizationWardSimpleVo数据 */
    @AutoGenerated(locked = true, uuid = "c4f38bb2-5c04-3d20-9ec7-416c9d6cafc5")
    public OrganizationWardSimpleVo convertAndAssembleData(OrganizationWardDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }
}
