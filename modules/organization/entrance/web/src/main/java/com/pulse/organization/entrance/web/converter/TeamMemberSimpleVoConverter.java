package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.entrance.web.query.assembler.TeamMemberSimpleVoDataAssembler;
import com.pulse.organization.entrance.web.vo.TeamMemberSimpleVo;
import com.pulse.organization.manager.dto.TeamMemberBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到TeamMemberSimpleVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "022d9d4f-b13c-4bb9-9c58-af8678f4d81c|VO|CONVERTER")
public class TeamMemberSimpleVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private TeamMemberSimpleVoDataAssembler teamMemberSimpleVoDataAssembler;

    /** 把TeamMemberBaseDto转换成TeamMemberSimpleVo */
    @AutoGenerated(locked = false, uuid = "022d9d4f-b13c-4bb9-9c58-af8678f4d81c-converter-Map")
    public Map<TeamMemberBaseDto, TeamMemberSimpleVo> convertToTeamMemberSimpleVoMap(
            List<TeamMemberBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<TeamMemberBaseDto, TeamMemberSimpleVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            TeamMemberSimpleVo vo = new TeamMemberSimpleVo();
                                            vo.setId(dto.getId());
                                            vo.setStaffId(dto.getStaffId());
                                            vo.setIdentityTag(dto.getIdentityTag());
                                            vo.setRole(dto.getRole());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把TeamMemberBaseDto转换成TeamMemberSimpleVo */
    @AutoGenerated(locked = true, uuid = "022d9d4f-b13c-4bb9-9c58-af8678f4d81c-converter-list")
    public List<TeamMemberSimpleVo> convertToTeamMemberSimpleVoList(
            List<TeamMemberBaseDto> dtoList) {
        return new ArrayList<>(convertToTeamMemberSimpleVoMap(dtoList).values());
    }

    /** 使用默认方式组装TeamMemberSimpleVo列表数据 */
    @AutoGenerated(locked = true, uuid = "16c2b0b2-9df9-34df-af6f-c602015c6719")
    public List<TeamMemberSimpleVo> convertAndAssembleDataList(List<TeamMemberBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, TeamMemberSimpleVo> voMap =
                convertToTeamMemberSimpleVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        teamMemberSimpleVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把TeamMemberBaseDto转换成TeamMemberSimpleVo */
    @AutoGenerated(locked = true, uuid = "c8b555da-8783-3529-afa9-08673597d525")
    public TeamMemberSimpleVo convertToTeamMemberSimpleVo(TeamMemberBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToTeamMemberSimpleVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装TeamMemberSimpleVo数据 */
    @AutoGenerated(locked = true, uuid = "e326da8c-4394-372f-b7da-482b19fdc0fd")
    public TeamMemberSimpleVo convertAndAssembleData(TeamMemberBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }
}
