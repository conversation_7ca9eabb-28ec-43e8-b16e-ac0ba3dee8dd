package com.pulse.organization.entrance.web.query.collector;

import com.pulse.application.manager.dto.ApplicationBaseDto;
import com.pulse.application.manager.dto.ApplicationInfoOrganizationDto;
import com.pulse.application.manager.dto.ApplicationOrganizationDto;
import com.pulse.organization.entrance.web.converter.ApplicationOrganizationCampusVoConverter;
import com.pulse.organization.entrance.web.query.assembler.ApplicationOrganizationCampusVoDataAssembler.ApplicationOrganizationCampusVoDataHolder;
import com.pulse.organization.entrance.web.vo.ApplicationOrganizationCampusVo;
import com.pulse.organization.manager.facade.application.ApplicationBaseDtoServiceInOrganizationRpcAdapter;
import com.pulse.organization.manager.facade.application.ApplicationOrganizationDtoServiceInOrganizationRpcAdapter;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装ApplicationOrganizationCampusVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "a0518daf-b4f2-37be-a621-feb12f4d3dfd")
public class ApplicationOrganizationCampusVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private ApplicationBaseDtoServiceInOrganizationRpcAdapter
            applicationBaseDtoServiceInOrganizationRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationOrganizationCampusVoConverter applicationOrganizationCampusVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationOrganizationCampusVoDataCollector
            applicationOrganizationCampusVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationOrganizationDtoServiceInOrganizationRpcAdapter
            applicationOrganizationDtoServiceInOrganizationRpcAdapter;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "4edc3399-2236-3239-b26e-6eb6a2b0334f")
    private void fillDataWhenNecessary(ApplicationOrganizationCampusVoDataHolder dataHolder) {
        List<ApplicationOrganizationDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.application == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(ApplicationOrganizationDto::getApplicationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ApplicationBaseDto> baseDtoList =
                    applicationBaseDtoServiceInOrganizationRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(ApplicationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, ApplicationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            ApplicationBaseDto::getId, Function.identity()));
            Map<ApplicationBaseDto, ApplicationOrganizationCampusVo.ApplicationBaseVo> dtoVoMap =
                    applicationOrganizationCampusVoConverter.convertToApplicationBaseVoMap(
                            baseDtoList);
            Map<ApplicationBaseDto, ApplicationOrganizationCampusVo.ApplicationBaseVo>
                    baseDtoVoMap =
                            baseDtoList.stream()
                                    .collect(
                                            Collectors.toMap(
                                                    Function.identity(),
                                                    baseDto -> dtoVoMap.get(baseDto),
                                                    (o1, o2) -> o1,
                                                    LinkedHashMap::new));
            dataHolder.application =
                    rootDtoList.stream()
                            .map(ApplicationOrganizationDto::getApplicationId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** 获取ApplicationInfoOrganizationDto数据填充ApplicationOrganizationCampusVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "ba2fecd2-7d89-3b0f-877e-0d081a753afd")
    public void collectDataWithDtoData(
            List<ApplicationInfoOrganizationDto> dtoList,
            ApplicationOrganizationCampusVoDataHolder dataHolder) {
        List<ApplicationBaseDto> applicationList = new ArrayList<>();

        for (ApplicationInfoOrganizationDto rootDto : dtoList) {
            ApplicationBaseDto applicationDto = rootDto.getApplication();
            if (applicationDto != null) {
                applicationList.add(applicationDto);
            }
        }

        // access application
        Map<ApplicationBaseDto, ApplicationOrganizationCampusVo.ApplicationBaseVo>
                applicationVoMap =
                        applicationOrganizationCampusVoConverter.convertToApplicationBaseVoMap(
                                applicationList);
        dataHolder.application =
                applicationList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> applicationVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "f4e15c5d-03ac-3c90-8bfc-6fa47b6340b2")
    public void collectDataDefault(ApplicationOrganizationCampusVoDataHolder dataHolder) {
        applicationOrganizationCampusVoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
