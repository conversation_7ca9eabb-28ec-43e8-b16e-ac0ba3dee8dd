package com.pulse.organization.entrance.web.query.collector;

import com.pulse.consulting_room.manager.dto.ConsultingRoomBaseDto;
import com.pulse.organization.entrance.web.converter.ConsultingRoomBaseVoConverter;
import com.pulse.organization.entrance.web.converter.StaffConsultingRoomVoConverter;
import com.pulse.organization.entrance.web.converter.StaffSimpleVoConverter;
import com.pulse.organization.entrance.web.query.assembler.StaffConsultingRoomVoDataAssembler.StaffConsultingRoomVoDataHolder;
import com.pulse.organization.entrance.web.vo.ConsultingRoomBaseVo;
import com.pulse.organization.entrance.web.vo.StaffSimpleVo;
import com.pulse.organization.manager.dto.StaffBaseDto;
import com.pulse.organization.manager.dto.StaffConsultingRoomBaseDto;
import com.pulse.organization.manager.dto.StaffConsultingRoomDto;
import com.pulse.organization.manager.facade.consulting_room.ConsultingRoomBaseDtoServiceInOrganizationRpcAdapter;
import com.pulse.organization.service.StaffBaseDtoService;
import com.pulse.organization.service.StaffConsultingRoomBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装StaffConsultingRoomVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "eb1edcaf-b7bf-3fc7-867b-ecda7b32f1c4")
public class StaffConsultingRoomVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private ConsultingRoomBaseDtoServiceInOrganizationRpcAdapter
            consultingRoomBaseDtoServiceInOrganizationRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private ConsultingRoomBaseVoConverter consultingRoomBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffBaseDtoService staffBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffConsultingRoomBaseDtoService staffConsultingRoomBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffConsultingRoomVoConverter staffConsultingRoomVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffConsultingRoomVoDataCollector staffConsultingRoomVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private StaffSimpleVoConverter staffSimpleVoConverter;

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "48349ed0-8a0a-3eee-b734-6aa790f54f13")
    public void collectDataDefault(StaffConsultingRoomVoDataHolder dataHolder) {
        staffConsultingRoomVoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "68fa06f7-6c81-377a-8cb0-cb162bf1db4b")
    private void fillDataWhenNecessary(StaffConsultingRoomVoDataHolder dataHolder) {
        List<StaffConsultingRoomBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.staff == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(StaffConsultingRoomBaseDto::getStaffId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<StaffBaseDto> baseDtoList =
                    staffBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(StaffBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, StaffBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.toMap(StaffBaseDto::getId, Function.identity()));
            Map<StaffBaseDto, StaffSimpleVo> dtoVoMap =
                    staffSimpleVoConverter.convertToStaffSimpleVoMap(baseDtoList);
            Map<StaffBaseDto, StaffSimpleVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.staff =
                    rootDtoList.stream()
                            .map(StaffConsultingRoomBaseDto::getStaffId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.consultingRoom == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(StaffConsultingRoomBaseDto::getConsultingRoomId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ConsultingRoomBaseDto> baseDtoList =
                    consultingRoomBaseDtoServiceInOrganizationRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(ConsultingRoomBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, ConsultingRoomBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            ConsultingRoomBaseDto::getId, Function.identity()));
            Map<ConsultingRoomBaseDto, ConsultingRoomBaseVo> dtoVoMap =
                    consultingRoomBaseVoConverter.convertToConsultingRoomBaseVoMap(baseDtoList);
            Map<ConsultingRoomBaseDto, ConsultingRoomBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.consultingRoom =
                    rootDtoList.stream()
                            .map(StaffConsultingRoomBaseDto::getConsultingRoomId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** 获取StaffConsultingRoomDto数据填充StaffConsultingRoomVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "96376b4e-8d1f-3e47-b8e0-8710dbde130b")
    public void collectDataWithDtoData(
            List<StaffConsultingRoomDto> dtoList, StaffConsultingRoomVoDataHolder dataHolder) {
        List<StaffBaseDto> staffList = new ArrayList<>();
        List<ConsultingRoomBaseDto> consultingRoomList = new ArrayList<>();

        for (StaffConsultingRoomDto rootDto : dtoList) {
            StaffBaseDto staffDto = rootDto.getStaff();
            if (staffDto != null) {
                staffList.add(staffDto);
            }
            ConsultingRoomBaseDto consultingRoomDto = rootDto.getConsultingRoom();
            if (consultingRoomDto != null) {
                consultingRoomList.add(consultingRoomDto);
            }
        }

        // access staff
        Map<StaffBaseDto, StaffSimpleVo> staffVoMap =
                staffSimpleVoConverter.convertToStaffSimpleVoMap(staffList);
        dataHolder.staff =
                staffList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> staffVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access consultingRoom
        Map<ConsultingRoomBaseDto, ConsultingRoomBaseVo> consultingRoomVoMap =
                consultingRoomBaseVoConverter.convertToConsultingRoomBaseVoMap(consultingRoomList);
        dataHolder.consultingRoom =
                consultingRoomList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> consultingRoomVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }
}
