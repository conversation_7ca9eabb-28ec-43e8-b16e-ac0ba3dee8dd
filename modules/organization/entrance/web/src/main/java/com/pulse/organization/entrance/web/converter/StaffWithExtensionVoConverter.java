package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.entrance.web.query.assembler.StaffWithExtensionVoDataAssembler;
import com.pulse.organization.entrance.web.query.assembler.StaffWithExtensionVoDataAssembler.StaffWithExtensionVoDataHolder;
import com.pulse.organization.entrance.web.query.collector.StaffWithExtensionVoDataCollector;
import com.pulse.organization.entrance.web.vo.OrganizationSimpleVo;
import com.pulse.organization.entrance.web.vo.StaffExtensionBaseVo;
import com.pulse.organization.entrance.web.vo.StaffWithExtensionVo;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.StaffExtensionBaseDto;
import com.pulse.organization.manager.dto.StaffWithExtensionDto;
import com.pulse.organization.service.StaffBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到StaffWithExtensionVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "3226b041-6211-48ae-bd5b-490e70d0213c|VO|CONVERTER")
public class StaffWithExtensionVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationSimpleVoConverter organizationSimpleVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffBaseDtoService staffBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffExtensionBaseVoConverter staffExtensionBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffWithExtensionVoDataAssembler staffWithExtensionVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private StaffWithExtensionVoDataCollector staffWithExtensionVoDataCollector;

    /** 使用默认方式组装StaffWithExtensionVo数据 */
    @AutoGenerated(locked = true, uuid = "2987a242-db2b-3cac-bec5-14df8f3600e3")
    public StaffWithExtensionVo convertAndAssembleData(StaffWithExtensionDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把StaffWithExtensionDto转换成StaffWithExtensionVo */
    @AutoGenerated(locked = false, uuid = "3226b041-6211-48ae-bd5b-490e70d0213c-converter-Map")
    public Map<StaffWithExtensionDto, StaffWithExtensionVo> convertToStaffWithExtensionVoMap(
            List<StaffWithExtensionDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<StaffExtensionBaseDto, StaffExtensionBaseVo> staffExtensionMap =
                staffExtensionBaseVoConverter.convertToStaffExtensionBaseVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(StaffWithExtensionDto::getStaffExtension)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<OrganizationBaseDto, OrganizationSimpleVo> organizationMap =
                organizationSimpleVoConverter.convertToOrganizationSimpleVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(StaffWithExtensionDto::getOrganization)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<OrganizationBaseDto, OrganizationSimpleVo> accountingOrganizationMap =
                organizationSimpleVoConverter.convertToOrganizationSimpleVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(StaffWithExtensionDto::getAccountingOrganization)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<OrganizationBaseDto, OrganizationSimpleVo> hrOrganizationMap =
                organizationSimpleVoConverter.convertToOrganizationSimpleVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(StaffWithExtensionDto::getHrOrganization)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<StaffWithExtensionDto, StaffWithExtensionVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            StaffWithExtensionVo vo = new StaffWithExtensionVo();
                                            vo.setId(dto.getId());
                                            vo.setBirthDate(dto.getBirthDate());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setDescription(dto.getDescription());
                                            vo.setEmailAddress(dto.getEmailAddress());
                                            vo.setGender(dto.getGender());
                                            vo.setCertificateNumber(dto.getCertificateNumber());
                                            vo.setInputCode(dto.getInputCode());
                                            vo.setName(dto.getName());
                                            vo.setPhoneNumber(dto.getPhoneNumber());
                                            vo.setStaffNumber(dto.getStaffNumber());
                                            vo.setStatus(dto.getStatus());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setShortPhoneNumber(dto.getShortPhoneNumber());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setUpdateBy(dto.getUpdateBy());
                                            vo.setDeletedBy(dto.getDeletedBy());
                                            vo.setCertificateTypeId(dto.getCertificateTypeId());
                                            vo.setStaffTypeId(dto.getStaffTypeId());
                                            vo.setExpertFlag(dto.getExpertFlag());
                                            vo.setProfessionalTitleId(dto.getProfessionalTitleId());
                                            vo.setSortNumber(dto.getSortNumber());
                                            vo.setStaffExtension(
                                                    dto.getStaffExtension() == null
                                                            ? null
                                                            : staffExtensionMap.get(
                                                                    dto.getStaffExtension()));
                                            vo.setOrganization(
                                                    dto.getOrganization() == null
                                                            ? null
                                                            : organizationMap.get(
                                                                    dto.getOrganization()));
                                            vo.setAccountingOrganization(
                                                    dto.getAccountingOrganization() == null
                                                            ? null
                                                            : accountingOrganizationMap.get(
                                                                    dto
                                                                            .getAccountingOrganization()));
                                            vo.setHrOrganization(
                                                    dto.getHrOrganization() == null
                                                            ? null
                                                            : hrOrganizationMap.get(
                                                                    dto.getHrOrganization()));
                                            vo.setPositionId(dto.getPositionId());
                                            vo.setPromotionDate(dto.getPromotionDate());
                                            vo.setRegisterTypeList(dto.getRegisterTypeList());
                                            vo.setProvincePlatformCancelFlag(
                                                    dto.getProvincePlatformCancelFlag());
                                            vo.setCountrysideStartDate(
                                                    dto.getCountrysideStartDate());
                                            vo.setDoctorRemark(dto.getDoctorRemark());
                                            vo.setRegisterDoctorFlag(dto.getRegisterDoctorFlag());
                                            vo.setCountrysideEndDate(dto.getCountrysideEndDate());
                                            vo.setRegisterDoctorEnableFlag(
                                                    dto.getRegisterDoctorEnableFlag());
                                            vo.setCampusOrganizationId(
                                                    dto.getCampusOrganizationId());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把StaffWithExtensionDto转换成StaffWithExtensionVo */
    @AutoGenerated(locked = true, uuid = "3226b041-6211-48ae-bd5b-490e70d0213c-converter-list")
    public List<StaffWithExtensionVo> convertToStaffWithExtensionVoList(
            List<StaffWithExtensionDto> dtoList) {
        return new ArrayList<>(convertToStaffWithExtensionVoMap(dtoList).values());
    }

    /** 把StaffWithExtensionDto转换成StaffWithExtensionVo */
    @AutoGenerated(locked = true, uuid = "5d56d3e5-264a-356a-bf4c-389aa33f3d16")
    public StaffWithExtensionVo convertToStaffWithExtensionVo(StaffWithExtensionDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToStaffWithExtensionVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装StaffWithExtensionVo列表数据 */
    @AutoGenerated(locked = true, uuid = "d2147d8e-f2c3-3b4a-8477-0dadb3702114")
    public List<StaffWithExtensionVo> convertAndAssembleDataList(
            List<StaffWithExtensionDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        StaffWithExtensionVoDataHolder dataHolder = new StaffWithExtensionVoDataHolder();
        dataHolder.setRootBaseDtoList(
                staffBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(StaffWithExtensionDto::getId)
                                .collect(Collectors.toList())));
        Map<String, StaffWithExtensionVo> voMap =
                convertToStaffWithExtensionVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        staffWithExtensionVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        staffWithExtensionVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
