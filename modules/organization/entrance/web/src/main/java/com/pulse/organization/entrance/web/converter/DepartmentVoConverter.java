package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.entrance.web.query.assembler.DepartmentVoDataAssembler;
import com.pulse.organization.entrance.web.vo.DepartmentVo;
import com.pulse.organization.manager.dto.DepartmentDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到DepartmentVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "6e70fc23-01c8-42b4-b927-6b0e8a0a27f9|VO|CONVERTER")
public class DepartmentVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private DepartmentVoDataAssembler departmentVoDataAssembler;

    /** 把DepartmentDto转换成DepartmentVo */
    @AutoGenerated(locked = false, uuid = "6e70fc23-01c8-42b4-b927-6b0e8a0a27f9-converter-Map")
    public Map<DepartmentDto, DepartmentVo> convertToDepartmentVoMap(List<DepartmentDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<DepartmentDto, DepartmentVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DepartmentVo vo = new DepartmentVo();
                                            vo.setId(dto.getId());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setAccountingDepartmentOrganizationId(
                                                    dto.getAccountingDepartmentOrganizationId());
                                            vo.setAccountingDepartmentName(
                                                    dto.getAccountingDepartmentName());
                                            vo.setCostDepartmentOrganizationId(
                                                    dto.getCostDepartmentOrganizationId());
                                            vo.setCostDepartmentName(dto.getCostDepartmentName());
                                            vo.setHrDepartmentOrganizationId(
                                                    dto.getHrDepartmentOrganizationId());
                                            vo.setHrDepartmentName(dto.getHrDepartmentName());
                                            vo.setMedicalRecordDepartmentCode(
                                                    dto.getMedicalRecordDepartmentCode());
                                            vo.setCampusOrganizationId(
                                                    dto.getCampusOrganizationId());
                                            vo.setCampusName(dto.getCampusName());
                                            vo.setAgeUpperLimit(dto.getAgeUpperLimit());
                                            vo.setAgeLowerLimit(dto.getAgeLowerLimit());
                                            vo.setOrganizationId(dto.getOrganizationId());
                                            vo.setMedicalServiceType(dto.getMedicalServiceType());
                                            vo.setParentDepartmentOrganizationId(
                                                    dto.getParentDepartmentOrganizationId());
                                            vo.setParentDepartmentName(
                                                    dto.getParentDepartmentName());
                                            vo.setDepartmentLevel(dto.getDepartmentLevel());
                                            vo.setAveragePrescriptionLimit(
                                                    dto.getAveragePrescriptionLimit());
                                            vo.setInjectionSecondDayAmount(
                                                    dto.getInjectionSecondDayAmount());
                                            vo.setDefaultPrescriptionType(
                                                    dto.getDefaultPrescriptionType());
                                            vo.setSpecialtyFlag(dto.getSpecialtyFlag());
                                            vo.setDepartmentProperty(dto.getDepartmentProperty());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setDeletedBy(dto.getDeletedBy());
                                            vo.setStandardDepartmentCatalogId(
                                                    dto.getStandardDepartmentCatalogId());
                                            vo.setOpenTimePeriod(dto.getOpenTimePeriod());
                                            vo.setDrugTypeList(dto.getDrugTypeList());
                                            vo.setDrugCatalogList(dto.getDrugCatalogList());
                                            vo.setStorageType(dto.getStorageType());
                                            vo.setProvincePlatformCancelFlag(
                                                    dto.getProvincePlatformCancelFlag());
                                            vo.setGenderLimit(dto.getGenderLimit());
                                            vo.setRegisterDepartmentFlag(
                                                    dto.getRegisterDepartmentFlag());
                                            vo.setManageDepartmentOrganizationId(
                                                    dto.getManageDepartmentOrganizationId());
                                            vo.setManageDepartmentName(
                                                    dto.getManageDepartmentName());
                                            vo.setRegisterDepartmentEnableFlag(
                                                    dto.getRegisterDepartmentEnableFlag());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DepartmentDto转换成DepartmentVo */
    @AutoGenerated(locked = true, uuid = "6e70fc23-01c8-42b4-b927-6b0e8a0a27f9-converter-list")
    public List<DepartmentVo> convertToDepartmentVoList(List<DepartmentDto> dtoList) {
        return new ArrayList<>(convertToDepartmentVoMap(dtoList).values());
    }

    /** 把DepartmentDto转换成DepartmentVo */
    @AutoGenerated(locked = true, uuid = "89ab363b-89f2-3d63-ba39-282913a92c7a")
    public DepartmentVo convertToDepartmentVo(DepartmentDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDepartmentVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装DepartmentVo数据 */
    @AutoGenerated(locked = true, uuid = "93f8ee01-da79-394c-a444-96a464d3a118")
    public DepartmentVo convertAndAssembleData(DepartmentDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装DepartmentVo列表数据 */
    @AutoGenerated(locked = true, uuid = "fe12b89c-cc15-353e-b906-e888c144712f")
    public List<DepartmentVo> convertAndAssembleDataList(List<DepartmentDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, DepartmentVo> voMap =
                convertToDepartmentVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        departmentVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
