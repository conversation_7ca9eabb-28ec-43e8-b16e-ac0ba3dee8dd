package com.pulse.organization.entrance.web.query.assembler;

import com.pulse.organization.entrance.web.vo.OrganizationBaseVo;
import com.pulse.organization.entrance.web.vo.TeamOrganizationVo;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.TeamOrganizationBaseDto;
import com.pulse.organization.service.TeamOrganizationBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** TeamOrganizationVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "462b913a-9fd7-3e8a-81ca-cd006eab38ad")
public class TeamOrganizationVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private TeamOrganizationBaseDtoService teamOrganizationBaseDtoService;

    /** 批量自定义组装TeamOrganizationVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "39c0502a-c810-3e86-8b52-464d6345c5ab")
    public void assembleDataCustomized(List<TeamOrganizationVo> dataList) {
        // 自定义数据组装

    }

    /** 组装TeamOrganizationVo数据 */
    @AutoGenerated(locked = true, uuid = "8f38344b-6485-36da-a3d2-a1e1641b376a")
    public void assembleData(
            Map<String, TeamOrganizationVo> voMap,
            TeamOrganizationVoDataAssembler.TeamOrganizationVoDataHolder dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<TeamOrganizationBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, Pair<OrganizationBaseDto, OrganizationBaseVo>> organization =
                dataHolder.organization.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getId(),
                                        dto -> Pair.of(dto, dataHolder.organization.get(dto)),
                                        (o1, o2) -> o1));

        for (TeamOrganizationBaseDto baseDto : baseDtoList) {
            TeamOrganizationVo vo = voMap.get(baseDto.getId());
            vo.setOrganization(
                    Optional.ofNullable(organization.get(baseDto.getOrganizationId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class TeamOrganizationVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<TeamOrganizationBaseDto> rootBaseDtoList;

        /** 持有字段organization的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<OrganizationBaseDto, OrganizationBaseVo> organization;
    }
}
