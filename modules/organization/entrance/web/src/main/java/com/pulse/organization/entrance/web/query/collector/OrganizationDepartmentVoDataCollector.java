package com.pulse.organization.entrance.web.query.collector;

import com.pulse.organization.entrance.web.converter.DepartmentVoConverter;
import com.pulse.organization.entrance.web.converter.OrganizationDepartmentVoConverter;
import com.pulse.organization.entrance.web.query.assembler.OrganizationDepartmentVoDataAssembler.OrganizationDepartmentVoDataHolder;
import com.pulse.organization.entrance.web.vo.DepartmentVo;
import com.pulse.organization.manager.converter.DepartmentDtoConverter;
import com.pulse.organization.manager.dto.DepartmentBaseDto;
import com.pulse.organization.manager.dto.DepartmentDto;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.OrganizationDepartmentDto;
import com.pulse.organization.service.DepartmentBaseDtoService;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装OrganizationDepartmentVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "fb614b8c-2674-3b89-83bb-fb950305812e")
public class OrganizationDepartmentVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private DepartmentBaseDtoService departmentBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DepartmentDtoConverter departmentDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DepartmentVoConverter departmentVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationDepartmentVoConverter organizationDepartmentVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationDepartmentVoDataCollector organizationDepartmentVoDataCollector;

    /** 获取OrganizationDepartmentDto数据填充OrganizationDepartmentVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "ba33f527-3272-387d-8202-5cdf476bed83")
    public void collectDataWithDtoData(
            List<OrganizationDepartmentDto> dtoList,
            OrganizationDepartmentVoDataHolder dataHolder) {
        Map<DepartmentBaseDto, DepartmentDto> departmentBaseDtoDtoMap = new LinkedHashMap<>();

        for (OrganizationDepartmentDto rootDto : dtoList) {
            DepartmentDto departmentDto = rootDto.getDepartment();
            if (departmentDto != null) {
                DepartmentBaseDto departmentBaseDto =
                        departmentDtoConverter.convertFromDepartmentDtoToDepartmentBaseDto(
                                departmentDto);
                departmentBaseDto.setOrganizationId(rootDto.getId());
                departmentBaseDtoDtoMap.put(departmentBaseDto, departmentDto);
            }
        }

        // access department
        Map<DepartmentDto, DepartmentVo> departmentVoMap =
                departmentVoConverter.convertToDepartmentVoMap(
                        new ArrayList<>(departmentBaseDtoDtoMap.values()));
        dataHolder.department =
                departmentBaseDtoDtoMap.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                departmentVoMap.get(
                                                        departmentBaseDtoDtoMap.get(baseDto)),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "f8bbbc19-5c4e-37e2-9af1-4d0c87443bc2")
    private void fillDataWhenNecessary(OrganizationDepartmentVoDataHolder dataHolder) {
        List<OrganizationBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.department == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(OrganizationBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DepartmentBaseDto> baseDtoList =
                    departmentBaseDtoService.getByOrganizationIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(DepartmentBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<DepartmentBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(DepartmentBaseDto::getOrganizationId));
            Map<String, DepartmentDto> departmentDtoMap =
                    departmentDtoConverter
                            .convertFromDepartmentBaseDtoToDepartmentDto(baseDtoList)
                            .stream()
                            .collect(Collectors.toMap(DepartmentDto::getId, Function.identity()));
            Map<DepartmentDto, DepartmentVo> dtoVoMap =
                    departmentVoConverter.convertToDepartmentVoMap(
                            new ArrayList<>(departmentDtoMap.values()));
            Map<DepartmentBaseDto, DepartmentVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .filter(baseDto -> departmentDtoMap.containsKey(baseDto.getId()))
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    dtoVoMap.get(
                                                            departmentDtoMap.get(baseDto.getId())),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.department =
                    rootDtoList.stream()
                            .map(OrganizationBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "fca236b1-d046-383c-af4b-4730a21838c1")
    public void collectDataDefault(OrganizationDepartmentVoDataHolder dataHolder) {
        organizationDepartmentVoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
