package com.pulse.organization.entrance.web.query.assembler;

import com.pulse.organization.entrance.web.vo.StaffOrganizationBaseVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** StaffOrganizationBaseVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "cbcc0250-f706-3d49-a94c-5812ec091dbe")
public class StaffOrganizationBaseVoDataAssembler {

    /** 组装StaffOrganizationBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "c6ffb6e4-**************-3cd620d84c75")
    public void assembleData(Map<String, StaffOrganizationBaseVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装StaffOrganizationBaseVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "f0ce43ad-16d9-377f-b70b-d57fee76a2c4")
    public void assembleDataCustomized(List<StaffOrganizationBaseVo> dataList) {
        // 自定义数据组装

    }
}
