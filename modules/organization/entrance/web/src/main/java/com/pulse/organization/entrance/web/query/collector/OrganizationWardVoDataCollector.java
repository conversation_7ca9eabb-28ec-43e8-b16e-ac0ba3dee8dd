package com.pulse.organization.entrance.web.query.collector;

import com.pulse.organization.entrance.web.converter.OrganizationWardVoConverter;
import com.pulse.organization.entrance.web.converter.WardBaseVoConverter;
import com.pulse.organization.entrance.web.query.assembler.OrganizationWardVoDataAssembler.OrganizationWardVoDataHolder;
import com.pulse.organization.entrance.web.vo.WardBaseVo;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.OrganizationWardDto;
import com.pulse.organization.manager.dto.WardBaseDto;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.pulse.organization.service.WardBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装OrganizationWardVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "ecd1ca59-5902-3fdf-a9ab-2d798efbf492")
public class OrganizationWardVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationWardVoConverter organizationWardVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationWardVoDataCollector organizationWardVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private WardBaseDtoService wardBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private WardBaseVoConverter wardBaseVoConverter;

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "3163f2be-2094-3c51-963b-250d28551898")
    public void collectDataDefault(OrganizationWardVoDataHolder dataHolder) {
        organizationWardVoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "807e3517-3807-3b8a-9899-8e4e871fc6a4")
    private void fillDataWhenNecessary(OrganizationWardVoDataHolder dataHolder) {
        List<OrganizationBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.ward == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(OrganizationBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<WardBaseDto> baseDtoList =
                    wardBaseDtoService.getByOrganizationIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(WardBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<WardBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(WardBaseDto::getOrganizationId));
            Map<WardBaseDto, WardBaseVo> dtoVoMap =
                    wardBaseVoConverter.convertToWardBaseVoMap(baseDtoList);
            Map<WardBaseDto, WardBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.ward =
                    rootDtoList.stream()
                            .map(OrganizationBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** 获取OrganizationWardDto数据填充OrganizationWardVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "b9b1c5fb-09ea-3614-8f3a-50a73eb52f68")
    public void collectDataWithDtoData(
            List<OrganizationWardDto> dtoList, OrganizationWardVoDataHolder dataHolder) {
        List<WardBaseDto> wardList = new ArrayList<>();

        for (OrganizationWardDto rootDto : dtoList) {
            WardBaseDto wardDto = rootDto.getWard();
            if (wardDto != null) {
                wardList.add(wardDto);
            }
        }

        // access ward
        Map<WardBaseDto, WardBaseVo> wardVoMap =
                wardBaseVoConverter.convertToWardBaseVoMap(wardList);
        dataHolder.ward =
                wardList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> wardVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }
}
