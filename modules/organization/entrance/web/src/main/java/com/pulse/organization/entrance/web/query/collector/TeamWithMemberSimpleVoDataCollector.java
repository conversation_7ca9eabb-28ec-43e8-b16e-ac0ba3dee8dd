package com.pulse.organization.entrance.web.query.collector;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.entrance.web.converter.TeamMemberSimpleVoConverter;
import com.pulse.organization.entrance.web.converter.TeamWithMemberSimpleVoConverter;
import com.pulse.organization.entrance.web.query.assembler.TeamWithMemberSimpleVoDataAssembler.TeamWithMemberSimpleVoDataHolder;
import com.pulse.organization.entrance.web.vo.TeamMemberSimpleVo;
import com.pulse.organization.manager.dto.TeamBaseDto;
import com.pulse.organization.manager.dto.TeamMemberBaseDto;
import com.pulse.organization.manager.dto.TeamWithMemberDto;
import com.pulse.organization.service.TeamBaseDtoService;
import com.pulse.organization.service.TeamMemberBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装TeamWithMemberSimpleVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "ae6e2fe7-3e0c-3552-9d94-61b7ea157427")
public class TeamWithMemberSimpleVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private TeamBaseDtoService teamBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TeamMemberBaseDtoService teamMemberBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TeamMemberSimpleVoConverter teamMemberSimpleVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TeamWithMemberSimpleVoConverter teamWithMemberSimpleVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TeamWithMemberSimpleVoDataCollector teamWithMemberSimpleVoDataCollector;

    /** 获取TeamWithMemberDto数据填充TeamWithMemberSimpleVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "5872dffe-a4b4-390e-b801-dfa3a2acbe18")
    public void collectDataWithDtoData(
            List<TeamWithMemberDto> dtoList, TeamWithMemberSimpleVoDataHolder dataHolder) {
        List<TeamMemberBaseDto> teamMemberListList = new ArrayList<>();

        for (TeamWithMemberDto rootDto : dtoList) {
            if (CollectionUtil.isNotEmpty(rootDto.getTeamMemberList())) {
                for (TeamMemberBaseDto teamMemberListDto : rootDto.getTeamMemberList()) {
                    teamMemberListList.add(teamMemberListDto);
                }
            }
        }

        // access teamMemberList
        Map<TeamMemberBaseDto, TeamMemberSimpleVo> teamMemberListVoMap =
                teamMemberSimpleVoConverter.convertToTeamMemberSimpleVoMap(teamMemberListList);
        dataHolder.teamMemberList =
                teamMemberListList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> teamMemberListVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "8736104a-bc9b-3d0e-8df3-f4d639810ad2")
    public void collectDataDefault(TeamWithMemberSimpleVoDataHolder dataHolder) {
        teamWithMemberSimpleVoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "e62d41c1-e36c-35d3-b538-ad05d0488508")
    private void fillDataWhenNecessary(TeamWithMemberSimpleVoDataHolder dataHolder) {
        List<TeamBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.teamMemberList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(TeamBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<TeamMemberBaseDto> baseDtoList =
                    teamMemberBaseDtoService.getByTeamIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(TeamMemberBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<TeamMemberBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(TeamMemberBaseDto::getTeamId));
            Map<TeamMemberBaseDto, TeamMemberSimpleVo> dtoVoMap =
                    teamMemberSimpleVoConverter.convertToTeamMemberSimpleVoMap(baseDtoList);
            Map<TeamMemberBaseDto, TeamMemberSimpleVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.teamMemberList =
                    rootDtoList.stream()
                            .map(TeamBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }
}
