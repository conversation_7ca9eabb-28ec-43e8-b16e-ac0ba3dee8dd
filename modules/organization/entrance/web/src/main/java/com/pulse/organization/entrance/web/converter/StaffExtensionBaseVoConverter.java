package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.entrance.web.query.assembler.StaffExtensionBaseVoDataAssembler;
import com.pulse.organization.entrance.web.vo.StaffExtensionBaseVo;
import com.pulse.organization.manager.dto.StaffExtensionBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到StaffExtensionBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "27b25065-5b17-4445-86f8-d9293c742552|VO|CONVERTER")
public class StaffExtensionBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private StaffExtensionBaseVoDataAssembler staffExtensionBaseVoDataAssembler;

    /** 把StaffExtensionBaseDto转换成StaffExtensionBaseVo */
    @AutoGenerated(locked = false, uuid = "27b25065-5b17-4445-86f8-d9293c742552-converter-Map")
    public Map<StaffExtensionBaseDto, StaffExtensionBaseVo> convertToStaffExtensionBaseVoMap(
            List<StaffExtensionBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<StaffExtensionBaseDto, StaffExtensionBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            StaffExtensionBaseVo vo = new StaffExtensionBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setStaffId(dto.getStaffId());
                                            vo.setAuthenticationInfo(dto.getAuthenticationInfo());
                                            vo.setProfessionalField(dto.getProfessionalField());
                                            vo.setProfessionalExpertise(
                                                    dto.getProfessionalExpertise());
                                            vo.setLabelIdList(dto.getLabelIdList());
                                            vo.setNationalCode(dto.getNationalCode());
                                            vo.setWorkTypeCode(dto.getWorkTypeCode());
                                            vo.setSocialAccount(dto.getSocialAccount());
                                            vo.setRecruitmentTime(dto.getRecruitmentTime());
                                            vo.setInternFlag(dto.getInternFlag());
                                            vo.setTutorId(dto.getTutorId());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setEthnicityCode(dto.getEthnicityCode());
                                            vo.setPoliticalAffiliation(
                                                    dto.getPoliticalAffiliation());
                                            vo.setMaritalStatus(dto.getMaritalStatus());
                                            vo.setHomeAddress(dto.getHomeAddress());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setDeletedBy(dto.getDeletedBy());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把StaffExtensionBaseDto转换成StaffExtensionBaseVo */
    @AutoGenerated(locked = true, uuid = "27b25065-5b17-4445-86f8-d9293c742552-converter-list")
    public List<StaffExtensionBaseVo> convertToStaffExtensionBaseVoList(
            List<StaffExtensionBaseDto> dtoList) {
        return new ArrayList<>(convertToStaffExtensionBaseVoMap(dtoList).values());
    }

    /** 使用默认方式组装StaffExtensionBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "56c0bf02-09ee-3c8e-8cfb-7e0db8525c37")
    public List<StaffExtensionBaseVo> convertAndAssembleDataList(
            List<StaffExtensionBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, StaffExtensionBaseVo> voMap =
                convertToStaffExtensionBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        staffExtensionBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 使用默认方式组装StaffExtensionBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "825554b4-4863-34b0-b8c3-c1b6d6e54ca7")
    public StaffExtensionBaseVo convertAndAssembleData(StaffExtensionBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把StaffExtensionBaseDto转换成StaffExtensionBaseVo */
    @AutoGenerated(locked = true, uuid = "c3eb5093-e08d-3fdf-9f0a-fcd7a65f1b35")
    public StaffExtensionBaseVo convertToStaffExtensionBaseVo(StaffExtensionBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToStaffExtensionBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }
}
