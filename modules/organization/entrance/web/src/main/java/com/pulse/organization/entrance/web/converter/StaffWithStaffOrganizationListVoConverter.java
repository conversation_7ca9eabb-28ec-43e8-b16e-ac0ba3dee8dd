package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.entrance.web.query.assembler.StaffWithStaffOrganizationListVoDataAssembler;
import com.pulse.organization.entrance.web.query.assembler.StaffWithStaffOrganizationListVoDataAssembler.StaffWithStaffOrganizationListVoDataHolder;
import com.pulse.organization.entrance.web.query.collector.StaffWithStaffOrganizationListVoDataCollector;
import com.pulse.organization.entrance.web.vo.StaffOrganizationVo;
import com.pulse.organization.entrance.web.vo.StaffWithStaffOrganizationListVo;
import com.pulse.organization.manager.dto.StaffOrganizationDto;
import com.pulse.organization.manager.dto.StaffWithStaffOrganizationListDto;
import com.pulse.organization.service.StaffBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到StaffWithStaffOrganizationListVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "690c407e-1d29-491e-8d9b-77b5e9b88b67|VO|CONVERTER")
public class StaffWithStaffOrganizationListVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private StaffBaseDtoService staffBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffOrganizationVoConverter staffOrganizationVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffWithStaffOrganizationListVoDataAssembler
            staffWithStaffOrganizationListVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private StaffWithStaffOrganizationListVoDataCollector
            staffWithStaffOrganizationListVoDataCollector;

    /** 把StaffWithStaffOrganizationListDto转换成StaffWithStaffOrganizationListVo */
    @AutoGenerated(locked = false, uuid = "690c407e-1d29-491e-8d9b-77b5e9b88b67-converter-Map")
    public Map<StaffWithStaffOrganizationListDto, StaffWithStaffOrganizationListVo>
            convertToStaffWithStaffOrganizationListVoMap(
                    List<StaffWithStaffOrganizationListDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<StaffOrganizationDto, StaffOrganizationVo> staffOrganizationListMap =
                staffOrganizationVoConverter.convertToStaffOrganizationVoMap(
                        dtoList.stream()
                                .filter(
                                        dto ->
                                                CollectionUtil.isNotEmpty(
                                                        dto.getStaffOrganizationList()))
                                .flatMap(dto -> dto.getStaffOrganizationList().stream())
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<StaffWithStaffOrganizationListDto, StaffWithStaffOrganizationListVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            StaffWithStaffOrganizationListVo vo =
                                                    new StaffWithStaffOrganizationListVo();
                                            vo.setId(dto.getId());
                                            vo.setBirthDate(dto.getBirthDate());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setDescription(dto.getDescription());
                                            vo.setEmailAddress(dto.getEmailAddress());
                                            vo.setGender(dto.getGender());
                                            vo.setCertificateNumber(dto.getCertificateNumber());
                                            vo.setInputCode(dto.getInputCode());
                                            vo.setName(dto.getName());
                                            vo.setPhoneNumber(dto.getPhoneNumber());
                                            vo.setStaffNumber(dto.getStaffNumber());
                                            vo.setStatus(dto.getStatus());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setShortPhoneNumber(dto.getShortPhoneNumber());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setUpdateBy(dto.getUpdateBy());
                                            vo.setDeletedBy(dto.getDeletedBy());
                                            vo.setCertificateTypeId(dto.getCertificateTypeId());
                                            vo.setStaffTypeId(dto.getStaffTypeId());
                                            vo.setExpertFlag(dto.getExpertFlag());
                                            vo.setProfessionalTitleId(dto.getProfessionalTitleId());
                                            vo.setSortNumber(dto.getSortNumber());
                                            vo.setOrganizationId(dto.getOrganizationId());
                                            vo.setAccountingOrganizationId(
                                                    dto.getAccountingOrganizationId());
                                            vo.setHrOrganizationId(dto.getHrOrganizationId());
                                            vo.setPositionId(dto.getPositionId());
                                            vo.setPromotionDate(dto.getPromotionDate());
                                            vo.setRegisterTypeList(dto.getRegisterTypeList());
                                            vo.setProvincePlatformCancelFlag(
                                                    dto.getProvincePlatformCancelFlag());
                                            vo.setCountrysideStartDate(
                                                    dto.getCountrysideStartDate());
                                            vo.setDoctorRemark(dto.getDoctorRemark());
                                            vo.setRegisterDoctorFlag(dto.getRegisterDoctorFlag());
                                            vo.setCountrysideEndDate(dto.getCountrysideEndDate());
                                            vo.setRegisterDoctorEnableFlag(
                                                    dto.getRegisterDoctorEnableFlag());
                                            vo.setStaffOrganizationList(
                                                    dto.getStaffOrganizationList() == null
                                                            ? null
                                                            : dto
                                                                    .getStaffOrganizationList()
                                                                    .stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    staffOrganizationListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            vo.setCampusOrganizationId(
                                                    dto.getCampusOrganizationId());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把StaffWithStaffOrganizationListDto转换成StaffWithStaffOrganizationListVo */
    @AutoGenerated(locked = true, uuid = "690c407e-1d29-491e-8d9b-77b5e9b88b67-converter-list")
    public List<StaffWithStaffOrganizationListVo> convertToStaffWithStaffOrganizationListVoList(
            List<StaffWithStaffOrganizationListDto> dtoList) {
        return new ArrayList<>(convertToStaffWithStaffOrganizationListVoMap(dtoList).values());
    }

    /** 使用默认方式组装StaffWithStaffOrganizationListVo数据 */
    @AutoGenerated(locked = true, uuid = "93c06a5a-55f4-3419-a02c-b6c259f7cda0")
    public StaffWithStaffOrganizationListVo convertAndAssembleData(
            StaffWithStaffOrganizationListDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装StaffWithStaffOrganizationListVo列表数据 */
    @AutoGenerated(locked = true, uuid = "a4c86b89-f65f-3a48-b6f5-2b84baa5e0c1")
    public List<StaffWithStaffOrganizationListVo> convertAndAssembleDataList(
            List<StaffWithStaffOrganizationListDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        StaffWithStaffOrganizationListVoDataHolder dataHolder =
                new StaffWithStaffOrganizationListVoDataHolder();
        dataHolder.setRootBaseDtoList(
                staffBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(StaffWithStaffOrganizationListDto::getId)
                                .collect(Collectors.toList())));
        Map<String, StaffWithStaffOrganizationListVo> voMap =
                convertToStaffWithStaffOrganizationListVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        staffWithStaffOrganizationListVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        staffWithStaffOrganizationListVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把StaffWithStaffOrganizationListDto转换成StaffWithStaffOrganizationListVo */
    @AutoGenerated(locked = true, uuid = "fa291489-c562-3b5c-934f-df0912660139")
    public StaffWithStaffOrganizationListVo convertToStaffWithStaffOrganizationListVo(
            StaffWithStaffOrganizationListDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToStaffWithStaffOrganizationListVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }
}
