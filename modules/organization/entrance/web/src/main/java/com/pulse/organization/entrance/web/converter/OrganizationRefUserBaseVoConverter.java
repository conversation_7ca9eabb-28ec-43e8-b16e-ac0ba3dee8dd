package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.entrance.web.query.assembler.OrganizationRefUserBaseVoDataAssembler;
import com.pulse.organization.entrance.web.vo.OrganizationRefUserBaseVo;
import com.pulse.user.manager.dto.UserBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到OrganizationRefUserBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "35d5f4d6-8ef6-4462-8f20-090ca03808a5|VO|CONVERTER")
public class OrganizationRefUserBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationRefUserBaseVoDataAssembler organizationRefUserBaseVoDataAssembler;

    /** 把UserBaseDto转换成OrganizationRefUserBaseVo */
    @AutoGenerated(locked = true, uuid = "035db1ee-f8d9-3476-b4d1-70d3b22a4e88")
    public OrganizationRefUserBaseVo convertToOrganizationRefUserBaseVo(UserBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToOrganizationRefUserBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装OrganizationRefUserBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "1ee2ea00-add6-3261-adc9-2e754cf14aa5")
    public OrganizationRefUserBaseVo convertAndAssembleData(UserBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把UserBaseDto转换成OrganizationRefUserBaseVo */
    @AutoGenerated(locked = false, uuid = "35d5f4d6-8ef6-4462-8f20-090ca03808a5-converter-Map")
    public Map<UserBaseDto, OrganizationRefUserBaseVo> convertToOrganizationRefUserBaseVoMap(
            List<UserBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<UserBaseDto, OrganizationRefUserBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            OrganizationRefUserBaseVo vo =
                                                    new OrganizationRefUserBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setAddress(dto.getAddress());
                                            vo.setBirthDay(dto.getBirthDay());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setGender(dto.getGender());
                                            vo.setNames(dto.getNames());
                                            vo.setStatus(dto.getStatus());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setAge(dto.getAge());
                                            vo.setPhoneNumber(dto.getPhoneNumber());
                                            vo.setUserExpirationStart(dto.getUserExpirationStart());
                                            vo.setUserName(dto.getUserName());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把UserBaseDto转换成OrganizationRefUserBaseVo */
    @AutoGenerated(locked = true, uuid = "35d5f4d6-8ef6-4462-8f20-090ca03808a5-converter-list")
    public List<OrganizationRefUserBaseVo> convertToOrganizationRefUserBaseVoList(
            List<UserBaseDto> dtoList) {
        return new ArrayList<>(convertToOrganizationRefUserBaseVoMap(dtoList).values());
    }

    /** 使用默认方式组装OrganizationRefUserBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "ab068883-5191-3c41-bfbe-927778207cbb")
    public List<OrganizationRefUserBaseVo> convertAndAssembleDataList(List<UserBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, OrganizationRefUserBaseVo> voMap =
                convertToOrganizationRefUserBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        organizationRefUserBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
