package com.pulse.organization.entrance.web.controller;

import com.pulse.organization.common.enums.OrganizationStatusEnum;
import com.pulse.organization.common.enums.OrganizationTypeEnum;
import com.pulse.organization.entrance.web.converter.OrganizationWardSimpleVoConverter;
import com.pulse.organization.entrance.web.converter.OrganizationWardVoConverter;
import com.pulse.organization.entrance.web.converter.OrganizationWardWithDepartmentListVoConverter;
import com.pulse.organization.entrance.web.vo.OrganizationWardSimpleVo;
import com.pulse.organization.entrance.web.vo.OrganizationWardVo;
import com.pulse.organization.entrance.web.vo.OrganizationWardWithDepartmentListVo;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.OrganizationWardDto;
import com.pulse.organization.manager.dto.OrganizationWardWithDepartmentListDto;
import com.pulse.organization.persist.qto.SearchOrganizationWardQto;
import com.pulse.organization.persist.qto.SearchOrganizationWardWithDepartmentListQto;
import com.pulse.organization.service.OrganizationBOService;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.pulse.organization.service.OrganizationRelationshipBOService;
import com.pulse.organization.service.OrganizationWardWithDepartmentListDtoService;
import com.pulse.organization.service.bto.CreateOrganizationWardBto;
import com.pulse.organization.service.bto.MergeOrganizationRelationshipBto;
import com.pulse.organization.service.bto.UpdateOrganizationStatusBto;
import com.pulse.organization.service.bto.UpdateOrganizationWardBto;
import com.pulse.organization.service.query.OrganizationWardDtoQueryService;
import com.pulse.organization.service.query.OrganizationWardWithDepartmentListDtoQueryService;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "22bbc108-1536-3a48-afb0-************")
public class OrganizationWardController {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationWardDtoQueryService organizationWardDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationWardSimpleVoConverter organizationWardSimpleVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationWardVoConverter organizationWardVoConverter;

    @Resource
    private OrganizationWardWithDepartmentListDtoService
            organizationWardWithDepartmentListDtoService;

    @Resource
    private OrganizationWardWithDepartmentListVoConverter
            organizationWardWithDepartmentListVoConverter;

    @Resource
    private OrganizationWardWithDepartmentListDtoQueryService
            organizationWardWithDepartmentListDtoQueryService;

    @Resource private OrganizationBOService organizationBOService;

    @Resource private OrganizationRelationshipBOService organizationRelationshipBOService;

    @Resource private OrganizationBaseDtoService organizationBaseDtoService;

    /** 查询组织病区（带关联科室列表）列表(按院区、病区类型、状态、关键词) */
    @PublicInterface(id = "808d4728-19f6-41af-9cf4-ab9fc0236bbe", version = "1746774147336")
    @AutoGenerated(locked = false, uuid = "808d4728-19f6-41af-9cf4-ab9fc0236bbe")
    @RequestMapping(
            value = {"/api/organization/search-organization-ward-with-department-list-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<OrganizationWardWithDepartmentListVo>
            searchOrganizationWardWithDepartmentListPaged(
                    @Valid SearchOrganizationWardWithDepartmentListQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<OrganizationWardWithDepartmentListDto> dtoResult =
                organizationWardWithDepartmentListDtoQueryService
                        .searchOrganizationWardWithDepartmentListPaged(qto);
        VSQueryResult<OrganizationWardWithDepartmentListVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(
                organizationWardWithDepartmentListVoConverter.convertAndAssembleDataList(
                        dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查询组织病区列表(按院区、病区类型、状态、关键词等) */
    @PublicInterface(id = "*************-4675-b246-4e890f342bf1", version = "1746777505512")
    @AutoGenerated(locked = false, uuid = "*************-4675-b246-4e890f342bf1")
    @RequestMapping(
            value = {"/api/organization/search-organization-ward-simple-list"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<OrganizationWardSimpleVo> searchOrganizationWardSimpleList(
            @Valid SearchOrganizationWardQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<OrganizationWardDto> rpcResult =
                organizationWardDtoQueryService.searchOrganizationWard(qto);
        List<OrganizationWardSimpleVo> result =
                organizationWardSimpleVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 更新组织病区 */
    @Transactional
    @PublicInterface(id = "832db260-18cd-463a-a150-ce176e6aba80", version = "1742896089205")
    @AutoGenerated(locked = false, uuid = "832db260-18cd-463a-a150-ce176e6aba80")
    @RequestMapping(
            value = {"/api/organization/update-organization-ward"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String updateOrganizationWard(
            @Valid UpdateOrganizationWardBto updateOrganizationWardBto,
            @Valid List<MergeOrganizationRelationshipBto> mergeOrganizationRelationshipBtoList) {
        // TODO implement method
        // 验证
        validateOrganizationIdEffectiveness(
                updateOrganizationWardBto.getWardBto().getCampusOrganizationId(), "所属院区");
        // 更新病区
        updateOrganizationWardBto.setParentId(
                updateOrganizationWardBto.getWardBto().getCampusOrganizationId());
        updateOrganizationWardBto.setType(OrganizationTypeEnum.WARD);
        updateOrganizationWardBto.setOrganizationLevel(3L);
        String result = organizationBOService.updateOrganizationWard(updateOrganizationWardBto);
        if (Strings.isNotEmpty(result)) {
            // 保存病区科室组织关系
            organizationRelationshipBOService.mergeOrganizationRelationshipList(
                    mergeOrganizationRelationshipBtoList,
                    result,
                    OrganizationTypeEnum.WARD,
                    OrganizationTypeEnum.DEPARTMENT);
        }
        return result;
    }

    /** 根据主键获取组织（病区）带病区对应科室列表 */
    @PublicInterface(id = "954d53bf-8831-47e2-b6aa-776b9bf286e1", version = "1745290437361")
    @AutoGenerated(locked = false, uuid = "954d53bf-8831-47e2-b6aa-776b9bf286e1")
    @RequestMapping(
            value = {"/api/organization/get-organization-ward-with-department-list-by-id"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public OrganizationWardWithDepartmentListVo getOrganizationWardWithDepartmentListById(
            String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        OrganizationWardWithDepartmentListDto rpcResult =
                organizationWardWithDepartmentListDtoService.getById(id);
        OrganizationWardWithDepartmentListVo result =
                organizationWardWithDepartmentListVoConverter.convertAndAssembleData(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查询组织病区列表(按院区、病区类型、状态、关键词等) */
    @PublicInterface(id = "a35a5c88-c8f9-46b0-9119-5608f1610da3", version = "1746777428100")
    @AutoGenerated(locked = false, uuid = "a35a5c88-c8f9-46b0-9119-5608f1610da3")
    @RequestMapping(
            value = {"/api/organization/search-organization-ward"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<OrganizationWardVo> searchOrganizationWard(@Valid SearchOrganizationWardQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<OrganizationWardDto> rpcResult =
                organizationWardDtoQueryService.searchOrganizationWard(qto);
        List<OrganizationWardVo> result =
                organizationWardVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查询组织病区分页列表(按院区、病区类型、状态、关键词) */
    @PublicInterface(id = "c260cf79-1248-4c87-9950-3bf9e97b2e01", version = "1743669018093")
    @AutoGenerated(locked = false, uuid = "c260cf79-1248-4c87-9950-3bf9e97b2e01")
    @RequestMapping(
            value = {"/api/organization/search-organization-ward-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<OrganizationWardVo> searchOrganizationWardPaged(
            @Valid SearchOrganizationWardQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<OrganizationWardDto> dtoResult =
                organizationWardDtoQueryService.searchOrganizationWardPaged(qto);
        VSQueryResult<OrganizationWardVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(
                organizationWardVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查询组织病区列表(按院区、病区类型、状态、关键词等) */
    @PublicInterface(id = "c7edfea7-d6a9-4928-8e8e-348f56a914ed", version = "1746777573863")
    @AutoGenerated(locked = false, uuid = "c7edfea7-d6a9-4928-8e8e-348f56a914ed")
    @RequestMapping(
            value = {"/api/organization/search-organization-ward-simple-list-waterfall"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<OrganizationWardSimpleVo> searchOrganizationWardSimpleListWaterfall(
            @Valid SearchOrganizationWardQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<OrganizationWardDto> dtoResult =
                organizationWardDtoQueryService.searchOrganizationWardWaterfall(qto);
        VSQueryResult<OrganizationWardSimpleVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(
                organizationWardSimpleVoConverter.convertAndAssembleDataList(
                        dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 更新组织病区状态 */
    @PublicInterface(id = "d5a2144f-9904-4614-93a2-a537167be83a", version = "1742974099450")
    @AutoGenerated(locked = false, uuid = "d5a2144f-9904-4614-93a2-a537167be83a")
    @RequestMapping(
            value = {"/api/organization/update-organization-ward-status"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String updateOrganizationWardStatus(
            @Valid UpdateOrganizationStatusBto updateOrganizationWardStatusBto) {
        // TODO implement method
        String result =
                organizationBOService.updateOrganizationStatus(updateOrganizationWardStatusBto);
        return result;
    }

    /** 创建组织病区 */
    @PublicInterface(id = "fa4f9394-2ff8-4e0e-ad11-fa97d9cedc97", version = "1742896869598")
    @AutoGenerated(locked = false, uuid = "fa4f9394-2ff8-4e0e-ad11-fa97d9cedc97")
    @RequestMapping(
            value = {"/api/organization/create-organization-ward"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    @Transactional
    public String createOrganizationWard(
            @Valid CreateOrganizationWardBto createOrganizationWardBto,
            @Valid List<MergeOrganizationRelationshipBto> mergeOrganizationRelationshipBtoList) {
        // TODO implement method
        // 验证
        validateOrganizationIdEffectiveness(
                createOrganizationWardBto.getWardBto().getCampusOrganizationId(), "所属院区");
        // 创建病区
        createOrganizationWardBto.setParentId(
                createOrganizationWardBto.getWardBto().getCampusOrganizationId());
        createOrganizationWardBto.setType(OrganizationTypeEnum.WARD);
        createOrganizationWardBto.setOrganizationLevel(3L);
        createOrganizationWardBto.setStatus(OrganizationStatusEnum.ENABLE);
        String result = organizationBOService.createOrganizationWard(createOrganizationWardBto);
        if (Strings.isNotEmpty(result)) {
            // 保存病区科室组织关系
            organizationRelationshipBOService.mergeOrganizationRelationshipList(
                    mergeOrganizationRelationshipBtoList,
                    result,
                    OrganizationTypeEnum.WARD,
                    OrganizationTypeEnum.DEPARTMENT);
        }
        return result;
    }

    private void validateOrganizationIdEffectiveness(String organizationId, String message) {
        OrganizationBaseDto organizationBaseDto =
                organizationBaseDtoService.getById(organizationId);
        if (organizationBaseDto == null)
            throw new IgnoredException(ErrorCode.SYS_ERROR, message + "不存在!");
        if (organizationBaseDto.getStatus().equals(OrganizationStatusEnum.DISABLE))
            throw new IgnoredException(ErrorCode.SYS_ERROR, message + "未启用!");
    }
}
