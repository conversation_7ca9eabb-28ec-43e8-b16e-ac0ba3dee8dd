package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.entrance.web.query.assembler.StaffBaseVoDataAssembler;
import com.pulse.organization.entrance.web.vo.StaffBaseVo;
import com.pulse.organization.manager.dto.StaffBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到StaffBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "6ca5e502-084c-439d-addf-185e39791e27|VO|CONVERTER")
public class StaffBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private StaffBaseVoDataAssembler staffBaseVoDataAssembler;

    /** 把StaffBaseDto转换成StaffBaseVo */
    @AutoGenerated(locked = false, uuid = "6ca5e502-084c-439d-addf-185e39791e27-converter-Map")
    public Map<StaffBaseDto, StaffBaseVo> convertToStaffBaseVoMap(List<StaffBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<StaffBaseDto, StaffBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            StaffBaseVo vo = new StaffBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setBirthDate(dto.getBirthDate());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setDescription(dto.getDescription());
                                            vo.setEmailAddress(dto.getEmailAddress());
                                            vo.setGender(dto.getGender());
                                            vo.setCertificateNumber(dto.getCertificateNumber());
                                            vo.setInputCode(dto.getInputCode());
                                            vo.setName(dto.getName());
                                            vo.setPhoneNumber(dto.getPhoneNumber());
                                            vo.setStaffNumber(dto.getStaffNumber());
                                            vo.setStatus(dto.getStatus());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setShortPhoneNumber(dto.getShortPhoneNumber());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setUpdateBy(dto.getUpdateBy());
                                            vo.setDeletedBy(dto.getDeletedBy());
                                            vo.setCertificateTypeId(dto.getCertificateTypeId());
                                            vo.setStaffTypeId(dto.getStaffTypeId());
                                            vo.setExpertFlag(dto.getExpertFlag());
                                            vo.setProfessionalTitleId(dto.getProfessionalTitleId());
                                            vo.setSortNumber(dto.getSortNumber());
                                            vo.setOrganizationId(dto.getOrganizationId());
                                            vo.setAccountingOrganizationId(
                                                    dto.getAccountingOrganizationId());
                                            vo.setHrOrganizationId(dto.getHrOrganizationId());
                                            vo.setPositionId(dto.getPositionId());
                                            vo.setPromotionDate(dto.getPromotionDate());
                                            vo.setRegisterTypeList(dto.getRegisterTypeList());
                                            vo.setProvincePlatformCancelFlag(
                                                    dto.getProvincePlatformCancelFlag());
                                            vo.setCountrysideStartDate(
                                                    dto.getCountrysideStartDate());
                                            vo.setDoctorRemark(dto.getDoctorRemark());
                                            vo.setRegisterDoctorFlag(dto.getRegisterDoctorFlag());
                                            vo.setCountrysideEndDate(dto.getCountrysideEndDate());
                                            vo.setRegisterDoctorEnableFlag(
                                                    dto.getRegisterDoctorEnableFlag());
                                            vo.setCampusOrganizationId(
                                                    dto.getCampusOrganizationId());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把StaffBaseDto转换成StaffBaseVo */
    @AutoGenerated(locked = true, uuid = "6ca5e502-084c-439d-addf-185e39791e27-converter-list")
    public List<StaffBaseVo> convertToStaffBaseVoList(List<StaffBaseDto> dtoList) {
        return new ArrayList<>(convertToStaffBaseVoMap(dtoList).values());
    }

    /** 使用默认方式组装StaffBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "762700a1-9710-3f58-bb77-14ba3da0b8df")
    public StaffBaseVo convertAndAssembleData(StaffBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把StaffBaseDto转换成StaffBaseVo */
    @AutoGenerated(locked = true, uuid = "dadc2595-b441-3f0d-bc75-ce8152c716de")
    public StaffBaseVo convertToStaffBaseVo(StaffBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToStaffBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装StaffBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "fbc1e08d-49bd-3c53-8174-ee32637e0e5b")
    public List<StaffBaseVo> convertAndAssembleDataList(List<StaffBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, StaffBaseVo> voMap =
                convertToStaffBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        staffBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
