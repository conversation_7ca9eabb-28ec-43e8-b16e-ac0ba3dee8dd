package com.pulse.organization.entrance.web.controller;

import com.pulse.dictionary_basic.service.bto.MergeAttributeValueBto;
import com.pulse.organization.common.enums.OrganizationStatusEnum;
import com.pulse.organization.common.enums.OrganizationTypeEnum;
import com.pulse.organization.entrance.web.converter.OrganizationCampusVoConverter;
import com.pulse.organization.entrance.web.vo.OrganizationCampusVo;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.OrganizationCampusDto;
import com.pulse.organization.manager.facade.dictionary_basic.AttributeValueBOServiceInOrganizationRpcAdapter;
import com.pulse.organization.persist.qto.SearchOrganizationCampusQto;
import com.pulse.organization.service.OrganizationBOService;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.pulse.organization.service.OrganizationCampusDtoService;
import com.pulse.organization.service.bto.CreateOrganizationCampusBto;
import com.pulse.organization.service.bto.UpdateOrganizationCampusBto;
import com.pulse.organization.service.bto.UpdateOrganizationStatusBto;
import com.pulse.organization.service.query.OrganizationBaseDtoQueryService;
import com.pulse.organization.service.query.OrganizationCampusDtoQueryService;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "3a6ad5e4-2c1e-3c14-a0f7-62cc8b1180bf")
public class OrganizationCampusController {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationCampusDtoQueryService organizationCampusDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationCampusDtoService organizationCampusDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationCampusVoConverter organizationCampusVoConverter;

    @Resource private OrganizationBOService organizationBOService;

    @Resource
    private AttributeValueBOServiceInOrganizationRpcAdapter
            attributeValueBOServiceInOrganizationRpcAdapter;

    @Resource private OrganizationBaseDtoQueryService organizationBaseDtoQueryService;
    @Resource private OrganizationBaseDtoService organizationBaseDtoService;

    /** 根据主键获取组织（院区） */
    @PublicInterface(id = "0573af03-25c0-4823-a6b1-d935164800a8", version = "1743058964858")
    @AutoGenerated(locked = false, uuid = "0573af03-25c0-4823-a6b1-d935164800a8")
    @RequestMapping(
            value = {"/api/organization/get-organization-campus-by-id"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public OrganizationCampusVo getOrganizationCampusById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        OrganizationCampusDto rpcResult = organizationCampusDtoService.getById(id);
        OrganizationCampusVo result =
                organizationCampusVoConverter.convertAndAssembleData(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查询组织院区列表 */
    @PublicInterface(id = "06a228be-9f0c-4f25-9044-6d054d05e8e3", version = "1746781474235")
    @AutoGenerated(locked = false, uuid = "06a228be-9f0c-4f25-9044-6d054d05e8e3")
    @RequestMapping(
            value = {"/api/organization/search-organization-campus-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<OrganizationCampusVo> searchOrganizationCampusPaged(
            @Valid SearchOrganizationCampusQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<OrganizationCampusDto> dtoResult =
                organizationCampusDtoQueryService.searchOrganizationCampusPaged(qto);
        VSQueryResult<OrganizationCampusVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(
                organizationCampusVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 创建组织院区以及扩展属性 */
    @Transactional
    @PublicInterface(id = "22858505-bc45-4fe1-a8b5-a96ceff09781", version = "1742785124549")
    @AutoGenerated(locked = false, uuid = "22858505-bc45-4fe1-a8b5-a96ceff09781")
    @RequestMapping(
            value = {"/api/organization/create-organization-campus"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String createOrganizationCampus(
            @Valid CreateOrganizationCampusBto createOrganizationCampusBto,
            @Valid List<MergeAttributeValueBto> mergeAttributeValueBtoList) {
        // TODO implement method
        // 判断是否存在有效机构
        validateOrganizationIdEffectiveness(createOrganizationCampusBto.getParentId());

        // 创建院区
        createOrganizationCampusBto.setOrganizationLevel(2L);
        createOrganizationCampusBto.setType(OrganizationTypeEnum.CAMPUS);
        createOrganizationCampusBto.setStatus(OrganizationStatusEnum.ENABLE);
        String result = organizationBOService.createOrganizationCampus(createOrganizationCampusBto);
        mergeCampusAttributeValueList(mergeAttributeValueBtoList, result);
        return result;
    }

    private void validateOrganizationIdEffectiveness(String organizationId) {
        OrganizationBaseDto organizationBaseDto =
                organizationBaseDtoService.getById(organizationId);
        if (organizationBaseDto == null)
            throw new IgnoredException(ErrorCode.SYS_ERROR, "当前维护院区的所属机构不存在！");
        if (organizationBaseDto.getStatus().equals(OrganizationStatusEnum.DISABLE))
            throw new IgnoredException(ErrorCode.SYS_ERROR, "当前维护院区的所属机构未启用！");
    }

    /** 更新组织院区及扩展属性 */
    @Transactional
    @PublicInterface(id = "2938dda9-c510-42a6-a119-2d65a36037af", version = "1742785146739")
    @AutoGenerated(locked = false, uuid = "2938dda9-c510-42a6-a119-2d65a36037af")
    @RequestMapping(
            value = {"/api/organization/update-organization-campus"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String updateOrganizationCampus(
            @Valid UpdateOrganizationCampusBto updateOrganizationCampusBto,
            @Valid List<MergeAttributeValueBto> mergeAttributeValueBtoList) {
        // TODO implement method
        // 验证机构有效性
        validateOrganizationIdEffectiveness(updateOrganizationCampusBto.getParentId());
        // 设置默认值
        updateOrganizationCampusBto.setOrganizationLevel(2L);
        updateOrganizationCampusBto.setType(OrganizationTypeEnum.CAMPUS);
        String result = organizationBOService.updateOrganizationCampus(updateOrganizationCampusBto);
        mergeCampusAttributeValueList(mergeAttributeValueBtoList, result);
        return result;
    }

    /** 更新院区扩展属性 */
    private void mergeCampusAttributeValueList(
            List<MergeAttributeValueBto> mergeAttributeValueBtoList, String oganizationId) {
        OrganizationCampusDto organizationCampusDto =
                organizationCampusDtoService.getById(oganizationId);
        if (organizationCampusDto != null) {
            mergeAttributeValueBtoList.forEach(
                    o -> {
                        o.setEntityType("CAMPUS");
                        o.setEntityId(organizationCampusDto.getCampus().getId());
                    });
            attributeValueBOServiceInOrganizationRpcAdapter.mergeAttributeValueList(
                    mergeAttributeValueBtoList,
                    "CAMPUS",
                    organizationCampusDto.getCampus().getId());
        }
    }

    /** 查询组织院区列表 */
    @PublicInterface(id = "37f3342f-e429-4d29-bbfa-4feb7c8ad749", version = "1746774426333")
    @AutoGenerated(locked = false, uuid = "37f3342f-e429-4d29-bbfa-4feb7c8ad749")
    @RequestMapping(
            value = {"/api/organization/search-organization-campus"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<OrganizationCampusVo> searchOrganizationCampus(
            @Valid SearchOrganizationCampusQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<OrganizationCampusDto> rpcResult =
                organizationCampusDtoQueryService.searchOrganizationCampus(qto);
        List<OrganizationCampusVo> result =
                organizationCampusVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查找组织院区列表 */
    @PublicInterface(id = "5c3de8f1-df44-4397-89b9-3ed7d3d497dd", version = "1746781649143")
    @AutoGenerated(locked = false, uuid = "5c3de8f1-df44-4397-89b9-3ed7d3d497dd")
    @RequestMapping(
            value = {"/api/organization/search-organization-campus-waterfall"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<OrganizationCampusVo> searchOrganizationCampusWaterfall(
            @Valid SearchOrganizationCampusQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<OrganizationCampusDto> dtoResult =
                organizationCampusDtoQueryService.searchOrganizationCampusWaterfall(qto);
        VSQueryResult<OrganizationCampusVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(
                organizationCampusVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 更新组织院区状态 */
    @PublicInterface(id = "9b860b0b-73f5-4731-af78-d2e05068561f", version = "1742795143544")
    @AutoGenerated(locked = false, uuid = "9b860b0b-73f5-4731-af78-d2e05068561f")
    @RequestMapping(
            value = {"/api/organization/update-organization-campus-status"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String updateOrganizationCampusStatus(
            @Valid UpdateOrganizationStatusBto updateOrganizationCampusStatusBto) {
        // TODO implement method
        String result =
                organizationBOService.updateOrganizationStatus(updateOrganizationCampusStatusBto);
        return result;
    }
}
