package com.pulse.organization.entrance.web.query.collector;

import com.pulse.organization.entrance.web.converter.OrganizationWardWithDepartmentListVoConverter;
import com.pulse.organization.entrance.web.converter.WardBaseVoConverter;
import com.pulse.organization.entrance.web.query.assembler.OrganizationWardWithDepartmentListVoDataAssembler.OrganizationWardWithDepartmentListVoDataHolder;
import com.pulse.organization.entrance.web.vo.WardBaseVo;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.OrganizationWardWithDepartmentListDto;
import com.pulse.organization.manager.dto.WardBaseDto;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.pulse.organization.service.WardBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装OrganizationWardWithDepartmentListVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "61eb87fb-36ae-3daa-b40c-ebe29913c4bf")
public class OrganizationWardWithDepartmentListVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationWardWithDepartmentListVoConverter
            organizationWardWithDepartmentListVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationWardWithDepartmentListVoDataCollector
            organizationWardWithDepartmentListVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private WardBaseDtoService wardBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private WardBaseVoConverter wardBaseVoConverter;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "25096fae-ab21-3a31-815c-9357b7bd25f0")
    private void fillDataWhenNecessary(OrganizationWardWithDepartmentListVoDataHolder dataHolder) {
        List<OrganizationBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.ward == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(OrganizationBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<WardBaseDto> baseDtoList =
                    wardBaseDtoService.getByOrganizationIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(WardBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<WardBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(WardBaseDto::getOrganizationId));
            Map<WardBaseDto, WardBaseVo> dtoVoMap =
                    wardBaseVoConverter.convertToWardBaseVoMap(baseDtoList);
            Map<WardBaseDto, WardBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.ward =
                    rootDtoList.stream()
                            .map(OrganizationBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /**
     * 获取OrganizationWardWithDepartmentListDto数据填充OrganizationWardWithDepartmentListVo，并根据扩展关系填充剩余数据
     */
    @AutoGenerated(locked = true, uuid = "33a690d7-04a7-307c-b58a-ef2e90af3822")
    public void collectDataWithDtoData(
            List<OrganizationWardWithDepartmentListDto> dtoList,
            OrganizationWardWithDepartmentListVoDataHolder dataHolder) {
        List<WardBaseDto> wardList = new ArrayList<>();

        for (OrganizationWardWithDepartmentListDto rootDto : dtoList) {
            WardBaseDto wardDto = rootDto.getWard();
            if (wardDto != null) {
                wardList.add(wardDto);
            }
        }

        // access ward
        Map<WardBaseDto, WardBaseVo> wardVoMap =
                wardBaseVoConverter.convertToWardBaseVoMap(wardList);
        dataHolder.ward =
                wardList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> wardVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "42deddb6-9e0c-3b67-bc1b-4c7de32be7d0")
    public void collectDataDefault(OrganizationWardWithDepartmentListVoDataHolder dataHolder) {
        organizationWardWithDepartmentListVoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
