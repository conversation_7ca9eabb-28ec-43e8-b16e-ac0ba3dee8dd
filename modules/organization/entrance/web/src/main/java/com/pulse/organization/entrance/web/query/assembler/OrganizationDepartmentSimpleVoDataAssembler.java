package com.pulse.organization.entrance.web.query.assembler;

import com.pulse.organization.entrance.web.vo.DepartmentSimpleVo;
import com.pulse.organization.entrance.web.vo.OrganizationDepartmentSimpleVo;
import com.pulse.organization.manager.dto.DepartmentBaseDto;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** OrganizationDepartmentSimpleVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "fdc35f2c-65a5-3d53-948f-359774675b28")
public class OrganizationDepartmentSimpleVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    /** 组装OrganizationDepartmentSimpleVo数据 */
    @AutoGenerated(locked = true, uuid = "872c52bb-f44e-3a99-ba35-6511f03d3514")
    public void assembleData(
            Map<String, OrganizationDepartmentSimpleVo> voMap,
            OrganizationDepartmentSimpleVoDataAssembler.OrganizationDepartmentSimpleVoDataHolder
                    dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<OrganizationBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, Pair<DepartmentBaseDto, DepartmentSimpleVo>> department =
                dataHolder.department.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getOrganizationId(),
                                        dto -> Pair.of(dto, dataHolder.department.get(dto)),
                                        (o1, o2) -> o1));

        for (OrganizationBaseDto baseDto : baseDtoList) {
            OrganizationDepartmentSimpleVo vo = voMap.get(baseDto.getId());
            vo.setDepartment(
                    Optional.ofNullable(department.get(baseDto.getId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装OrganizationDepartmentSimpleVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "aaa3b251-7807-3399-8b10-dd370836e41f")
    public void assembleDataCustomized(List<OrganizationDepartmentSimpleVo> dataList) {
        // 自定义数据组装

    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class OrganizationDepartmentSimpleVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<OrganizationBaseDto> rootBaseDtoList;

        /** 持有字段department的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DepartmentBaseDto, DepartmentSimpleVo> department;
    }
}
