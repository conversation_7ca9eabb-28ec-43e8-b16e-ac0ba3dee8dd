package com.pulse.organization.entrance.web.query.collector;

import com.pulse.organization.entrance.web.converter.OrganizationBaseVoConverter;
import com.pulse.organization.entrance.web.converter.TeamOrganizationVoConverter;
import com.pulse.organization.entrance.web.query.assembler.TeamOrganizationVoDataAssembler.TeamOrganizationVoDataHolder;
import com.pulse.organization.entrance.web.vo.OrganizationBaseVo;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.TeamOrganizationBaseDto;
import com.pulse.organization.manager.dto.TeamOrganizationDto;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.pulse.organization.service.TeamOrganizationBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装TeamOrganizationVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "17f1e289-03c2-3cde-9877-a1c65a1bdfdb")
public class TeamOrganizationVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseVoConverter organizationBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TeamOrganizationBaseDtoService teamOrganizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TeamOrganizationVoConverter teamOrganizationVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TeamOrganizationVoDataCollector teamOrganizationVoDataCollector;

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "067d0887-6836-302a-9d5c-846e4f9eea21")
    public void collectDataDefault(TeamOrganizationVoDataHolder dataHolder) {
        teamOrganizationVoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 获取TeamOrganizationDto数据填充TeamOrganizationVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "b11af35e-803a-3fa5-a335-84e50aec12e5")
    public void collectDataWithDtoData(
            List<TeamOrganizationDto> dtoList, TeamOrganizationVoDataHolder dataHolder) {
        List<OrganizationBaseDto> organizationList = new ArrayList<>();

        for (TeamOrganizationDto rootDto : dtoList) {
            OrganizationBaseDto organizationDto = rootDto.getOrganization();
            if (organizationDto != null) {
                organizationList.add(organizationDto);
            }
        }

        // access organization
        Map<OrganizationBaseDto, OrganizationBaseVo> organizationVoMap =
                organizationBaseVoConverter.convertToOrganizationBaseVoMap(organizationList);
        dataHolder.organization =
                organizationList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> organizationVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "e2f16523-e8ec-3ff6-a00b-b8915501b67c")
    private void fillDataWhenNecessary(TeamOrganizationVoDataHolder dataHolder) {
        List<TeamOrganizationBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.organization == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(TeamOrganizationBaseDto::getOrganizationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            Map<OrganizationBaseDto, OrganizationBaseVo> dtoVoMap =
                    organizationBaseVoConverter.convertToOrganizationBaseVoMap(baseDtoList);
            Map<OrganizationBaseDto, OrganizationBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.organization =
                    rootDtoList.stream()
                            .map(TeamOrganizationBaseDto::getOrganizationId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }
}
