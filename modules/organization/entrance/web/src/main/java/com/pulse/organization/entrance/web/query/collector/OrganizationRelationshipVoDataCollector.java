package com.pulse.organization.entrance.web.query.collector;

import com.pulse.organization.entrance.web.converter.OrganizationRelationshipVoConverter;
import com.pulse.organization.entrance.web.converter.OrganizationSimpleVoConverter;
import com.pulse.organization.entrance.web.query.assembler.OrganizationRelationshipVoDataAssembler.OrganizationRelationshipVoDataHolder;
import com.pulse.organization.entrance.web.vo.OrganizationSimpleVo;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.OrganizationRelationshipBaseDto;
import com.pulse.organization.manager.dto.OrganizationRelationshipDto;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.pulse.organization.service.OrganizationRelationshipBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装OrganizationRelationshipVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "2f6c50c6-94d7-3d5a-96ce-d7de85eb895f")
public class OrganizationRelationshipVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationRelationshipBaseDtoService organizationRelationshipBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationRelationshipVoConverter organizationRelationshipVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationRelationshipVoDataCollector organizationRelationshipVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationSimpleVoConverter organizationSimpleVoConverter;

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "39551088-3ddb-3654-8d14-e1f35b25baaf")
    public void collectDataDefault(OrganizationRelationshipVoDataHolder dataHolder) {
        organizationRelationshipVoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "49a13224-e8c3-3ce6-93f4-5f05cdcbeacd")
    private void fillDataWhenNecessary(OrganizationRelationshipVoDataHolder dataHolder) {
        List<OrganizationRelationshipBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.sourceOrganization == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(OrganizationRelationshipBaseDto::getSourceOrganizationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            Map<OrganizationBaseDto, OrganizationSimpleVo> dtoVoMap =
                    organizationSimpleVoConverter.convertToOrganizationSimpleVoMap(baseDtoList);
            Map<OrganizationBaseDto, OrganizationSimpleVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.sourceOrganization =
                    rootDtoList.stream()
                            .map(OrganizationRelationshipBaseDto::getSourceOrganizationId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.targetOrganization == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(OrganizationRelationshipBaseDto::getTargetOrganizationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            Map<OrganizationBaseDto, OrganizationSimpleVo> dtoVoMap =
                    organizationSimpleVoConverter.convertToOrganizationSimpleVoMap(baseDtoList);
            Map<OrganizationBaseDto, OrganizationSimpleVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.targetOrganization =
                    rootDtoList.stream()
                            .map(OrganizationRelationshipBaseDto::getTargetOrganizationId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** 获取OrganizationRelationshipDto数据填充OrganizationRelationshipVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "73a449a1-c8d3-38cf-9d90-acef4c9f8056")
    public void collectDataWithDtoData(
            List<OrganizationRelationshipDto> dtoList,
            OrganizationRelationshipVoDataHolder dataHolder) {
        List<OrganizationBaseDto> sourceOrganizationList = new ArrayList<>();
        List<OrganizationBaseDto> targetOrganizationList = new ArrayList<>();

        for (OrganizationRelationshipDto rootDto : dtoList) {
            OrganizationBaseDto sourceOrganizationDto = rootDto.getSourceOrganization();
            if (sourceOrganizationDto != null) {
                sourceOrganizationList.add(sourceOrganizationDto);
            }
            OrganizationBaseDto targetOrganizationDto = rootDto.getTargetOrganization();
            if (targetOrganizationDto != null) {
                targetOrganizationList.add(targetOrganizationDto);
            }
        }

        // access sourceOrganization
        Map<OrganizationBaseDto, OrganizationSimpleVo> sourceOrganizationVoMap =
                organizationSimpleVoConverter.convertToOrganizationSimpleVoMap(
                        sourceOrganizationList);
        dataHolder.sourceOrganization =
                sourceOrganizationList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> sourceOrganizationVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access targetOrganization
        Map<OrganizationBaseDto, OrganizationSimpleVo> targetOrganizationVoMap =
                organizationSimpleVoConverter.convertToOrganizationSimpleVoMap(
                        targetOrganizationList);
        dataHolder.targetOrganization =
                targetOrganizationList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> targetOrganizationVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }
}
