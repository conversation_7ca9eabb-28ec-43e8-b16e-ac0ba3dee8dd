package com.pulse.organization.entrance.web.query.assembler;

import com.pulse.application.manager.dto.ApplicationBaseDto;
import com.pulse.application.manager.dto.ApplicationOrganizationDto;
import com.pulse.organization.entrance.web.vo.ApplicationOrganizationCampusVo;
import com.pulse.organization.manager.facade.application.ApplicationOrganizationDtoServiceInOrganizationRpcAdapter;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** ApplicationOrganizationCampusVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "1700895b-da5d-31c1-8e5f-cd166db23f69")
public class ApplicationOrganizationCampusVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private ApplicationOrganizationDtoServiceInOrganizationRpcAdapter
            applicationOrganizationDtoServiceInOrganizationRpcAdapter;

    /** 组装ApplicationOrganizationCampusVo数据 */
    @AutoGenerated(locked = true, uuid = "4b2c13e9-0c49-36a2-bd83-0fca572a3d08")
    public void assembleData(
            Map<String, ApplicationOrganizationCampusVo> voMap,
            ApplicationOrganizationCampusVoDataAssembler.ApplicationOrganizationCampusVoDataHolder
                    dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<ApplicationOrganizationDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, Pair<ApplicationBaseDto, ApplicationOrganizationCampusVo.ApplicationBaseVo>>
                application =
                        dataHolder.application.keySet().stream()
                                .collect(
                                        Collectors.toMap(
                                                dto -> dto.getId(),
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder.application.get(dto)),
                                                (o1, o2) -> o1));

        for (ApplicationOrganizationDto baseDto : baseDtoList) {
            ApplicationOrganizationCampusVo vo = voMap.get(baseDto.getId());
            vo.setApplication(
                    Optional.ofNullable(application.get(baseDto.getApplicationId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装ApplicationOrganizationCampusVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "503dc3ca-5d90-3512-ac8f-2d17daf4ac55")
    public void assembleDataCustomized(List<ApplicationOrganizationCampusVo> dataList) {
        // 自定义数据组装

    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class ApplicationOrganizationCampusVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<ApplicationOrganizationDto> rootBaseDtoList;

        /** 持有字段application的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<ApplicationBaseDto, ApplicationOrganizationCampusVo.ApplicationBaseVo>
                application;
    }
}
