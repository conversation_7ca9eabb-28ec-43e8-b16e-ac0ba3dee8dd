package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.application.manager.dto.ApplicationInfoOrganizationDto;
import com.pulse.organization.entrance.web.query.assembler.OrganizationCampusApplicationVoDataAssembler;
import com.pulse.organization.entrance.web.query.assembler.OrganizationCampusApplicationVoDataAssembler.OrganizationCampusApplicationVoDataHolder;
import com.pulse.organization.entrance.web.query.collector.OrganizationCampusApplicationVoDataCollector;
import com.pulse.organization.entrance.web.vo.ApplicationOrganizationCampusVo;
import com.pulse.organization.entrance.web.vo.OrganizationCampusApplicationVo;
import com.pulse.organization.manager.dto.OrganizationCampusApplicationDto;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到OrganizationCampusApplicationVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "045a9462-c94d-4bab-b3da-f994466e7e45|VO|CONVERTER")
public class OrganizationCampusApplicationVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private ApplicationOrganizationCampusVoConverter applicationOrganizationCampusVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationCampusApplicationVoDataAssembler
            organizationCampusApplicationVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationCampusApplicationVoDataCollector
            organizationCampusApplicationVoDataCollector;

    /** 把OrganizationCampusApplicationDto转换成OrganizationCampusApplicationVo */
    @AutoGenerated(locked = false, uuid = "045a9462-c94d-4bab-b3da-f994466e7e45-converter-Map")
    public Map<OrganizationCampusApplicationDto, OrganizationCampusApplicationVo>
            convertToOrganizationCampusApplicationVoMap(
                    List<OrganizationCampusApplicationDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<ApplicationInfoOrganizationDto, ApplicationOrganizationCampusVo>
                applicationInfoOrganizationListMap =
                        applicationOrganizationCampusVoConverter
                                .convertToApplicationOrganizationCampusVoMap(
                                        dtoList.stream()
                                                .filter(
                                                        dto ->
                                                                CollectionUtil.isNotEmpty(
                                                                        dto
                                                                                .getApplicationInfoOrganizationList()))
                                                .flatMap(
                                                        dto ->
                                                                dto
                                                                        .getApplicationInfoOrganizationList()
                                                                        .stream())
                                                .filter(Objects::nonNull)
                                                .collect(Collectors.toList()));
        Map<OrganizationCampusApplicationDto, OrganizationCampusApplicationVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            OrganizationCampusApplicationVo vo =
                                                    new OrganizationCampusApplicationVo();
                                            vo.setId(dto.getId());
                                            vo.setParentId(dto.getParentId());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setDescription(dto.getDescription());
                                            vo.setName(dto.getName());
                                            vo.setOrganizationLevel(dto.getOrganizationLevel());
                                            vo.setSortNumber(dto.getSortNumber());
                                            vo.setStatus(dto.getStatus());
                                            vo.setType(dto.getType());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setAddress(dto.getAddress());
                                            vo.setContactPerson(dto.getContactPerson());
                                            vo.setInputCode(dto.getInputCode());
                                            vo.setContactNumber(dto.getContactNumber());
                                            vo.setAbbreviation(dto.getAbbreviation());
                                            vo.setDeletedBy(dto.getDeletedBy());
                                            vo.setAlias(dto.getAlias());
                                            vo.setEnglishName(dto.getEnglishName());
                                            vo.setInvalidFlag(dto.getInvalidFlag());
                                            vo.setApplicationInfoOrganizationList(
                                                    dto.getApplicationInfoOrganizationList() == null
                                                            ? null
                                                            : dto
                                                                    .getApplicationInfoOrganizationList()
                                                                    .stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    applicationInfoOrganizationListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把OrganizationCampusApplicationDto转换成OrganizationCampusApplicationVo */
    @AutoGenerated(locked = true, uuid = "045a9462-c94d-4bab-b3da-f994466e7e45-converter-list")
    public List<OrganizationCampusApplicationVo> convertToOrganizationCampusApplicationVoList(
            List<OrganizationCampusApplicationDto> dtoList) {
        return new ArrayList<>(convertToOrganizationCampusApplicationVoMap(dtoList).values());
    }

    /** 使用默认方式组装OrganizationCampusApplicationVo列表数据 */
    @AutoGenerated(locked = true, uuid = "1f6be37d-7597-31b9-bf56-d7bdb61e9949")
    public List<OrganizationCampusApplicationVo> convertAndAssembleDataList(
            List<OrganizationCampusApplicationDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        OrganizationCampusApplicationVoDataHolder dataHolder =
                new OrganizationCampusApplicationVoDataHolder();
        dataHolder.setRootBaseDtoList(
                organizationBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(OrganizationCampusApplicationDto::getId)
                                .collect(Collectors.toList())));
        Map<String, OrganizationCampusApplicationVo> voMap =
                convertToOrganizationCampusApplicationVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        organizationCampusApplicationVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        organizationCampusApplicationVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 使用默认方式组装OrganizationCampusApplicationVo数据 */
    @AutoGenerated(locked = true, uuid = "5e43b913-ea22-3c75-b337-e0b68d55f019")
    public OrganizationCampusApplicationVo convertAndAssembleData(
            OrganizationCampusApplicationDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把OrganizationCampusApplicationDto转换成OrganizationCampusApplicationVo */
    @AutoGenerated(locked = true, uuid = "da4b5ebe-ce8e-3fad-8b53-3b131b8f90a8")
    public OrganizationCampusApplicationVo convertToOrganizationCampusApplicationVo(
            OrganizationCampusApplicationDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToOrganizationCampusApplicationVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }
}
