package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.entrance.web.query.assembler.InstitutionBaseVoDataAssembler;
import com.pulse.organization.entrance.web.vo.InstitutionBaseVo;
import com.pulse.organization.manager.dto.InstitutionBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到InstitutionBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "ec9b417e-4f0e-409e-91d0-624400dede71|VO|CONVERTER")
public class InstitutionBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private InstitutionBaseVoDataAssembler institutionBaseVoDataAssembler;

    /** 使用默认方式组装InstitutionBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "1022a4fc-71dc-3a01-9be0-b52f64c6cce1")
    public InstitutionBaseVo convertAndAssembleData(InstitutionBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装InstitutionBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "6064e1a3-fa85-3e7a-a917-73abd77c66e8")
    public List<InstitutionBaseVo> convertAndAssembleDataList(List<InstitutionBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, InstitutionBaseVo> voMap =
                convertToInstitutionBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        institutionBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把InstitutionBaseDto转换成InstitutionBaseVo */
    @AutoGenerated(locked = true, uuid = "95aaff00-2bf6-3c86-a8c5-877c15bc00db")
    public InstitutionBaseVo convertToInstitutionBaseVo(InstitutionBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToInstitutionBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把InstitutionBaseDto转换成InstitutionBaseVo */
    @AutoGenerated(locked = false, uuid = "ec9b417e-4f0e-409e-91d0-624400dede71-converter-Map")
    public Map<InstitutionBaseDto, InstitutionBaseVo> convertToInstitutionBaseVoMap(
            List<InstitutionBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<InstitutionBaseDto, InstitutionBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            InstitutionBaseVo vo = new InstitutionBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setOrganizationId(dto.getOrganizationId());
                                            vo.setOwnershipType(dto.getOwnershipType());
                                            vo.setInstitutionType(dto.getInstitutionType());
                                            vo.setInstitutionLevel(dto.getInstitutionLevel());
                                            vo.setGrade(dto.getGrade());
                                            vo.setAffiliation(dto.getAffiliation());
                                            vo.setMedicalRecordInstitutionCode(
                                                    dto.getMedicalRecordInstitutionCode());
                                            vo.setMedicalInsuranceCode(
                                                    dto.getMedicalInsuranceCode());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setDeletedBy(dto.getDeletedBy());
                                            vo.setUnitCode(dto.getUnitCode());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把InstitutionBaseDto转换成InstitutionBaseVo */
    @AutoGenerated(locked = true, uuid = "ec9b417e-4f0e-409e-91d0-624400dede71-converter-list")
    public List<InstitutionBaseVo> convertToInstitutionBaseVoList(
            List<InstitutionBaseDto> dtoList) {
        return new ArrayList<>(convertToInstitutionBaseVoMap(dtoList).values());
    }
}
