package com.pulse.organization.entrance.web.controller;

import cn.hutool.core.collection.CollUtil;

import com.pulse.organization.entrance.web.converter.StaffConsultingRoomVoConverter;
import com.pulse.organization.entrance.web.vo.ConsultingRoomBaseVo;
import com.pulse.organization.entrance.web.vo.StaffConsultingRoomVo;
import com.pulse.organization.manager.dto.StaffConsultingRoomDto;
import com.pulse.organization.persist.qto.SearchStaffConsultingRoomQto;
import com.pulse.organization.service.StaffBOService;
import com.pulse.organization.service.bto.MergeStaffConsultingRoomBto;
import com.pulse.organization.service.bto.UpdateStaffConsultingRoomEnableFlagBto;
import com.pulse.organization.service.query.StaffConsultingRoomDtoQueryService;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "a18905da-570e-3159-92a5-172cd87feb07")
public class StaffConsultingRoomController {
    @AutoGenerated(locked = true)
    @Resource
    private StaffBOService staffBOService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffConsultingRoomDtoQueryService staffConsultingRoomDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffConsultingRoomVoConverter staffConsultingRoomVoConverter;

    /** 批量更新员工诊室启用状态 更新员工诊室启用状态 */
    @PublicInterface(id = "801e557c-5ad4-4d8b-bd25-77e9b4afa098", version = "1747039037915")
    @AutoGenerated(locked = false, uuid = "801e557c-5ad4-4d8b-bd25-77e9b4afa098")
    @RequestMapping(
            value = {"/api/organization/update-batch-staff-consulting-room-enable-flag"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<String> updateBatchStaffConsultingRoomEnableFlag(
            @NotNull String staffId, String campusId, Boolean enableFlag, String updatedBy) {
        // TODO implement method
        List<String> result = new ArrayList<>();
        SearchStaffConsultingRoomQto qto = new SearchStaffConsultingRoomQto();
        qto.setStaffIdIs(staffId);
        qto.setConsultingRoomCampusOrganizationIdIs(campusId);
        List<StaffConsultingRoomDto> rpcResult =
                staffConsultingRoomDtoQueryService.searchStaffConsultingRoom(qto);
        if (CollUtil.isNotEmpty(rpcResult)) {
            UpdateStaffConsultingRoomEnableFlagBto.StaffConsultingRoomBto bto =
                    new UpdateStaffConsultingRoomEnableFlagBto.StaffConsultingRoomBto();
            bto.setEnableFlag(enableFlag);
            bto.setUpdatedBy(updatedBy);
            for (StaffConsultingRoomDto dto : rpcResult) {
                bto.setId(dto.getId());
                result.add(staffBOService.updateStaffConsultingRoomEnableFlag(bto));
            }
        }
        return result;
    }

    /** 查询员工诊室列表 */
    @PublicInterface(id = "91244cd4-be34-434c-a58f-6dff46083c1c", version = "1747032929576")
    @AutoGenerated(locked = false, uuid = "91244cd4-be34-434c-a58f-6dff46083c1c")
    @RequestMapping(
            value = {"/api/organization/search-staff-consulting-room"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<StaffConsultingRoomVo> searchStaffConsultingRoom(
            @Valid SearchStaffConsultingRoomQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<StaffConsultingRoomDto> rpcResult =
                staffConsultingRoomDtoQueryService.searchStaffConsultingRoom(qto);
        List<StaffConsultingRoomVo> result =
                staffConsultingRoomVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        if (qto.getSplitByCampusIs()) {
            // 按院区分组，并合并同一个院区下的所有诊室的名称数据，并用分号
            Map<String, StaffConsultingRoomVo> groupedByCampus = new HashMap<>();
            for (StaffConsultingRoomVo vo : result) {
                // 拼接用于显示的名称
                ConsultingRoomBaseVo consultingRoom = vo.getConsultingRoom();
                if (consultingRoom == null) {
                    continue; // 跳过没有诊室信息的记录
                }
                String displayName =
                        (consultingRoom.getFloorUnit() != null ? consultingRoom.getFloorUnit() : "")
                                + (consultingRoom.getArea() != null ? consultingRoom.getArea() : "")
                                + (consultingRoom.getCode() != null
                                        ? consultingRoom.getCode()
                                        : "");
                String compositeKey =
                        vo.getStaff().getId()
                                + "-"
                                + vo.getConsultingRoom().getCampusOrganizationId();
                if (compositeKey == null) {
                    continue;
                }
                if (!groupedByCampus.containsKey(compositeKey)) {
                    groupedByCampus.put(compositeKey, vo);
                }

                StaffConsultingRoomVo groupedVo = groupedByCampus.get(compositeKey);
                if (groupedVo.getConsultingRoomSplicingName() == null) {
                    groupedVo.setConsultingRoomSplicingName(displayName); //
                } else {
                    groupedVo.setConsultingRoomSplicingName(
                            groupedVo.getConsultingRoomSplicingName() + ";" + displayName);
                }
            }
            result = new ArrayList<>(groupedByCampus.values());
        }
        return result;
    }

    /** 保存员工诊室信息列表 */
    @PublicInterface(id = "b16a6dcb-3e5b-402c-9684-3254753a140f", version = "1747036447726")
    @AutoGenerated(locked = false, uuid = "b16a6dcb-3e5b-402c-9684-3254753a140f")
    @RequestMapping(
            value = {"/api/organization/merge-staff-consulting-room"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeStaffConsultingRoom(
            @Valid MergeStaffConsultingRoomBto mergeStaffConsultingRoomBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = staffBOService.mergeStaffConsultingRoom(mergeStaffConsultingRoomBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }
}
