package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.application.manager.dto.ApplicationBaseDto;
import com.pulse.organization.entrance.web.query.assembler.StaffOrganizationVoDataAssembler;
import com.pulse.organization.entrance.web.query.assembler.StaffOrganizationVoDataAssembler.StaffOrganizationVoDataHolder;
import com.pulse.organization.entrance.web.query.collector.StaffOrganizationVoDataCollector;
import com.pulse.organization.entrance.web.vo.OrganizationBaseVo;
import com.pulse.organization.entrance.web.vo.OrganizationRefApplicationBaseVo;
import com.pulse.organization.entrance.web.vo.StaffOrganizationVo;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.StaffOrganizationDto;
import com.pulse.organization.service.StaffOrganizationBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到StaffOrganizationVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "3755a80e-cc11-4537-b529-952029a62f90|VO|CONVERTER")
public class StaffOrganizationVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseVoConverter organizationBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationRefApplicationBaseVoConverter organizationRefApplicationBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffOrganizationBaseDtoService staffOrganizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffOrganizationVoDataAssembler staffOrganizationVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private StaffOrganizationVoDataCollector staffOrganizationVoDataCollector;

    /** 把StaffOrganizationDto转换成StaffOrganizationVo */
    @AutoGenerated(locked = false, uuid = "3755a80e-cc11-4537-b529-952029a62f90-converter-Map")
    public Map<StaffOrganizationDto, StaffOrganizationVo> convertToStaffOrganizationVoMap(
            List<StaffOrganizationDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<OrganizationBaseDto, OrganizationBaseVo> organizationMap =
                organizationBaseVoConverter.convertToOrganizationBaseVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(StaffOrganizationDto::getOrganization)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<ApplicationBaseDto, OrganizationRefApplicationBaseVo> applicationMap =
                organizationRefApplicationBaseVoConverter
                        .convertToOrganizationRefApplicationBaseVoMap(
                                dtoList.stream()
                                        .filter(Objects::nonNull)
                                        .map(StaffOrganizationDto::getApplication)
                                        .filter(Objects::nonNull)
                                        .collect(Collectors.toList()));
        Map<StaffOrganizationDto, StaffOrganizationVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            StaffOrganizationVo vo = new StaffOrganizationVo();
                                            vo.setId(dto.getId());
                                            vo.setStaffId(dto.getStaffId());
                                            vo.setOrganization(
                                                    dto.getOrganization() == null
                                                            ? null
                                                            : organizationMap.get(
                                                                    dto.getOrganization()));
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setDeletedBy(dto.getDeletedBy());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setApplication(
                                                    dto.getApplication() == null
                                                            ? null
                                                            : applicationMap.get(
                                                                    dto.getApplication()));
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把StaffOrganizationDto转换成StaffOrganizationVo */
    @AutoGenerated(locked = true, uuid = "3755a80e-cc11-4537-b529-952029a62f90-converter-list")
    public List<StaffOrganizationVo> convertToStaffOrganizationVoList(
            List<StaffOrganizationDto> dtoList) {
        return new ArrayList<>(convertToStaffOrganizationVoMap(dtoList).values());
    }

    /** 把StaffOrganizationDto转换成StaffOrganizationVo */
    @AutoGenerated(locked = true, uuid = "6170abd8-eb2a-3554-b480-a69152fbd5c5")
    public StaffOrganizationVo convertToStaffOrganizationVo(StaffOrganizationDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToStaffOrganizationVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装StaffOrganizationVo数据 */
    @AutoGenerated(locked = true, uuid = "d58ff6bb-a9c8-3f36-9414-776bd09bfbe9")
    public StaffOrganizationVo convertAndAssembleData(StaffOrganizationDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装StaffOrganizationVo列表数据 */
    @AutoGenerated(locked = true, uuid = "d6307c5c-a282-3c38-84e4-9fe138e516d8")
    public List<StaffOrganizationVo> convertAndAssembleDataList(
            List<StaffOrganizationDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        StaffOrganizationVoDataHolder dataHolder = new StaffOrganizationVoDataHolder();
        dataHolder.setRootBaseDtoList(
                staffOrganizationBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(StaffOrganizationDto::getId)
                                .collect(Collectors.toList())));
        Map<String, StaffOrganizationVo> voMap =
                convertToStaffOrganizationVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        staffOrganizationVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        staffOrganizationVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
