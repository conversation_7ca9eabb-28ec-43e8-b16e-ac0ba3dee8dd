package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.entrance.web.query.assembler.StaffDetailVoDataAssembler;
import com.pulse.organization.entrance.web.query.assembler.StaffDetailVoDataAssembler.StaffDetailVoDataHolder;
import com.pulse.organization.entrance.web.query.collector.StaffDetailVoDataCollector;
import com.pulse.organization.entrance.web.vo.OrganizationSimpleVo;
import com.pulse.organization.entrance.web.vo.StaffDetailVo;
import com.pulse.organization.entrance.web.vo.StaffEducationBaseVo;
import com.pulse.organization.entrance.web.vo.StaffEmploymentBaseVo;
import com.pulse.organization.entrance.web.vo.StaffExtensionBaseVo;
import com.pulse.organization.entrance.web.vo.StaffOrganizationVo;
import com.pulse.organization.entrance.web.vo.StaffPracticeBaseVo;
import com.pulse.organization.entrance.web.vo.StaffUserVo;
import com.pulse.organization.entrance.web.vo.StaffWorkExperienceBaseVo;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.StaffDetailDto;
import com.pulse.organization.manager.dto.StaffEducationBaseDto;
import com.pulse.organization.manager.dto.StaffEmploymentBaseDto;
import com.pulse.organization.manager.dto.StaffExtensionBaseDto;
import com.pulse.organization.manager.dto.StaffOrganizationDto;
import com.pulse.organization.manager.dto.StaffPracticeBaseDto;
import com.pulse.organization.manager.dto.StaffUserDto;
import com.pulse.organization.manager.dto.StaffWorkExperienceBaseDto;
import com.pulse.organization.service.StaffBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到StaffDetailVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "c9e7dba8-3119-48b1-b676-647c1351d9a5|VO|CONVERTER")
public class StaffDetailVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationSimpleVoConverter organizationSimpleVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffBaseDtoService staffBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffDetailVoDataAssembler staffDetailVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private StaffDetailVoDataCollector staffDetailVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private StaffEducationBaseVoConverter staffEducationBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffEmploymentBaseVoConverter staffEmploymentBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffExtensionBaseVoConverter staffExtensionBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffOrganizationVoConverter staffOrganizationVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffPracticeBaseVoConverter staffPracticeBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffUserVoConverter staffUserVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffWorkExperienceBaseVoConverter staffWorkExperienceBaseVoConverter;

    /** 使用默认方式组装StaffDetailVo数据 */
    @AutoGenerated(locked = true, uuid = "06553ec9-12a6-38d6-bd23-1693eeae75c6")
    public StaffDetailVo convertAndAssembleData(StaffDetailDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装StaffDetailVo列表数据 */
    @AutoGenerated(locked = true, uuid = "138fde2a-50d8-3187-9bff-53387e1c665c")
    public List<StaffDetailVo> convertAndAssembleDataList(List<StaffDetailDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        StaffDetailVoDataHolder dataHolder = new StaffDetailVoDataHolder();
        dataHolder.setRootBaseDtoList(
                staffBaseDtoService.getByIds(
                        dtoList.stream().map(StaffDetailDto::getId).collect(Collectors.toList())));
        Map<String, StaffDetailVo> voMap =
                convertToStaffDetailVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        staffDetailVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        staffDetailVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把StaffDetailDto转换成StaffDetailVo */
    @AutoGenerated(locked = false, uuid = "c9e7dba8-3119-48b1-b676-647c1351d9a5-converter-Map")
    public Map<StaffDetailDto, StaffDetailVo> convertToStaffDetailVoMap(
            List<StaffDetailDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<StaffOrganizationDto, StaffOrganizationVo> staffLoginOrganizationDetailListMap =
                staffOrganizationVoConverter.convertToStaffOrganizationVoMap(
                        dtoList.stream()
                                .filter(
                                        dto ->
                                                CollectionUtil.isNotEmpty(
                                                        dto.getStaffLoginOrganizationDetailList()))
                                .flatMap(dto -> dto.getStaffLoginOrganizationDetailList().stream())
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<StaffUserDto, StaffUserVo> staffUserListMap =
                staffUserVoConverter.convertToStaffUserVoMap(
                        dtoList.stream()
                                .filter(dto -> CollectionUtil.isNotEmpty(dto.getStaffUserList()))
                                .flatMap(dto -> dto.getStaffUserList().stream())
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<OrganizationBaseDto, OrganizationSimpleVo> organizationMap =
                organizationSimpleVoConverter.convertToOrganizationSimpleVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(StaffDetailDto::getOrganization)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<OrganizationBaseDto, OrganizationSimpleVo> accountingOrganizationMap =
                organizationSimpleVoConverter.convertToOrganizationSimpleVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(StaffDetailDto::getAccountingOrganization)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<OrganizationBaseDto, OrganizationSimpleVo> hrOrganizationMap =
                organizationSimpleVoConverter.convertToOrganizationSimpleVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(StaffDetailDto::getHrOrganization)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<StaffEducationBaseDto, StaffEducationBaseVo> staffEducationListMap =
                staffEducationBaseVoConverter.convertToStaffEducationBaseVoMap(
                        dtoList.stream()
                                .filter(
                                        dto ->
                                                CollectionUtil.isNotEmpty(
                                                        dto.getStaffEducationList()))
                                .flatMap(dto -> dto.getStaffEducationList().stream())
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<StaffEmploymentBaseDto, StaffEmploymentBaseVo> staffEmploymentListMap =
                staffEmploymentBaseVoConverter.convertToStaffEmploymentBaseVoMap(
                        dtoList.stream()
                                .filter(
                                        dto ->
                                                CollectionUtil.isNotEmpty(
                                                        dto.getStaffEmploymentList()))
                                .flatMap(dto -> dto.getStaffEmploymentList().stream())
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<StaffExtensionBaseDto, StaffExtensionBaseVo> staffExtensionMap =
                staffExtensionBaseVoConverter.convertToStaffExtensionBaseVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(StaffDetailDto::getStaffExtension)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<StaffWorkExperienceBaseDto, StaffWorkExperienceBaseVo> staffWorkExperienceListMap =
                staffWorkExperienceBaseVoConverter.convertToStaffWorkExperienceBaseVoMap(
                        dtoList.stream()
                                .filter(
                                        dto ->
                                                CollectionUtil.isNotEmpty(
                                                        dto.getStaffWorkExperienceList()))
                                .flatMap(dto -> dto.getStaffWorkExperienceList().stream())
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<StaffPracticeBaseDto, StaffPracticeBaseVo> staffPracticeListMap =
                staffPracticeBaseVoConverter.convertToStaffPracticeBaseVoMap(
                        dtoList.stream()
                                .filter(
                                        dto ->
                                                CollectionUtil.isNotEmpty(
                                                        dto.getStaffPracticeList()))
                                .flatMap(dto -> dto.getStaffPracticeList().stream())
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<StaffDetailDto, StaffDetailVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            StaffDetailVo vo = new StaffDetailVo();
                                            vo.setId(dto.getId());
                                            vo.setBirthDate(dto.getBirthDate());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setDescription(dto.getDescription());
                                            vo.setEmailAddress(dto.getEmailAddress());
                                            vo.setGender(dto.getGender());
                                            vo.setCertificateNumber(dto.getCertificateNumber());
                                            vo.setInputCode(dto.getInputCode());
                                            vo.setName(dto.getName());
                                            vo.setPhoneNumber(dto.getPhoneNumber());
                                            vo.setStaffNumber(dto.getStaffNumber());
                                            vo.setStatus(dto.getStatus());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setShortPhoneNumber(dto.getShortPhoneNumber());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setUpdateBy(dto.getUpdateBy());
                                            vo.setDeletedBy(dto.getDeletedBy());
                                            vo.setCertificateTypeId(dto.getCertificateTypeId());
                                            vo.setStaffTypeId(dto.getStaffTypeId());
                                            vo.setExpertFlag(dto.getExpertFlag());
                                            vo.setProfessionalTitleId(dto.getProfessionalTitleId());
                                            vo.setSortNumber(dto.getSortNumber());
                                            vo.setStaffLoginOrganizationDetailList(
                                                    dto.getStaffLoginOrganizationDetailList()
                                                                    == null
                                                            ? null
                                                            : dto
                                                                    .getStaffLoginOrganizationDetailList()
                                                                    .stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    staffLoginOrganizationDetailListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            vo.setStaffUserList(
                                                    dto.getStaffUserList() == null
                                                            ? null
                                                            : dto.getStaffUserList().stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    staffUserListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            vo.setOrganization(
                                                    dto.getOrganization() == null
                                                            ? null
                                                            : organizationMap.get(
                                                                    dto.getOrganization()));
                                            vo.setAccountingOrganization(
                                                    dto.getAccountingOrganization() == null
                                                            ? null
                                                            : accountingOrganizationMap.get(
                                                                    dto
                                                                            .getAccountingOrganization()));
                                            vo.setHrOrganization(
                                                    dto.getHrOrganization() == null
                                                            ? null
                                                            : hrOrganizationMap.get(
                                                                    dto.getHrOrganization()));
                                            vo.setStaffEducationList(
                                                    dto.getStaffEducationList() == null
                                                            ? null
                                                            : dto.getStaffEducationList().stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    staffEducationListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            vo.setStaffEmploymentList(
                                                    dto.getStaffEmploymentList() == null
                                                            ? null
                                                            : dto.getStaffEmploymentList().stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    staffEmploymentListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            vo.setStaffExtension(
                                                    dto.getStaffExtension() == null
                                                            ? null
                                                            : staffExtensionMap.get(
                                                                    dto.getStaffExtension()));
                                            vo.setStaffWorkExperienceList(
                                                    dto.getStaffWorkExperienceList() == null
                                                            ? null
                                                            : dto
                                                                    .getStaffWorkExperienceList()
                                                                    .stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    staffWorkExperienceListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            vo.setStaffPracticeList(
                                                    dto.getStaffPracticeList() == null
                                                            ? null
                                                            : dto.getStaffPracticeList().stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    staffPracticeListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            vo.setPositionId(dto.getPositionId());
                                            vo.setPromotionDate(dto.getPromotionDate());
                                            vo.setRegisterTypeList(dto.getRegisterTypeList());
                                            vo.setProvincePlatformCancelFlag(
                                                    dto.getProvincePlatformCancelFlag());
                                            vo.setCountrysideStartDate(
                                                    dto.getCountrysideStartDate());
                                            vo.setDoctorRemark(dto.getDoctorRemark());
                                            vo.setRegisterDoctorFlag(dto.getRegisterDoctorFlag());
                                            vo.setCountrysideEndDate(dto.getCountrysideEndDate());
                                            vo.setRegisterDoctorEnableFlag(
                                                    dto.getRegisterDoctorEnableFlag());
                                            vo.setCampusOrganizationId(
                                                    dto.getCampusOrganizationId());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把StaffDetailDto转换成StaffDetailVo */
    @AutoGenerated(locked = true, uuid = "c9e7dba8-3119-48b1-b676-647c1351d9a5-converter-list")
    public List<StaffDetailVo> convertToStaffDetailVoList(List<StaffDetailDto> dtoList) {
        return new ArrayList<>(convertToStaffDetailVoMap(dtoList).values());
    }

    /** 把StaffDetailDto转换成StaffDetailVo */
    @AutoGenerated(locked = true, uuid = "d19de7c4-6e32-344f-a31d-e183aafb94bc")
    public StaffDetailVo convertToStaffDetailVo(StaffDetailDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToStaffDetailVoList(List.of(dto)).stream().findAny().orElse(null);
    }
}
