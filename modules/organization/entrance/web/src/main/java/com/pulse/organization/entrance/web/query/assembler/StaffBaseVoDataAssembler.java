package com.pulse.organization.entrance.web.query.assembler;

import com.pulse.organization.entrance.web.vo.StaffBaseVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** StaffBaseVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "1e3eee8a-6b6c-3e51-84b2-47b96d239f9f")
public class StaffBaseVoDataAssembler {

    /** 组装StaffBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "82874ef4-7b21-3a67-8d5a-073de4dcf7f8")
    public void assembleData(Map<String, StaffBaseVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装StaffBaseVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "db33c258-8a54-3332-bd04-74e69f252467")
    public void assembleDataCustomized(List<StaffBaseVo> dataList) {
        // 自定义数据组装

    }
}
