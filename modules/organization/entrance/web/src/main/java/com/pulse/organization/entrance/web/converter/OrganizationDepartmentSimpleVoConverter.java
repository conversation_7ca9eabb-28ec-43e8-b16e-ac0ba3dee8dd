package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.entrance.web.query.assembler.OrganizationDepartmentSimpleVoDataAssembler;
import com.pulse.organization.entrance.web.query.assembler.OrganizationDepartmentSimpleVoDataAssembler.OrganizationDepartmentSimpleVoDataHolder;
import com.pulse.organization.entrance.web.query.collector.OrganizationDepartmentSimpleVoDataCollector;
import com.pulse.organization.entrance.web.vo.DepartmentSimpleVo;
import com.pulse.organization.entrance.web.vo.OrganizationDepartmentSimpleVo;
import com.pulse.organization.manager.dto.DepartmentDto;
import com.pulse.organization.manager.dto.OrganizationDepartmentDto;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到OrganizationDepartmentSimpleVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "9c0c84c0-ad5b-4ddd-9d6d-e1a3bd303b0e|VO|CONVERTER")
public class OrganizationDepartmentSimpleVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private DepartmentSimpleVoConverter departmentSimpleVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationDepartmentSimpleVoDataAssembler organizationDepartmentSimpleVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationDepartmentSimpleVoDataCollector organizationDepartmentSimpleVoDataCollector;

    /** 使用默认方式组装OrganizationDepartmentSimpleVo列表数据 */
    @AutoGenerated(locked = true, uuid = "11be57f3-166a-3142-b180-5b67e9097940")
    public List<OrganizationDepartmentSimpleVo> convertAndAssembleDataList(
            List<OrganizationDepartmentDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        OrganizationDepartmentSimpleVoDataHolder dataHolder =
                new OrganizationDepartmentSimpleVoDataHolder();
        dataHolder.setRootBaseDtoList(
                organizationBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(OrganizationDepartmentDto::getId)
                                .collect(Collectors.toList())));
        Map<String, OrganizationDepartmentSimpleVo> voMap =
                convertToOrganizationDepartmentSimpleVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        organizationDepartmentSimpleVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        organizationDepartmentSimpleVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把OrganizationDepartmentDto转换成OrganizationDepartmentSimpleVo */
    @AutoGenerated(locked = true, uuid = "4b2c2555-e6f4-3a2d-9753-22bbd4a86ed6")
    public OrganizationDepartmentSimpleVo convertToOrganizationDepartmentSimpleVo(
            OrganizationDepartmentDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToOrganizationDepartmentSimpleVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 使用默认方式组装OrganizationDepartmentSimpleVo数据 */
    @AutoGenerated(locked = true, uuid = "6a7e8231-3334-3b54-b8c9-77e8011b7df8")
    public OrganizationDepartmentSimpleVo convertAndAssembleData(OrganizationDepartmentDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把OrganizationDepartmentDto转换成OrganizationDepartmentSimpleVo */
    @AutoGenerated(locked = false, uuid = "9c0c84c0-ad5b-4ddd-9d6d-e1a3bd303b0e-converter-Map")
    public Map<OrganizationDepartmentDto, OrganizationDepartmentSimpleVo>
            convertToOrganizationDepartmentSimpleVoMap(List<OrganizationDepartmentDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<DepartmentDto, DepartmentSimpleVo> departmentMap =
                departmentSimpleVoConverter.convertToDepartmentSimpleVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(OrganizationDepartmentDto::getDepartment)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<OrganizationDepartmentDto, OrganizationDepartmentSimpleVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            OrganizationDepartmentSimpleVo vo =
                                                    new OrganizationDepartmentSimpleVo();
                                            vo.setId(dto.getId());
                                            vo.setName(dto.getName());
                                            vo.setOrganizationLevel(dto.getOrganizationLevel());
                                            vo.setSortNumber(dto.getSortNumber());
                                            vo.setDepartment(
                                                    dto.getDepartment() == null
                                                            ? null
                                                            : departmentMap.get(
                                                                    dto.getDepartment()));
                                            vo.setInputCode(dto.getInputCode());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把OrganizationDepartmentDto转换成OrganizationDepartmentSimpleVo */
    @AutoGenerated(locked = true, uuid = "9c0c84c0-ad5b-4ddd-9d6d-e1a3bd303b0e-converter-list")
    public List<OrganizationDepartmentSimpleVo> convertToOrganizationDepartmentSimpleVoList(
            List<OrganizationDepartmentDto> dtoList) {
        return new ArrayList<>(convertToOrganizationDepartmentSimpleVoMap(dtoList).values());
    }
}
