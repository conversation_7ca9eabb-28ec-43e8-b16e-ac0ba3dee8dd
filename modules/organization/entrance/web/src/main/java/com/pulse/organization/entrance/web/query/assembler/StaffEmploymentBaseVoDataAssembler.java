package com.pulse.organization.entrance.web.query.assembler;

import com.pulse.organization.entrance.web.vo.StaffEmploymentBaseVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** StaffEmploymentBaseVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "badc4c99-b3ef-3fb5-a2fa-8c6ca29cc2fa")
public class StaffEmploymentBaseVoDataAssembler {

    /** 批量自定义组装StaffEmploymentBaseVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "6626d00a-ac97-3259-8f6a-2c8f489e1c07")
    public void assembleDataCustomized(List<StaffEmploymentBaseVo> dataList) {
        // 自定义数据组装

    }

    /** 组装StaffEmploymentBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "786f7230-78b6-392e-9999-34333521d60f")
    public void assembleData(Map<String, StaffEmploymentBaseVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }
}
