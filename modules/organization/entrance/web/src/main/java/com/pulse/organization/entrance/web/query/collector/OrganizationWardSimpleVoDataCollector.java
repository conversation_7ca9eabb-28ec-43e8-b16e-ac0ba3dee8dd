package com.pulse.organization.entrance.web.query.collector;

import com.pulse.organization.entrance.web.converter.OrganizationWardSimpleVoConverter;
import com.pulse.organization.entrance.web.converter.WardSimpleBaseVoConverter;
import com.pulse.organization.entrance.web.query.assembler.OrganizationWardSimpleVoDataAssembler.OrganizationWardSimpleVoDataHolder;
import com.pulse.organization.entrance.web.vo.WardSimpleBaseVo;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.OrganizationWardDto;
import com.pulse.organization.manager.dto.WardBaseDto;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.pulse.organization.service.WardBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装OrganizationWardSimpleVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "61616e85-63fd-3522-b700-9a28d484f5e9")
public class OrganizationWardSimpleVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationWardSimpleVoConverter organizationWardSimpleVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationWardSimpleVoDataCollector organizationWardSimpleVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private WardBaseDtoService wardBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private WardSimpleBaseVoConverter wardSimpleBaseVoConverter;

    /** 获取OrganizationWardDto数据填充OrganizationWardSimpleVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "5fefa9a3-6e4f-31c0-911b-31f5b06722e6")
    public void collectDataWithDtoData(
            List<OrganizationWardDto> dtoList, OrganizationWardSimpleVoDataHolder dataHolder) {
        List<WardBaseDto> wardList = new ArrayList<>();

        for (OrganizationWardDto rootDto : dtoList) {
            WardBaseDto wardDto = rootDto.getWard();
            if (wardDto != null) {
                wardList.add(wardDto);
            }
        }

        // access ward
        Map<WardBaseDto, WardSimpleBaseVo> wardVoMap =
                wardSimpleBaseVoConverter.convertToWardSimpleBaseVoMap(wardList);
        dataHolder.ward =
                wardList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> wardVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "60cf0ac9-29c3-347f-b62c-902252dea8d9")
    private void fillDataWhenNecessary(OrganizationWardSimpleVoDataHolder dataHolder) {
        List<OrganizationBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.ward == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(OrganizationBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<WardBaseDto> baseDtoList =
                    wardBaseDtoService.getByOrganizationIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(WardBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<WardBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(WardBaseDto::getOrganizationId));
            Map<WardBaseDto, WardSimpleBaseVo> dtoVoMap =
                    wardSimpleBaseVoConverter.convertToWardSimpleBaseVoMap(baseDtoList);
            Map<WardBaseDto, WardSimpleBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.ward =
                    rootDtoList.stream()
                            .map(OrganizationBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "ecb26ce7-b5ef-3fe7-b048-63119b0887ae")
    public void collectDataDefault(OrganizationWardSimpleVoDataHolder dataHolder) {
        organizationWardSimpleVoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
