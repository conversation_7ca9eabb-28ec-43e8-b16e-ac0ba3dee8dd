package com.pulse.organization.entrance.web.query.assembler;

import com.pulse.organization.entrance.web.vo.StaffWorkExperienceBaseVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** StaffWorkExperienceBaseVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "a2cd1555-0cc7-324c-9a97-ea0c2866819b")
public class StaffWorkExperienceBaseVoDataAssembler {

    /** 组装StaffWorkExperienceBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "69f589fe-5097-323e-ba30-edc76af28b16")
    public void assembleData(Map<String, StaffWorkExperienceBaseVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装StaffWorkExperienceBaseVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "714c221c-83b9-3f80-8423-9586645ac93c")
    public void assembleDataCustomized(List<StaffWorkExperienceBaseVo> dataList) {
        // 自定义数据组装

    }
}
