package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.application.manager.dto.ApplicationBaseDto;
import com.pulse.organization.entrance.web.query.assembler.AssetOrganizationVoDataAssembler;
import com.pulse.organization.entrance.web.query.assembler.AssetOrganizationVoDataAssembler.AssetOrganizationVoDataHolder;
import com.pulse.organization.entrance.web.query.collector.AssetOrganizationVoDataCollector;
import com.pulse.organization.entrance.web.vo.AssetOrganizationVo;
import com.pulse.organization.entrance.web.vo.OrganizationRefApplicationBaseVo;
import com.pulse.organization.entrance.web.vo.OrganizationSimpleVo;
import com.pulse.organization.manager.dto.AssetOrganizationDto;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.service.AssetOrganizationBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到AssetOrganizationVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "7901952d-c02f-4f04-abc1-f0dd8c3de18d|VO|CONVERTER")
public class AssetOrganizationVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private AssetOrganizationBaseDtoService assetOrganizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private AssetOrganizationVoDataAssembler assetOrganizationVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private AssetOrganizationVoDataCollector assetOrganizationVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationRefApplicationBaseVoConverter organizationRefApplicationBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationSimpleVoConverter organizationSimpleVoConverter;

    /** 把AssetOrganizationDto转换成AssetOrganizationVo */
    @AutoGenerated(locked = true, uuid = "3939a1aa-f510-3707-96fe-29b17855a3c9")
    public AssetOrganizationVo convertToAssetOrganizationVo(AssetOrganizationDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToAssetOrganizationVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把AssetOrganizationDto转换成AssetOrganizationVo */
    @AutoGenerated(locked = false, uuid = "7901952d-c02f-4f04-abc1-f0dd8c3de18d-converter-Map")
    public Map<AssetOrganizationDto, AssetOrganizationVo> convertToAssetOrganizationVoMap(
            List<AssetOrganizationDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<ApplicationBaseDto, OrganizationRefApplicationBaseVo> applicationMap =
                organizationRefApplicationBaseVoConverter
                        .convertToOrganizationRefApplicationBaseVoMap(
                                dtoList.stream()
                                        .filter(Objects::nonNull)
                                        .map(AssetOrganizationDto::getApplication)
                                        .filter(Objects::nonNull)
                                        .collect(Collectors.toList()));
        Map<OrganizationBaseDto, OrganizationSimpleVo> organizationMap =
                organizationSimpleVoConverter.convertToOrganizationSimpleVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(AssetOrganizationDto::getOrganization)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<AssetOrganizationDto, AssetOrganizationVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            AssetOrganizationVo vo = new AssetOrganizationVo();
                                            vo.setId(dto.getId());
                                            vo.setAssetId(dto.getAssetId());
                                            vo.setApplication(
                                                    dto.getApplication() == null
                                                            ? null
                                                            : applicationMap.get(
                                                                    dto.getApplication()));
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setDeletedBy(dto.getDeletedBy());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setOrganization(
                                                    dto.getOrganization() == null
                                                            ? null
                                                            : organizationMap.get(
                                                                    dto.getOrganization()));
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把AssetOrganizationDto转换成AssetOrganizationVo */
    @AutoGenerated(locked = true, uuid = "7901952d-c02f-4f04-abc1-f0dd8c3de18d-converter-list")
    public List<AssetOrganizationVo> convertToAssetOrganizationVoList(
            List<AssetOrganizationDto> dtoList) {
        return new ArrayList<>(convertToAssetOrganizationVoMap(dtoList).values());
    }

    /** 使用默认方式组装AssetOrganizationVo列表数据 */
    @AutoGenerated(locked = true, uuid = "b52336f1-1cfe-3120-8011-56ab446f8b53")
    public List<AssetOrganizationVo> convertAndAssembleDataList(
            List<AssetOrganizationDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        AssetOrganizationVoDataHolder dataHolder = new AssetOrganizationVoDataHolder();
        dataHolder.setRootBaseDtoList(
                assetOrganizationBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(AssetOrganizationDto::getId)
                                .collect(Collectors.toList())));
        Map<String, AssetOrganizationVo> voMap =
                convertToAssetOrganizationVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        assetOrganizationVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        assetOrganizationVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 使用默认方式组装AssetOrganizationVo数据 */
    @AutoGenerated(locked = true, uuid = "e1aa4a66-e39b-3ab8-ba96-46a9be48c6c8")
    public AssetOrganizationVo convertAndAssembleData(AssetOrganizationDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }
}
