package com.pulse.organization.entrance.web.query.collector;

import com.pulse.application.manager.dto.ApplicationBaseDto;
import com.pulse.organization.entrance.web.converter.OrganizationBaseVoConverter;
import com.pulse.organization.entrance.web.converter.OrganizationRefApplicationBaseVoConverter;
import com.pulse.organization.entrance.web.converter.StaffOrganizationVoConverter;
import com.pulse.organization.entrance.web.query.assembler.StaffOrganizationVoDataAssembler.StaffOrganizationVoDataHolder;
import com.pulse.organization.entrance.web.vo.OrganizationBaseVo;
import com.pulse.organization.entrance.web.vo.OrganizationRefApplicationBaseVo;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.StaffOrganizationBaseDto;
import com.pulse.organization.manager.dto.StaffOrganizationDto;
import com.pulse.organization.manager.facade.application.ApplicationBaseDtoServiceInOrganizationRpcAdapter;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.pulse.organization.service.StaffOrganizationBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装StaffOrganizationVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "0e335382-f0ff-31fb-8d68-3a03eb5f0793")
public class StaffOrganizationVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private ApplicationBaseDtoServiceInOrganizationRpcAdapter
            applicationBaseDtoServiceInOrganizationRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseVoConverter organizationBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationRefApplicationBaseVoConverter organizationRefApplicationBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffOrganizationBaseDtoService staffOrganizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffOrganizationVoConverter staffOrganizationVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffOrganizationVoDataCollector staffOrganizationVoDataCollector;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "456d7805-137e-3daa-b1f9-580b8ce7808f")
    private void fillDataWhenNecessary(StaffOrganizationVoDataHolder dataHolder) {
        List<StaffOrganizationBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.organization == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(StaffOrganizationBaseDto::getOrganizationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            Map<OrganizationBaseDto, OrganizationBaseVo> dtoVoMap =
                    organizationBaseVoConverter.convertToOrganizationBaseVoMap(baseDtoList);
            Map<OrganizationBaseDto, OrganizationBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.organization =
                    rootDtoList.stream()
                            .map(StaffOrganizationBaseDto::getOrganizationId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.application == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(StaffOrganizationBaseDto::getApplicationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ApplicationBaseDto> baseDtoList =
                    applicationBaseDtoServiceInOrganizationRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(ApplicationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, ApplicationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            ApplicationBaseDto::getId, Function.identity()));
            Map<ApplicationBaseDto, OrganizationRefApplicationBaseVo> dtoVoMap =
                    organizationRefApplicationBaseVoConverter
                            .convertToOrganizationRefApplicationBaseVoMap(baseDtoList);
            Map<ApplicationBaseDto, OrganizationRefApplicationBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.application =
                    rootDtoList.stream()
                            .map(StaffOrganizationBaseDto::getApplicationId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** 获取StaffOrganizationDto数据填充StaffOrganizationVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "4a964e12-7f2a-302e-9f7f-b0d9725c6e90")
    public void collectDataWithDtoData(
            List<StaffOrganizationDto> dtoList, StaffOrganizationVoDataHolder dataHolder) {
        List<OrganizationBaseDto> organizationList = new ArrayList<>();
        List<ApplicationBaseDto> applicationList = new ArrayList<>();

        for (StaffOrganizationDto rootDto : dtoList) {
            OrganizationBaseDto organizationDto = rootDto.getOrganization();
            if (organizationDto != null) {
                organizationList.add(organizationDto);
            }
            ApplicationBaseDto applicationDto = rootDto.getApplication();
            if (applicationDto != null) {
                applicationList.add(applicationDto);
            }
        }

        // access organization
        Map<OrganizationBaseDto, OrganizationBaseVo> organizationVoMap =
                organizationBaseVoConverter.convertToOrganizationBaseVoMap(organizationList);
        dataHolder.organization =
                organizationList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> organizationVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access application
        Map<ApplicationBaseDto, OrganizationRefApplicationBaseVo> applicationVoMap =
                organizationRefApplicationBaseVoConverter
                        .convertToOrganizationRefApplicationBaseVoMap(applicationList);
        dataHolder.application =
                applicationList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> applicationVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "dcd1671f-4e2d-3aea-92f7-70a5027c814c")
    public void collectDataDefault(StaffOrganizationVoDataHolder dataHolder) {
        staffOrganizationVoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
