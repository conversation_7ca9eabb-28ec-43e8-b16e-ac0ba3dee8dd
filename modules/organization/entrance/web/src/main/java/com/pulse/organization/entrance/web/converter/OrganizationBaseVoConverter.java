package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.entrance.web.query.assembler.OrganizationBaseVoDataAssembler;
import com.pulse.organization.entrance.web.vo.OrganizationBaseVo;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到OrganizationBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "8d31a143-1cd2-4558-a6f0-2dbdb7d593c0|VO|CONVERTER")
public class OrganizationBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseVoDataAssembler organizationBaseVoDataAssembler;

    /** 使用默认方式组装OrganizationBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "85a56748-204b-34a0-82fb-b769fc9d94e8")
    public List<OrganizationBaseVo> convertAndAssembleDataList(List<OrganizationBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, OrganizationBaseVo> voMap =
                convertToOrganizationBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        organizationBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把OrganizationBaseDto转换成OrganizationBaseVo */
    @AutoGenerated(locked = false, uuid = "8d31a143-1cd2-4558-a6f0-2dbdb7d593c0-converter-Map")
    public Map<OrganizationBaseDto, OrganizationBaseVo> convertToOrganizationBaseVoMap(
            List<OrganizationBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<OrganizationBaseDto, OrganizationBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            OrganizationBaseVo vo = new OrganizationBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setParentId(dto.getParentId());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setDescription(dto.getDescription());
                                            vo.setName(dto.getName());
                                            vo.setOrganizationLevel(dto.getOrganizationLevel());
                                            vo.setSortNumber(dto.getSortNumber());
                                            vo.setStatus(dto.getStatus());
                                            vo.setType(dto.getType());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setAddress(dto.getAddress());
                                            vo.setContactPerson(dto.getContactPerson());
                                            vo.setInputCode(dto.getInputCode());
                                            vo.setContactNumber(dto.getContactNumber());
                                            vo.setAbbreviation(dto.getAbbreviation());
                                            vo.setDeletedBy(dto.getDeletedBy());
                                            vo.setAlias(dto.getAlias());
                                            vo.setEnglishName(dto.getEnglishName());
                                            vo.setInvalidFlag(dto.getInvalidFlag());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把OrganizationBaseDto转换成OrganizationBaseVo */
    @AutoGenerated(locked = true, uuid = "8d31a143-1cd2-4558-a6f0-2dbdb7d593c0-converter-list")
    public List<OrganizationBaseVo> convertToOrganizationBaseVoList(
            List<OrganizationBaseDto> dtoList) {
        return new ArrayList<>(convertToOrganizationBaseVoMap(dtoList).values());
    }

    /** 使用默认方式组装OrganizationBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "e9ff3888-ccc6-315b-8d54-72ce44e979be")
    public OrganizationBaseVo convertAndAssembleData(OrganizationBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把OrganizationBaseDto转换成OrganizationBaseVo */
    @AutoGenerated(locked = true, uuid = "f5f2cef5-6f64-373f-a4ee-3976aef566c2")
    public OrganizationBaseVo convertToOrganizationBaseVo(OrganizationBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToOrganizationBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }
}
