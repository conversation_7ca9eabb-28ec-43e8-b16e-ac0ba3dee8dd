package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.entrance.web.query.assembler.OrganizationCampusVoDataAssembler;
import com.pulse.organization.entrance.web.query.assembler.OrganizationCampusVoDataAssembler.OrganizationCampusVoDataHolder;
import com.pulse.organization.entrance.web.query.collector.OrganizationCampusVoDataCollector;
import com.pulse.organization.entrance.web.vo.CampusBaseVo;
import com.pulse.organization.entrance.web.vo.OrganizationCampusVo;
import com.pulse.organization.manager.dto.CampusBaseDto;
import com.pulse.organization.manager.dto.OrganizationCampusDto;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到OrganizationCampusVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "de6c1d9f-2b8d-4184-a078-1b86b9be4c32|VO|CONVERTER")
public class OrganizationCampusVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private CampusBaseVoConverter campusBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationCampusVoDataAssembler organizationCampusVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationCampusVoDataCollector organizationCampusVoDataCollector;

    /** 把OrganizationCampusDto转换成OrganizationCampusVo */
    @AutoGenerated(locked = true, uuid = "9cdfda41-9fa8-3a87-8027-76d5a4f718f0")
    public OrganizationCampusVo convertToOrganizationCampusVo(OrganizationCampusDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToOrganizationCampusVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装OrganizationCampusVo列表数据 */
    @AutoGenerated(locked = true, uuid = "aa6a3956-cb1b-300f-a0db-93f249a8ea08")
    public List<OrganizationCampusVo> convertAndAssembleDataList(
            List<OrganizationCampusDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        OrganizationCampusVoDataHolder dataHolder = new OrganizationCampusVoDataHolder();
        dataHolder.setRootBaseDtoList(
                organizationBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(OrganizationCampusDto::getId)
                                .collect(Collectors.toList())));
        Map<String, OrganizationCampusVo> voMap =
                convertToOrganizationCampusVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        organizationCampusVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        organizationCampusVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把OrganizationCampusDto转换成OrganizationCampusVo */
    @AutoGenerated(locked = false, uuid = "de6c1d9f-2b8d-4184-a078-1b86b9be4c32-converter-Map")
    public Map<OrganizationCampusDto, OrganizationCampusVo> convertToOrganizationCampusVoMap(
            List<OrganizationCampusDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<CampusBaseDto, CampusBaseVo> campusMap =
                campusBaseVoConverter.convertToCampusBaseVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(OrganizationCampusDto::getCampus)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<OrganizationCampusDto, OrganizationCampusVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            OrganizationCampusVo vo = new OrganizationCampusVo();
                                            vo.setId(dto.getId());
                                            vo.setParentId(dto.getParentId());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setDescription(dto.getDescription());
                                            vo.setName(dto.getName());
                                            vo.setOrganizationLevel(dto.getOrganizationLevel());
                                            vo.setSortNumber(dto.getSortNumber());
                                            vo.setStatus(dto.getStatus());
                                            vo.setType(dto.getType());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setAddress(dto.getAddress());
                                            vo.setContactPerson(dto.getContactPerson());
                                            vo.setInputCode(dto.getInputCode());
                                            vo.setContactNumber(dto.getContactNumber());
                                            vo.setAbbreviation(dto.getAbbreviation());
                                            vo.setDeletedBy(dto.getDeletedBy());
                                            vo.setAlias(dto.getAlias());
                                            vo.setEnglishName(dto.getEnglishName());
                                            vo.setCampus(
                                                    dto.getCampus() == null
                                                            ? null
                                                            : campusMap.get(dto.getCampus()));
                                            vo.setInvalidFlag(dto.getInvalidFlag());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把OrganizationCampusDto转换成OrganizationCampusVo */
    @AutoGenerated(locked = true, uuid = "de6c1d9f-2b8d-4184-a078-1b86b9be4c32-converter-list")
    public List<OrganizationCampusVo> convertToOrganizationCampusVoList(
            List<OrganizationCampusDto> dtoList) {
        return new ArrayList<>(convertToOrganizationCampusVoMap(dtoList).values());
    }

    /** 使用默认方式组装OrganizationCampusVo数据 */
    @AutoGenerated(locked = true, uuid = "f8088989-6d8e-3475-824a-0315f928239e")
    public OrganizationCampusVo convertAndAssembleData(OrganizationCampusDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }
}
