package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.application.manager.dto.ApplicationBaseDto;
import com.pulse.organization.entrance.web.query.assembler.OrganizationRefApplicationBaseVoDataAssembler;
import com.pulse.organization.entrance.web.vo.OrganizationRefApplicationBaseVo;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到OrganizationRefApplicationBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "e9be1b98-10f8-4860-a16e-83d9f5fc068c|VO|CONVERTER")
public class OrganizationRefApplicationBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationRefApplicationBaseVoDataAssembler
            organizationRefApplicationBaseVoDataAssembler;

    /** 使用默认方式组装OrganizationRefApplicationBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "5e3405a3-9855-3ed8-b290-455e37f3d219")
    public OrganizationRefApplicationBaseVo convertAndAssembleData(ApplicationBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装OrganizationRefApplicationBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "69319cb4-ded8-345d-9620-ec46f9e69886")
    public List<OrganizationRefApplicationBaseVo> convertAndAssembleDataList(
            List<ApplicationBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, OrganizationRefApplicationBaseVo> voMap =
                convertToOrganizationRefApplicationBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        organizationRefApplicationBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把ApplicationBaseDto转换成OrganizationRefApplicationBaseVo */
    @AutoGenerated(locked = true, uuid = "8295430c-c7a0-3ee3-b163-9004ea9e1fdf")
    public OrganizationRefApplicationBaseVo convertToOrganizationRefApplicationBaseVo(
            ApplicationBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToOrganizationRefApplicationBaseVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 把ApplicationBaseDto转换成OrganizationRefApplicationBaseVo */
    @AutoGenerated(locked = false, uuid = "e9be1b98-10f8-4860-a16e-83d9f5fc068c-converter-Map")
    public Map<ApplicationBaseDto, OrganizationRefApplicationBaseVo>
            convertToOrganizationRefApplicationBaseVoMap(List<ApplicationBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<ApplicationBaseDto, OrganizationRefApplicationBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            OrganizationRefApplicationBaseVo vo =
                                                    new OrganizationRefApplicationBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setName(dto.getName());
                                            vo.setInputCode(dto.getInputCode());
                                            vo.setAbbreviation(dto.getAbbreviation());
                                            vo.setDescription(dto.getDescription());
                                            vo.setTemplateApplicationFlag(
                                                    dto.getTemplateApplicationFlag());
                                            vo.setTemplateApplicationId(
                                                    dto.getTemplateApplicationId());
                                            vo.setCategoryId(dto.getCategoryId());
                                            vo.setEnableFlag(dto.getEnableFlag());
                                            vo.setMultipleInstanceFlag(
                                                    dto.getMultipleInstanceFlag());
                                            vo.setOrganizationId(dto.getOrganizationId());
                                            vo.setParentApplicationId(dto.getParentApplicationId());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setRouterId(dto.getRouterId());
                                            vo.setCode(dto.getCode());
                                            vo.setStatus(dto.getStatus());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把ApplicationBaseDto转换成OrganizationRefApplicationBaseVo */
    @AutoGenerated(locked = true, uuid = "e9be1b98-10f8-4860-a16e-83d9f5fc068c-converter-list")
    public List<OrganizationRefApplicationBaseVo> convertToOrganizationRefApplicationBaseVoList(
            List<ApplicationBaseDto> dtoList) {
        return new ArrayList<>(convertToOrganizationRefApplicationBaseVoMap(dtoList).values());
    }
}
