package com.pulse.organization.entrance.web.query.collector;

import com.pulse.organization.entrance.web.converter.DepartmentSimpleVoConverter;
import com.pulse.organization.entrance.web.converter.OrganizationDepartmentSimpleVoConverter;
import com.pulse.organization.entrance.web.query.assembler.OrganizationDepartmentSimpleVoDataAssembler.OrganizationDepartmentSimpleVoDataHolder;
import com.pulse.organization.entrance.web.vo.DepartmentSimpleVo;
import com.pulse.organization.manager.converter.DepartmentDtoConverter;
import com.pulse.organization.manager.dto.DepartmentBaseDto;
import com.pulse.organization.manager.dto.DepartmentDto;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.OrganizationDepartmentDto;
import com.pulse.organization.service.DepartmentBaseDtoService;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装OrganizationDepartmentSimpleVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "8fda200d-e3de-353e-85ea-ab148e346029")
public class OrganizationDepartmentSimpleVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private DepartmentBaseDtoService departmentBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DepartmentDtoConverter departmentDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DepartmentSimpleVoConverter departmentSimpleVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationDepartmentSimpleVoConverter organizationDepartmentSimpleVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationDepartmentSimpleVoDataCollector organizationDepartmentSimpleVoDataCollector;

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "0cb143e1-60b6-3da9-a93b-bb2138f3177b")
    public void collectDataDefault(OrganizationDepartmentSimpleVoDataHolder dataHolder) {
        organizationDepartmentSimpleVoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 获取OrganizationDepartmentDto数据填充OrganizationDepartmentSimpleVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "10555cab-97fb-330d-a797-e7ae81357915")
    public void collectDataWithDtoData(
            List<OrganizationDepartmentDto> dtoList,
            OrganizationDepartmentSimpleVoDataHolder dataHolder) {
        Map<DepartmentBaseDto, DepartmentDto> departmentBaseDtoDtoMap = new LinkedHashMap<>();

        for (OrganizationDepartmentDto rootDto : dtoList) {
            DepartmentDto departmentDto = rootDto.getDepartment();
            if (departmentDto != null) {
                DepartmentBaseDto departmentBaseDto =
                        departmentDtoConverter.convertFromDepartmentDtoToDepartmentBaseDto(
                                departmentDto);
                departmentBaseDto.setOrganizationId(rootDto.getId());
                departmentBaseDtoDtoMap.put(departmentBaseDto, departmentDto);
            }
        }

        // access department
        Map<DepartmentDto, DepartmentSimpleVo> departmentVoMap =
                departmentSimpleVoConverter.convertToDepartmentSimpleVoMap(
                        new ArrayList<>(departmentBaseDtoDtoMap.values()));
        dataHolder.department =
                departmentBaseDtoDtoMap.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                departmentVoMap.get(
                                                        departmentBaseDtoDtoMap.get(baseDto)),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "1853c990-0960-3a2c-b4cb-fc5535b3e05e")
    private void fillDataWhenNecessary(OrganizationDepartmentSimpleVoDataHolder dataHolder) {
        List<OrganizationBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.department == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(OrganizationBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DepartmentBaseDto> baseDtoList =
                    departmentBaseDtoService.getByOrganizationIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(DepartmentBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<DepartmentBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(DepartmentBaseDto::getOrganizationId));
            Map<String, DepartmentDto> departmentDtoMap =
                    departmentDtoConverter
                            .convertFromDepartmentBaseDtoToDepartmentDto(baseDtoList)
                            .stream()
                            .collect(Collectors.toMap(DepartmentDto::getId, Function.identity()));
            Map<DepartmentDto, DepartmentSimpleVo> dtoVoMap =
                    departmentSimpleVoConverter.convertToDepartmentSimpleVoMap(
                            new ArrayList<>(departmentDtoMap.values()));
            Map<DepartmentBaseDto, DepartmentSimpleVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .filter(baseDto -> departmentDtoMap.containsKey(baseDto.getId()))
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    dtoVoMap.get(
                                                            departmentDtoMap.get(baseDto.getId())),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.department =
                    rootDtoList.stream()
                            .map(OrganizationBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }
}
