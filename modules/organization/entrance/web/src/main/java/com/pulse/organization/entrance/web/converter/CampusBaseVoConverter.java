package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.entrance.web.query.assembler.CampusBaseVoDataAssembler;
import com.pulse.organization.entrance.web.vo.CampusBaseVo;
import com.pulse.organization.manager.dto.CampusBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到CampusBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "4a4aa2d5-62db-4a76-8699-29b20e5a70c1|VO|CONVERTER")
public class CampusBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private CampusBaseVoDataAssembler campusBaseVoDataAssembler;

    /** 把CampusBaseDto转换成CampusBaseVo */
    @AutoGenerated(locked = true, uuid = "2cd994cb-b273-3379-b312-8d0e0e6df61c")
    public CampusBaseVo convertToCampusBaseVo(CampusBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToCampusBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把CampusBaseDto转换成CampusBaseVo */
    @AutoGenerated(locked = false, uuid = "4a4aa2d5-62db-4a76-8699-29b20e5a70c1-converter-Map")
    public Map<CampusBaseDto, CampusBaseVo> convertToCampusBaseVoMap(List<CampusBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<CampusBaseDto, CampusBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            CampusBaseVo vo = new CampusBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setOrganizationId(dto.getOrganizationId());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setCampusNumber(dto.getCampusNumber());
                                            vo.setMedicalInsuranceCode(
                                                    dto.getMedicalInsuranceCode());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setDeletedBy(dto.getDeletedBy());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把CampusBaseDto转换成CampusBaseVo */
    @AutoGenerated(locked = true, uuid = "4a4aa2d5-62db-4a76-8699-29b20e5a70c1-converter-list")
    public List<CampusBaseVo> convertToCampusBaseVoList(List<CampusBaseDto> dtoList) {
        return new ArrayList<>(convertToCampusBaseVoMap(dtoList).values());
    }

    /** 使用默认方式组装CampusBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "72225c5f-e7a1-3242-a450-6128c438534f")
    public CampusBaseVo convertAndAssembleData(CampusBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装CampusBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "b8723a2c-3240-3571-8e8d-3cac729566ec")
    public List<CampusBaseVo> convertAndAssembleDataList(List<CampusBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, CampusBaseVo> voMap =
                convertToCampusBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        campusBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
