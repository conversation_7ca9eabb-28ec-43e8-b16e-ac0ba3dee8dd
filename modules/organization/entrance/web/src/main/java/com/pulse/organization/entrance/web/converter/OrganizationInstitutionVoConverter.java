package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.entrance.web.query.assembler.OrganizationInstitutionVoDataAssembler;
import com.pulse.organization.entrance.web.query.assembler.OrganizationInstitutionVoDataAssembler.OrganizationInstitutionVoDataHolder;
import com.pulse.organization.entrance.web.query.collector.OrganizationInstitutionVoDataCollector;
import com.pulse.organization.entrance.web.vo.InstitutionBaseVo;
import com.pulse.organization.entrance.web.vo.OrganizationInstitutionVo;
import com.pulse.organization.manager.dto.InstitutionBaseDto;
import com.pulse.organization.manager.dto.OrganizationInstitutionDto;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到OrganizationInstitutionVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "3aab1efb-1c44-4a15-8264-1f573e6e1c10|VO|CONVERTER")
public class OrganizationInstitutionVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private InstitutionBaseVoConverter institutionBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationInstitutionVoDataAssembler organizationInstitutionVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationInstitutionVoDataCollector organizationInstitutionVoDataCollector;

    /** 把OrganizationInstitutionDto转换成OrganizationInstitutionVo */
    @AutoGenerated(locked = true, uuid = "26e5547e-4acd-3b6a-b737-1c156e856195")
    public OrganizationInstitutionVo convertToOrganizationInstitutionVo(
            OrganizationInstitutionDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToOrganizationInstitutionVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把OrganizationInstitutionDto转换成OrganizationInstitutionVo */
    @AutoGenerated(locked = false, uuid = "3aab1efb-1c44-4a15-8264-1f573e6e1c10-converter-Map")
    public Map<OrganizationInstitutionDto, OrganizationInstitutionVo>
            convertToOrganizationInstitutionVoMap(List<OrganizationInstitutionDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<InstitutionBaseDto, InstitutionBaseVo> institutionMap =
                institutionBaseVoConverter.convertToInstitutionBaseVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(OrganizationInstitutionDto::getInstitution)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<OrganizationInstitutionDto, OrganizationInstitutionVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            OrganizationInstitutionVo vo =
                                                    new OrganizationInstitutionVo();
                                            vo.setId(dto.getId());
                                            vo.setParentId(dto.getParentId());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setDescription(dto.getDescription());
                                            vo.setName(dto.getName());
                                            vo.setOrganizationLevel(dto.getOrganizationLevel());
                                            vo.setSortNumber(dto.getSortNumber());
                                            vo.setStatus(dto.getStatus());
                                            vo.setType(dto.getType());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setAddress(dto.getAddress());
                                            vo.setContactPerson(dto.getContactPerson());
                                            vo.setInputCode(dto.getInputCode());
                                            vo.setContactNumber(dto.getContactNumber());
                                            vo.setAbbreviation(dto.getAbbreviation());
                                            vo.setDeletedBy(dto.getDeletedBy());
                                            vo.setAlias(dto.getAlias());
                                            vo.setEnglishName(dto.getEnglishName());
                                            vo.setInstitution(
                                                    dto.getInstitution() == null
                                                            ? null
                                                            : institutionMap.get(
                                                                    dto.getInstitution()));
                                            vo.setInvalidFlag(dto.getInvalidFlag());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把OrganizationInstitutionDto转换成OrganizationInstitutionVo */
    @AutoGenerated(locked = true, uuid = "3aab1efb-1c44-4a15-8264-1f573e6e1c10-converter-list")
    public List<OrganizationInstitutionVo> convertToOrganizationInstitutionVoList(
            List<OrganizationInstitutionDto> dtoList) {
        return new ArrayList<>(convertToOrganizationInstitutionVoMap(dtoList).values());
    }

    /** 使用默认方式组装OrganizationInstitutionVo数据 */
    @AutoGenerated(locked = true, uuid = "6a66516f-9e0b-32c5-ba33-00247085132c")
    public OrganizationInstitutionVo convertAndAssembleData(OrganizationInstitutionDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装OrganizationInstitutionVo列表数据 */
    @AutoGenerated(locked = true, uuid = "963bd0db-ae2a-3bd2-b0f3-f72615c4433c")
    public List<OrganizationInstitutionVo> convertAndAssembleDataList(
            List<OrganizationInstitutionDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        OrganizationInstitutionVoDataHolder dataHolder = new OrganizationInstitutionVoDataHolder();
        dataHolder.setRootBaseDtoList(
                organizationBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(OrganizationInstitutionDto::getId)
                                .collect(Collectors.toList())));
        Map<String, OrganizationInstitutionVo> voMap =
                convertToOrganizationInstitutionVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        organizationInstitutionVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        organizationInstitutionVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
