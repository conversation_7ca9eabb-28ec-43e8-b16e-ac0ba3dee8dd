package com.pulse.organization.entrance.web.query.collector;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.application.manager.dto.ApplicationBaseDto;
import com.pulse.organization.entrance.web.converter.OrganizationBaseVoConverter;
import com.pulse.organization.entrance.web.converter.OrganizationRefApplicationBaseVoConverter;
import com.pulse.organization.entrance.web.converter.OrganizationRefUserBaseVoConverter;
import com.pulse.organization.entrance.web.converter.OrganizationSimpleVoConverter;
import com.pulse.organization.entrance.web.converter.StaffBaseVoConverter;
import com.pulse.organization.entrance.web.converter.StaffDetailVoConverter;
import com.pulse.organization.entrance.web.converter.StaffEducationBaseVoConverter;
import com.pulse.organization.entrance.web.converter.StaffEmploymentBaseVoConverter;
import com.pulse.organization.entrance.web.converter.StaffExtensionBaseVoConverter;
import com.pulse.organization.entrance.web.converter.StaffOrganizationVoConverter;
import com.pulse.organization.entrance.web.converter.StaffPracticeBaseVoConverter;
import com.pulse.organization.entrance.web.converter.StaffUserVoConverter;
import com.pulse.organization.entrance.web.converter.StaffWorkExperienceBaseVoConverter;
import com.pulse.organization.entrance.web.query.assembler.StaffDetailVoDataAssembler.StaffDetailVoDataHolder;
import com.pulse.organization.entrance.web.vo.OrganizationBaseVo;
import com.pulse.organization.entrance.web.vo.OrganizationRefApplicationBaseVo;
import com.pulse.organization.entrance.web.vo.OrganizationRefUserBaseVo;
import com.pulse.organization.entrance.web.vo.OrganizationSimpleVo;
import com.pulse.organization.entrance.web.vo.StaffBaseVo;
import com.pulse.organization.entrance.web.vo.StaffEducationBaseVo;
import com.pulse.organization.entrance.web.vo.StaffEmploymentBaseVo;
import com.pulse.organization.entrance.web.vo.StaffExtensionBaseVo;
import com.pulse.organization.entrance.web.vo.StaffOrganizationVo;
import com.pulse.organization.entrance.web.vo.StaffPracticeBaseVo;
import com.pulse.organization.entrance.web.vo.StaffUserVo;
import com.pulse.organization.entrance.web.vo.StaffWorkExperienceBaseVo;
import com.pulse.organization.manager.converter.StaffOrganizationDtoConverter;
import com.pulse.organization.manager.converter.StaffUserDtoConverter;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.StaffBaseDto;
import com.pulse.organization.manager.dto.StaffDetailDto;
import com.pulse.organization.manager.dto.StaffEducationBaseDto;
import com.pulse.organization.manager.dto.StaffEmploymentBaseDto;
import com.pulse.organization.manager.dto.StaffExtensionBaseDto;
import com.pulse.organization.manager.dto.StaffOrganizationBaseDto;
import com.pulse.organization.manager.dto.StaffOrganizationDto;
import com.pulse.organization.manager.dto.StaffPracticeBaseDto;
import com.pulse.organization.manager.dto.StaffUserBaseDto;
import com.pulse.organization.manager.dto.StaffUserDto;
import com.pulse.organization.manager.dto.StaffWorkExperienceBaseDto;
import com.pulse.organization.manager.facade.application.ApplicationBaseDtoServiceInOrganizationRpcAdapter;
import com.pulse.organization.manager.facade.user.UserBaseDtoServiceInOrganizationRpcAdapter;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.pulse.organization.service.StaffBaseDtoService;
import com.pulse.organization.service.StaffEducationBaseDtoService;
import com.pulse.organization.service.StaffEmploymentBaseDtoService;
import com.pulse.organization.service.StaffExtensionBaseDtoService;
import com.pulse.organization.service.StaffOrganizationBaseDtoService;
import com.pulse.organization.service.StaffPracticeBaseDtoService;
import com.pulse.organization.service.StaffUserBaseDtoService;
import com.pulse.organization.service.StaffWorkExperienceBaseDtoService;
import com.pulse.user.manager.dto.UserBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装StaffDetailVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "fb69ea24-c9b4-3cf7-aa8e-606d3b280ef0")
public class StaffDetailVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private ApplicationBaseDtoServiceInOrganizationRpcAdapter
            applicationBaseDtoServiceInOrganizationRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseVoConverter organizationBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationRefApplicationBaseVoConverter organizationRefApplicationBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationRefUserBaseVoConverter organizationRefUserBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationSimpleVoConverter organizationSimpleVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffBaseDtoService staffBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffBaseVoConverter staffBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffDetailVoConverter staffDetailVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffDetailVoDataCollector staffDetailVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private StaffEducationBaseDtoService staffEducationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffEducationBaseVoConverter staffEducationBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffEmploymentBaseDtoService staffEmploymentBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffEmploymentBaseVoConverter staffEmploymentBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffExtensionBaseDtoService staffExtensionBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffExtensionBaseVoConverter staffExtensionBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffOrganizationBaseDtoService staffOrganizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffOrganizationDtoConverter staffOrganizationDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffOrganizationVoConverter staffOrganizationVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffPracticeBaseDtoService staffPracticeBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffPracticeBaseVoConverter staffPracticeBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffUserBaseDtoService staffUserBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffUserDtoConverter staffUserDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffUserVoConverter staffUserVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffWorkExperienceBaseDtoService staffWorkExperienceBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffWorkExperienceBaseVoConverter staffWorkExperienceBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private UserBaseDtoServiceInOrganizationRpcAdapter userBaseDtoServiceInOrganizationRpcAdapter;

    /** 获取StaffDetailDto数据填充StaffDetailVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "41b2c8ee-e5e0-3c24-964d-ed14d706a9b7")
    public void collectDataWithDtoData(
            List<StaffDetailDto> dtoList, StaffDetailVoDataHolder dataHolder) {
        Map<StaffUserBaseDto, StaffUserDto> staffUserListBaseDtoDtoMap = new LinkedHashMap<>();
        List<StaffBaseDto> staffUserList2StaffList = new ArrayList<>();
        List<UserBaseDto> staffUserList2UserList = new ArrayList<>();
        List<StaffEducationBaseDto> staffEducationListList = new ArrayList<>();
        List<StaffWorkExperienceBaseDto> staffWorkExperienceListList = new ArrayList<>();
        Map<StaffOrganizationBaseDto, StaffOrganizationDto>
                staffLoginOrganizationDetailListBaseDtoDtoMap = new LinkedHashMap<>();
        List<OrganizationBaseDto> staffLoginOrganizationDetailList2OrganizationList =
                new ArrayList<>();
        List<ApplicationBaseDto> staffLoginOrganizationDetailList2ApplicationList =
                new ArrayList<>();
        List<StaffExtensionBaseDto> staffExtensionList = new ArrayList<>();
        List<StaffEmploymentBaseDto> staffEmploymentListList = new ArrayList<>();
        List<OrganizationBaseDto> accountingOrganizationList = new ArrayList<>();
        List<OrganizationBaseDto> hrOrganizationList = new ArrayList<>();
        List<StaffPracticeBaseDto> staffPracticeListList = new ArrayList<>();
        List<OrganizationBaseDto> organizationList = new ArrayList<>();

        for (StaffDetailDto rootDto : dtoList) {
            if (CollectionUtil.isNotEmpty(rootDto.getStaffUserList())) {
                Map<String, StaffUserBaseDto> staffUserListBaseDtoMap =
                        staffUserDtoConverter
                                .convertFromStaffUserDtoToStaffUserBaseDto(
                                        rootDto.getStaffUserList())
                                .stream()
                                .collect(
                                        Collectors.toMap(
                                                StaffUserBaseDto::getId, Function.identity()));
                for (StaffUserDto staffUserListDto : rootDto.getStaffUserList()) {
                    StaffUserBaseDto staffUserListBaseDto =
                            staffUserListBaseDtoMap.get(staffUserListDto.getId());
                    staffUserListBaseDto.setStaffId(rootDto.getId());
                    staffUserListBaseDtoDtoMap.put(staffUserListBaseDto, staffUserListDto);
                    StaffBaseDto staffUserList2StaffDto = staffUserListDto.getStaff();
                    if (staffUserList2StaffDto != null) {
                        staffUserList2StaffList.add(staffUserList2StaffDto);
                    }
                    UserBaseDto staffUserList2UserDto = staffUserListDto.getUser();
                    if (staffUserList2UserDto != null) {
                        staffUserList2UserList.add(staffUserList2UserDto);
                    }
                }
            }
            if (CollectionUtil.isNotEmpty(rootDto.getStaffEducationList())) {
                for (StaffEducationBaseDto staffEducationListDto :
                        rootDto.getStaffEducationList()) {
                    staffEducationListList.add(staffEducationListDto);
                }
            }
            if (CollectionUtil.isNotEmpty(rootDto.getStaffWorkExperienceList())) {
                for (StaffWorkExperienceBaseDto staffWorkExperienceListDto :
                        rootDto.getStaffWorkExperienceList()) {
                    staffWorkExperienceListList.add(staffWorkExperienceListDto);
                }
            }
            if (CollectionUtil.isNotEmpty(rootDto.getStaffLoginOrganizationDetailList())) {
                Map<String, StaffOrganizationBaseDto> staffLoginOrganizationDetailListBaseDtoMap =
                        staffOrganizationDtoConverter
                                .convertFromStaffOrganizationDtoToStaffOrganizationBaseDto(
                                        rootDto.getStaffLoginOrganizationDetailList())
                                .stream()
                                .collect(
                                        Collectors.toMap(
                                                StaffOrganizationBaseDto::getId,
                                                Function.identity()));
                for (StaffOrganizationDto staffLoginOrganizationDetailListDto :
                        rootDto.getStaffLoginOrganizationDetailList()) {
                    StaffOrganizationBaseDto staffLoginOrganizationDetailListBaseDto =
                            staffLoginOrganizationDetailListBaseDtoMap.get(
                                    staffLoginOrganizationDetailListDto.getId());
                    staffLoginOrganizationDetailListBaseDto.setStaffId(rootDto.getId());
                    staffLoginOrganizationDetailListBaseDtoDtoMap.put(
                            staffLoginOrganizationDetailListBaseDto,
                            staffLoginOrganizationDetailListDto);
                    OrganizationBaseDto staffLoginOrganizationDetailList2OrganizationDto =
                            staffLoginOrganizationDetailListDto.getOrganization();
                    if (staffLoginOrganizationDetailList2OrganizationDto != null) {
                        staffLoginOrganizationDetailList2OrganizationList.add(
                                staffLoginOrganizationDetailList2OrganizationDto);
                    }
                    ApplicationBaseDto staffLoginOrganizationDetailList2ApplicationDto =
                            staffLoginOrganizationDetailListDto.getApplication();
                    if (staffLoginOrganizationDetailList2ApplicationDto != null) {
                        staffLoginOrganizationDetailList2ApplicationList.add(
                                staffLoginOrganizationDetailList2ApplicationDto);
                    }
                }
            }
            StaffExtensionBaseDto staffExtensionDto = rootDto.getStaffExtension();
            if (staffExtensionDto != null) {
                staffExtensionList.add(staffExtensionDto);
            }
            if (CollectionUtil.isNotEmpty(rootDto.getStaffEmploymentList())) {
                for (StaffEmploymentBaseDto staffEmploymentListDto :
                        rootDto.getStaffEmploymentList()) {
                    staffEmploymentListList.add(staffEmploymentListDto);
                }
            }
            OrganizationBaseDto accountingOrganizationDto = rootDto.getAccountingOrganization();
            if (accountingOrganizationDto != null) {
                accountingOrganizationList.add(accountingOrganizationDto);
            }
            OrganizationBaseDto hrOrganizationDto = rootDto.getHrOrganization();
            if (hrOrganizationDto != null) {
                hrOrganizationList.add(hrOrganizationDto);
            }
            if (CollectionUtil.isNotEmpty(rootDto.getStaffPracticeList())) {
                for (StaffPracticeBaseDto staffPracticeListDto : rootDto.getStaffPracticeList()) {
                    staffPracticeListList.add(staffPracticeListDto);
                }
            }
            OrganizationBaseDto organizationDto = rootDto.getOrganization();
            if (organizationDto != null) {
                organizationList.add(organizationDto);
            }
        }

        // access staffUserList
        Map<StaffUserDto, StaffUserVo> staffUserListVoMap =
                staffUserVoConverter.convertToStaffUserVoMap(
                        new ArrayList<>(staffUserListBaseDtoDtoMap.values()));
        dataHolder.staffUserList =
                staffUserListBaseDtoDtoMap.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                staffUserListVoMap.get(
                                                        staffUserListBaseDtoDtoMap.get(baseDto)),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access staffUserList2Staff
        Map<StaffBaseDto, StaffBaseVo> staffUserList2StaffVoMap =
                staffBaseVoConverter.convertToStaffBaseVoMap(staffUserList2StaffList);
        dataHolder.staffUserList2Staff =
                staffUserList2StaffList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> staffUserList2StaffVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access staffUserList2User
        Map<UserBaseDto, OrganizationRefUserBaseVo> staffUserList2UserVoMap =
                organizationRefUserBaseVoConverter.convertToOrganizationRefUserBaseVoMap(
                        staffUserList2UserList);
        dataHolder.staffUserList2User =
                staffUserList2UserList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> staffUserList2UserVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access staffEducationList
        Map<StaffEducationBaseDto, StaffEducationBaseVo> staffEducationListVoMap =
                staffEducationBaseVoConverter.convertToStaffEducationBaseVoMap(
                        staffEducationListList);
        dataHolder.staffEducationList =
                staffEducationListList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> staffEducationListVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access staffWorkExperienceList
        Map<StaffWorkExperienceBaseDto, StaffWorkExperienceBaseVo> staffWorkExperienceListVoMap =
                staffWorkExperienceBaseVoConverter.convertToStaffWorkExperienceBaseVoMap(
                        staffWorkExperienceListList);
        dataHolder.staffWorkExperienceList =
                staffWorkExperienceListList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> staffWorkExperienceListVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access staffLoginOrganizationDetailList
        Map<StaffOrganizationDto, StaffOrganizationVo> staffLoginOrganizationDetailListVoMap =
                staffOrganizationVoConverter.convertToStaffOrganizationVoMap(
                        new ArrayList<>(staffLoginOrganizationDetailListBaseDtoDtoMap.values()));
        dataHolder.staffLoginOrganizationDetailList =
                staffLoginOrganizationDetailListBaseDtoDtoMap.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                staffLoginOrganizationDetailListVoMap.get(
                                                        staffLoginOrganizationDetailListBaseDtoDtoMap
                                                                .get(baseDto)),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access staffLoginOrganizationDetailList2Organization
        Map<OrganizationBaseDto, OrganizationBaseVo>
                staffLoginOrganizationDetailList2OrganizationVoMap =
                        organizationBaseVoConverter.convertToOrganizationBaseVoMap(
                                staffLoginOrganizationDetailList2OrganizationList);
        dataHolder.staffLoginOrganizationDetailList2Organization =
                staffLoginOrganizationDetailList2OrganizationList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                staffLoginOrganizationDetailList2OrganizationVoMap
                                                        .get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access staffLoginOrganizationDetailList2Application
        Map<ApplicationBaseDto, OrganizationRefApplicationBaseVo>
                staffLoginOrganizationDetailList2ApplicationVoMap =
                        organizationRefApplicationBaseVoConverter
                                .convertToOrganizationRefApplicationBaseVoMap(
                                        staffLoginOrganizationDetailList2ApplicationList);
        dataHolder.staffLoginOrganizationDetailList2Application =
                staffLoginOrganizationDetailList2ApplicationList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                staffLoginOrganizationDetailList2ApplicationVoMap
                                                        .get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access staffExtension
        Map<StaffExtensionBaseDto, StaffExtensionBaseVo> staffExtensionVoMap =
                staffExtensionBaseVoConverter.convertToStaffExtensionBaseVoMap(staffExtensionList);
        dataHolder.staffExtension =
                staffExtensionList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> staffExtensionVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access staffEmploymentList
        Map<StaffEmploymentBaseDto, StaffEmploymentBaseVo> staffEmploymentListVoMap =
                staffEmploymentBaseVoConverter.convertToStaffEmploymentBaseVoMap(
                        staffEmploymentListList);
        dataHolder.staffEmploymentList =
                staffEmploymentListList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> staffEmploymentListVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access accountingOrganization
        Map<OrganizationBaseDto, OrganizationSimpleVo> accountingOrganizationVoMap =
                organizationSimpleVoConverter.convertToOrganizationSimpleVoMap(
                        accountingOrganizationList);
        dataHolder.accountingOrganization =
                accountingOrganizationList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> accountingOrganizationVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access hrOrganization
        Map<OrganizationBaseDto, OrganizationSimpleVo> hrOrganizationVoMap =
                organizationSimpleVoConverter.convertToOrganizationSimpleVoMap(hrOrganizationList);
        dataHolder.hrOrganization =
                hrOrganizationList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> hrOrganizationVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access staffPracticeList
        Map<StaffPracticeBaseDto, StaffPracticeBaseVo> staffPracticeListVoMap =
                staffPracticeBaseVoConverter.convertToStaffPracticeBaseVoMap(staffPracticeListList);
        dataHolder.staffPracticeList =
                staffPracticeListList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> staffPracticeListVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access organization
        Map<OrganizationBaseDto, OrganizationSimpleVo> organizationVoMap =
                organizationSimpleVoConverter.convertToOrganizationSimpleVoMap(organizationList);
        dataHolder.organization =
                organizationList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> organizationVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "568b8197-d0dc-3387-a550-29bdb65aa339")
    public void collectDataDefault(StaffDetailVoDataHolder dataHolder) {
        staffDetailVoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "9430c124-b838-392d-a77f-d828184cdee0")
    private void fillDataWhenNecessary(StaffDetailVoDataHolder dataHolder) {
        List<StaffBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.staffUserList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<StaffUserBaseDto> baseDtoList =
                    staffUserBaseDtoService.getByStaffIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(StaffUserBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<StaffUserBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(StaffUserBaseDto::getStaffId));
            Map<String, StaffUserDto> staffUserDtoMap =
                    staffUserDtoConverter
                            .convertFromStaffUserBaseDtoToStaffUserDto(baseDtoList)
                            .stream()
                            .collect(Collectors.toMap(StaffUserDto::getId, Function.identity()));
            Map<StaffUserDto, StaffUserVo> dtoVoMap =
                    staffUserVoConverter.convertToStaffUserVoMap(
                            new ArrayList<>(staffUserDtoMap.values()));
            Map<StaffUserBaseDto, StaffUserVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .filter(baseDto -> staffUserDtoMap.containsKey(baseDto.getId()))
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    dtoVoMap.get(
                                                            staffUserDtoMap.get(baseDto.getId())),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.staffUserList =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.staffEducationList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<StaffEducationBaseDto> baseDtoList =
                    staffEducationBaseDtoService.getByStaffIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(StaffEducationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<StaffEducationBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(StaffEducationBaseDto::getStaffId));
            Map<StaffEducationBaseDto, StaffEducationBaseVo> dtoVoMap =
                    staffEducationBaseVoConverter.convertToStaffEducationBaseVoMap(baseDtoList);
            Map<StaffEducationBaseDto, StaffEducationBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.staffEducationList =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.staffWorkExperienceList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<StaffWorkExperienceBaseDto> baseDtoList =
                    staffWorkExperienceBaseDtoService.getByStaffIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(StaffWorkExperienceBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<StaffWorkExperienceBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(StaffWorkExperienceBaseDto::getStaffId));
            Map<StaffWorkExperienceBaseDto, StaffWorkExperienceBaseVo> dtoVoMap =
                    staffWorkExperienceBaseVoConverter.convertToStaffWorkExperienceBaseVoMap(
                            baseDtoList);
            Map<StaffWorkExperienceBaseDto, StaffWorkExperienceBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.staffWorkExperienceList =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.staffLoginOrganizationDetailList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<StaffOrganizationBaseDto> baseDtoList =
                    staffOrganizationBaseDtoService.getByStaffIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(StaffOrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<StaffOrganizationBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(StaffOrganizationBaseDto::getStaffId));
            Map<String, StaffOrganizationDto> staffOrganizationDtoMap =
                    staffOrganizationDtoConverter
                            .convertFromStaffOrganizationBaseDtoToStaffOrganizationDto(baseDtoList)
                            .stream()
                            .collect(
                                    Collectors.toMap(
                                            StaffOrganizationDto::getId, Function.identity()));
            Map<StaffOrganizationDto, StaffOrganizationVo> dtoVoMap =
                    staffOrganizationVoConverter.convertToStaffOrganizationVoMap(
                            new ArrayList<>(staffOrganizationDtoMap.values()));
            Map<StaffOrganizationBaseDto, StaffOrganizationVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .filter(baseDto -> staffOrganizationDtoMap.containsKey(baseDto.getId()))
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    dtoVoMap.get(
                                                            staffOrganizationDtoMap.get(
                                                                    baseDto.getId())),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.staffLoginOrganizationDetailList =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.staffExtension == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<StaffExtensionBaseDto> baseDtoList =
                    staffExtensionBaseDtoService.getByStaffIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(StaffExtensionBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<StaffExtensionBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(StaffExtensionBaseDto::getStaffId));
            Map<StaffExtensionBaseDto, StaffExtensionBaseVo> dtoVoMap =
                    staffExtensionBaseVoConverter.convertToStaffExtensionBaseVoMap(baseDtoList);
            Map<StaffExtensionBaseDto, StaffExtensionBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.staffExtension =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.staffEmploymentList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<StaffEmploymentBaseDto> baseDtoList =
                    staffEmploymentBaseDtoService.getByStaffIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(StaffEmploymentBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<StaffEmploymentBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(StaffEmploymentBaseDto::getStaffId));
            Map<StaffEmploymentBaseDto, StaffEmploymentBaseVo> dtoVoMap =
                    staffEmploymentBaseVoConverter.convertToStaffEmploymentBaseVoMap(baseDtoList);
            Map<StaffEmploymentBaseDto, StaffEmploymentBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.staffEmploymentList =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.accountingOrganization == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getAccountingOrganizationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            Map<OrganizationBaseDto, OrganizationSimpleVo> dtoVoMap =
                    organizationSimpleVoConverter.convertToOrganizationSimpleVoMap(baseDtoList);
            Map<OrganizationBaseDto, OrganizationSimpleVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.accountingOrganization =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getAccountingOrganizationId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.hrOrganization == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getHrOrganizationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            Map<OrganizationBaseDto, OrganizationSimpleVo> dtoVoMap =
                    organizationSimpleVoConverter.convertToOrganizationSimpleVoMap(baseDtoList);
            Map<OrganizationBaseDto, OrganizationSimpleVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.hrOrganization =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getHrOrganizationId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.staffPracticeList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<StaffPracticeBaseDto> baseDtoList =
                    staffPracticeBaseDtoService.getByStaffIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(StaffPracticeBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<StaffPracticeBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(StaffPracticeBaseDto::getStaffId));
            Map<StaffPracticeBaseDto, StaffPracticeBaseVo> dtoVoMap =
                    staffPracticeBaseVoConverter.convertToStaffPracticeBaseVoMap(baseDtoList);
            Map<StaffPracticeBaseDto, StaffPracticeBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.staffPracticeList =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.organization == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getOrganizationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            Map<OrganizationBaseDto, OrganizationSimpleVo> dtoVoMap =
                    organizationSimpleVoConverter.convertToOrganizationSimpleVoMap(baseDtoList);
            Map<OrganizationBaseDto, OrganizationSimpleVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.organization =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getOrganizationId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.staffUserList2Staff == null) {
            Set<String> ids =
                    dataHolder.staffUserList.keySet().stream()
                            .map(StaffUserBaseDto::getStaffId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<StaffBaseDto> baseDtoList =
                    staffBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(StaffBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, StaffBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.toMap(StaffBaseDto::getId, Function.identity()));
            Map<StaffBaseDto, StaffBaseVo> dtoVoMap =
                    staffBaseVoConverter.convertToStaffBaseVoMap(baseDtoList);
            Map<StaffBaseDto, StaffBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.staffUserList2Staff =
                    dataHolder.staffUserList.keySet().stream()
                            .map(StaffUserBaseDto::getStaffId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.staffUserList2User == null) {
            Set<String> ids =
                    dataHolder.staffUserList.keySet().stream()
                            .map(StaffUserBaseDto::getUserId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<UserBaseDto> baseDtoList =
                    userBaseDtoServiceInOrganizationRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(UserBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, UserBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.toMap(UserBaseDto::getId, Function.identity()));
            Map<UserBaseDto, OrganizationRefUserBaseVo> dtoVoMap =
                    organizationRefUserBaseVoConverter.convertToOrganizationRefUserBaseVoMap(
                            baseDtoList);
            Map<UserBaseDto, OrganizationRefUserBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.staffUserList2User =
                    dataHolder.staffUserList.keySet().stream()
                            .map(StaffUserBaseDto::getUserId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.staffLoginOrganizationDetailList2Organization == null) {
            Set<String> ids =
                    dataHolder.staffLoginOrganizationDetailList.keySet().stream()
                            .map(StaffOrganizationBaseDto::getOrganizationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            Map<OrganizationBaseDto, OrganizationBaseVo> dtoVoMap =
                    organizationBaseVoConverter.convertToOrganizationBaseVoMap(baseDtoList);
            Map<OrganizationBaseDto, OrganizationBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.staffLoginOrganizationDetailList2Organization =
                    dataHolder.staffLoginOrganizationDetailList.keySet().stream()
                            .map(StaffOrganizationBaseDto::getOrganizationId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.staffLoginOrganizationDetailList2Application == null) {
            Set<String> ids =
                    dataHolder.staffLoginOrganizationDetailList.keySet().stream()
                            .map(StaffOrganizationBaseDto::getApplicationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ApplicationBaseDto> baseDtoList =
                    applicationBaseDtoServiceInOrganizationRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(ApplicationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, ApplicationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            ApplicationBaseDto::getId, Function.identity()));
            Map<ApplicationBaseDto, OrganizationRefApplicationBaseVo> dtoVoMap =
                    organizationRefApplicationBaseVoConverter
                            .convertToOrganizationRefApplicationBaseVoMap(baseDtoList);
            Map<ApplicationBaseDto, OrganizationRefApplicationBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.staffLoginOrganizationDetailList2Application =
                    dataHolder.staffLoginOrganizationDetailList.keySet().stream()
                            .map(StaffOrganizationBaseDto::getApplicationId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }
}
