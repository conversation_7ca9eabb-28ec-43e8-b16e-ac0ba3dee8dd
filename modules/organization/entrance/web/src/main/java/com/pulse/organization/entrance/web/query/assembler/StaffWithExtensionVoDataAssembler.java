package com.pulse.organization.entrance.web.query.assembler;

import com.pulse.organization.entrance.web.vo.OrganizationSimpleVo;
import com.pulse.organization.entrance.web.vo.StaffExtensionBaseVo;
import com.pulse.organization.entrance.web.vo.StaffWithExtensionVo;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.StaffBaseDto;
import com.pulse.organization.manager.dto.StaffExtensionBaseDto;
import com.pulse.organization.service.StaffBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** StaffWithExtensionVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "54b2190d-44ac-31ac-a9c5-bd94d314d8cf")
public class StaffWithExtensionVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private StaffBaseDtoService staffBaseDtoService;

    /** 批量自定义组装StaffWithExtensionVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "0529fda0-d19e-3c9d-b506-0e255ee30ba8")
    public void assembleDataCustomized(List<StaffWithExtensionVo> dataList) {
        // 自定义数据组装

    }

    /** 组装StaffWithExtensionVo数据 */
    @AutoGenerated(locked = true, uuid = "1d7a3e31-2c02-3709-9108-6aaef29fb92a")
    public void assembleData(
            Map<String, StaffWithExtensionVo> voMap,
            StaffWithExtensionVoDataAssembler.StaffWithExtensionVoDataHolder dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<StaffBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, Pair<OrganizationBaseDto, OrganizationSimpleVo>> accountingOrganization =
                dataHolder.accountingOrganization.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getId(),
                                        dto ->
                                                Pair.of(
                                                        dto,
                                                        dataHolder.accountingOrganization.get(dto)),
                                        (o1, o2) -> o1));
        Map<String, Pair<OrganizationBaseDto, OrganizationSimpleVo>> hrOrganization =
                dataHolder.hrOrganization.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getId(),
                                        dto -> Pair.of(dto, dataHolder.hrOrganization.get(dto)),
                                        (o1, o2) -> o1));
        Map<String, Pair<StaffExtensionBaseDto, StaffExtensionBaseVo>> staffExtension =
                dataHolder.staffExtension.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getStaffId(),
                                        dto -> Pair.of(dto, dataHolder.staffExtension.get(dto)),
                                        (o1, o2) -> o1));
        Map<String, Pair<OrganizationBaseDto, OrganizationSimpleVo>> organization =
                dataHolder.organization.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getId(),
                                        dto -> Pair.of(dto, dataHolder.organization.get(dto)),
                                        (o1, o2) -> o1));

        for (StaffBaseDto baseDto : baseDtoList) {
            StaffWithExtensionVo vo = voMap.get(baseDto.getId());
            vo.setAccountingOrganization(
                    Optional.ofNullable(
                                    accountingOrganization.get(
                                            baseDto.getAccountingOrganizationId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
            vo.setHrOrganization(
                    Optional.ofNullable(hrOrganization.get(baseDto.getHrOrganizationId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
            vo.setStaffExtension(
                    Optional.ofNullable(staffExtension.get(baseDto.getId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
            vo.setOrganization(
                    Optional.ofNullable(organization.get(baseDto.getOrganizationId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class StaffWithExtensionVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<StaffBaseDto> rootBaseDtoList;

        /** 持有字段accountingOrganization的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<OrganizationBaseDto, OrganizationSimpleVo> accountingOrganization;

        /** 持有字段hrOrganization的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<OrganizationBaseDto, OrganizationSimpleVo> hrOrganization;

        /** 持有字段staffExtension的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<StaffExtensionBaseDto, StaffExtensionBaseVo> staffExtension;

        /** 持有字段organization的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<OrganizationBaseDto, OrganizationSimpleVo> organization;
    }
}
