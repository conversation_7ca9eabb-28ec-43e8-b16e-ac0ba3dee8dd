package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.entrance.web.query.assembler.WardBaseVoDataAssembler;
import com.pulse.organization.entrance.web.vo.WardBaseVo;
import com.pulse.organization.manager.dto.WardBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到WardBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "58785e6c-3058-4f0a-acc1-272a0d0f2e3a|VO|CONVERTER")
public class WardBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private WardBaseVoDataAssembler wardBaseVoDataAssembler;

    /** 使用默认方式组装WardBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "102e162d-a604-3dfe-8ffa-60aff8d58e88")
    public List<WardBaseVo> convertAndAssembleDataList(List<WardBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, WardBaseVo> voMap =
                convertToWardBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        wardBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把WardBaseDto转换成WardBaseVo */
    @AutoGenerated(locked = false, uuid = "58785e6c-3058-4f0a-acc1-272a0d0f2e3a-converter-Map")
    public Map<WardBaseDto, WardBaseVo> convertToWardBaseVoMap(List<WardBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<WardBaseDto, WardBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            WardBaseVo vo = new WardBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setWardType(dto.getWardType());
                                            vo.setInjectionStartTime(dto.getInjectionStartTime());
                                            vo.setTreatmentStartTime(dto.getTreatmentStartTime());
                                            vo.setOralMedicationStartTime(
                                                    dto.getOralMedicationStartTime());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setCampusOrganizationId(
                                                    dto.getCampusOrganizationId());
                                            vo.setOrganizationId(dto.getOrganizationId());
                                            vo.setMaxBedCount(dto.getMaxBedCount());
                                            vo.setShellPosition(dto.getShellPosition());
                                            vo.setVirtualWardFlag(dto.getVirtualWardFlag());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setDeletedBy(dto.getDeletedBy());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把WardBaseDto转换成WardBaseVo */
    @AutoGenerated(locked = true, uuid = "58785e6c-3058-4f0a-acc1-272a0d0f2e3a-converter-list")
    public List<WardBaseVo> convertToWardBaseVoList(List<WardBaseDto> dtoList) {
        return new ArrayList<>(convertToWardBaseVoMap(dtoList).values());
    }

    /** 把WardBaseDto转换成WardBaseVo */
    @AutoGenerated(locked = true, uuid = "b41f28ee-b0c0-36f3-a5ab-dc610edf6ab4")
    public WardBaseVo convertToWardBaseVo(WardBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToWardBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装WardBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "cb5b62f3-ce90-350b-bf0e-cef40e54eed1")
    public WardBaseVo convertAndAssembleData(WardBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }
}
