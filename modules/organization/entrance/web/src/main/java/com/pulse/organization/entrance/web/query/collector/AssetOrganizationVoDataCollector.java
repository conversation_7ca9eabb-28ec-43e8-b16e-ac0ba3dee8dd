package com.pulse.organization.entrance.web.query.collector;

import com.pulse.application.manager.dto.ApplicationBaseDto;
import com.pulse.organization.entrance.web.converter.AssetOrganizationVoConverter;
import com.pulse.organization.entrance.web.converter.OrganizationRefApplicationBaseVoConverter;
import com.pulse.organization.entrance.web.converter.OrganizationSimpleVoConverter;
import com.pulse.organization.entrance.web.query.assembler.AssetOrganizationVoDataAssembler.AssetOrganizationVoDataHolder;
import com.pulse.organization.entrance.web.vo.OrganizationRefApplicationBaseVo;
import com.pulse.organization.entrance.web.vo.OrganizationSimpleVo;
import com.pulse.organization.manager.dto.AssetOrganizationBaseDto;
import com.pulse.organization.manager.dto.AssetOrganizationDto;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.facade.application.ApplicationBaseDtoServiceInOrganizationRpcAdapter;
import com.pulse.organization.service.AssetOrganizationBaseDtoService;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装AssetOrganizationVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "b8971a88-ee41-337a-802f-************")
public class AssetOrganizationVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private ApplicationBaseDtoServiceInOrganizationRpcAdapter
            applicationBaseDtoServiceInOrganizationRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private AssetOrganizationBaseDtoService assetOrganizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private AssetOrganizationVoConverter assetOrganizationVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private AssetOrganizationVoDataCollector assetOrganizationVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationRefApplicationBaseVoConverter organizationRefApplicationBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationSimpleVoConverter organizationSimpleVoConverter;

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "b00ef450-9c6e-3065-b2d6-4fc5c5f0e7d6")
    public void collectDataDefault(AssetOrganizationVoDataHolder dataHolder) {
        assetOrganizationVoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 获取AssetOrganizationDto数据填充AssetOrganizationVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "bc235cd7-8cd3-3dcf-906a-6a713cb4d690")
    public void collectDataWithDtoData(
            List<AssetOrganizationDto> dtoList, AssetOrganizationVoDataHolder dataHolder) {
        List<ApplicationBaseDto> applicationList = new ArrayList<>();
        List<OrganizationBaseDto> organizationList = new ArrayList<>();

        for (AssetOrganizationDto rootDto : dtoList) {
            ApplicationBaseDto applicationDto = rootDto.getApplication();
            if (applicationDto != null) {
                applicationList.add(applicationDto);
            }
            OrganizationBaseDto organizationDto = rootDto.getOrganization();
            if (organizationDto != null) {
                organizationList.add(organizationDto);
            }
        }

        // access application
        Map<ApplicationBaseDto, OrganizationRefApplicationBaseVo> applicationVoMap =
                organizationRefApplicationBaseVoConverter
                        .convertToOrganizationRefApplicationBaseVoMap(applicationList);
        dataHolder.application =
                applicationList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> applicationVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access organization
        Map<OrganizationBaseDto, OrganizationSimpleVo> organizationVoMap =
                organizationSimpleVoConverter.convertToOrganizationSimpleVoMap(organizationList);
        dataHolder.organization =
                organizationList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> organizationVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "c6554f78-fafe-3c59-b71c-c143ff21bcdc")
    private void fillDataWhenNecessary(AssetOrganizationVoDataHolder dataHolder) {
        List<AssetOrganizationBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.application == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(AssetOrganizationBaseDto::getApplicationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ApplicationBaseDto> baseDtoList =
                    applicationBaseDtoServiceInOrganizationRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(ApplicationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, ApplicationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            ApplicationBaseDto::getId, Function.identity()));
            Map<ApplicationBaseDto, OrganizationRefApplicationBaseVo> dtoVoMap =
                    organizationRefApplicationBaseVoConverter
                            .convertToOrganizationRefApplicationBaseVoMap(baseDtoList);
            Map<ApplicationBaseDto, OrganizationRefApplicationBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.application =
                    rootDtoList.stream()
                            .map(AssetOrganizationBaseDto::getApplicationId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.organization == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(AssetOrganizationBaseDto::getOrganizationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            Map<OrganizationBaseDto, OrganizationSimpleVo> dtoVoMap =
                    organizationSimpleVoConverter.convertToOrganizationSimpleVoMap(baseDtoList);
            Map<OrganizationBaseDto, OrganizationSimpleVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.organization =
                    rootDtoList.stream()
                            .map(AssetOrganizationBaseDto::getOrganizationId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }
}
