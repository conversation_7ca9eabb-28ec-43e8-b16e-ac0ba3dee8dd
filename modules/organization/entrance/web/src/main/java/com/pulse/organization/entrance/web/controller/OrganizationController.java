package com.pulse.organization.entrance.web.controller;

import com.pulse.organization.entrance.web.converter.OrganizationBaseVoConverter;
import com.pulse.organization.entrance.web.vo.OrganizationBaseVo;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.persist.qto.SearchOrganizationQto;
import com.pulse.organization.service.query.OrganizationBaseDtoQueryService;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "b763b64d-952e-357c-b698-dfafe8ae84fb")
public class OrganizationController {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoQueryService organizationBaseDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseVoConverter organizationBaseVoConverter;

    /** 查询组织数据 */
    @PublicInterface(id = "627190cb-0892-4116-a574-cf9863624398", version = "1744773727741")
    @AutoGenerated(locked = false, uuid = "627190cb-0892-4116-a574-cf9863624398")
    @RequestMapping(
            value = {"/api/organization/search-organization"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<OrganizationBaseVo> searchOrganization(@Valid SearchOrganizationQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<OrganizationBaseDto> rpcResult =
                organizationBaseDtoQueryService.searchOrganization(qto);
        List<OrganizationBaseVo> result =
                organizationBaseVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查询组织数据 */
    @PublicInterface(id = "6efb9eee-bab9-4d24-b35d-33ba71ac3dca", version = "1744773713886")
    @AutoGenerated(locked = false, uuid = "6efb9eee-bab9-4d24-b35d-33ba71ac3dca")
    @RequestMapping(
            value = {"/api/organization/search-organization-count"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public Integer searchOrganizationCount(@Valid SearchOrganizationQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        Integer result = organizationBaseDtoQueryService.searchOrganizationCount(qto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查询组织数据 */
    @PublicInterface(id = "9523d57b-1375-43d6-ac89-bb59f0d65017", version = "1744773697943")
    @AutoGenerated(locked = false, uuid = "9523d57b-1375-43d6-ac89-bb59f0d65017")
    @RequestMapping(
            value = {"/api/organization/search-organization-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<OrganizationBaseVo> searchOrganizationPaged(
            @Valid SearchOrganizationQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<OrganizationBaseDto> dtoResult =
                organizationBaseDtoQueryService.searchOrganizationPaged(qto);
        VSQueryResult<OrganizationBaseVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(
                organizationBaseVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查询组织数据 */
    @PublicInterface(id = "e2a0f376-4c6a-4a1a-95e0-593963f881bd", version = "1744773677460")
    @AutoGenerated(locked = false, uuid = "e2a0f376-4c6a-4a1a-95e0-593963f881bd")
    @RequestMapping(
            value = {"/api/organization/search-organization-waterfall"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<OrganizationBaseVo> searchOrganizationWaterfall(
            @Valid SearchOrganizationQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<OrganizationBaseDto> dtoResult =
                organizationBaseDtoQueryService.searchOrganizationWaterfall(qto);
        VSQueryResult<OrganizationBaseVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(
                organizationBaseVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }
}
