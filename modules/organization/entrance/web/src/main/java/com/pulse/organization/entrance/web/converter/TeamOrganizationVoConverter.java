package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.entrance.web.query.assembler.TeamOrganizationVoDataAssembler;
import com.pulse.organization.entrance.web.query.assembler.TeamOrganizationVoDataAssembler.TeamOrganizationVoDataHolder;
import com.pulse.organization.entrance.web.query.collector.TeamOrganizationVoDataCollector;
import com.pulse.organization.entrance.web.vo.OrganizationBaseVo;
import com.pulse.organization.entrance.web.vo.TeamOrganizationVo;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.TeamOrganizationDto;
import com.pulse.organization.service.TeamOrganizationBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到TeamOrganizationVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "6fe8a5a8-b6e0-49f5-9e0a-89e34d91a2f9|VO|CONVERTER")
public class TeamOrganizationVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseVoConverter organizationBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TeamOrganizationBaseDtoService teamOrganizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TeamOrganizationVoDataAssembler teamOrganizationVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private TeamOrganizationVoDataCollector teamOrganizationVoDataCollector;

    /** 使用默认方式组装TeamOrganizationVo列表数据 */
    @AutoGenerated(locked = true, uuid = "279f3cda-2f38-3bcb-b85d-314e96ffc6be")
    public List<TeamOrganizationVo> convertAndAssembleDataList(List<TeamOrganizationDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        TeamOrganizationVoDataHolder dataHolder = new TeamOrganizationVoDataHolder();
        dataHolder.setRootBaseDtoList(
                teamOrganizationBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(TeamOrganizationDto::getId)
                                .collect(Collectors.toList())));
        Map<String, TeamOrganizationVo> voMap =
                convertToTeamOrganizationVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        teamOrganizationVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        teamOrganizationVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把TeamOrganizationDto转换成TeamOrganizationVo */
    @AutoGenerated(locked = false, uuid = "6fe8a5a8-b6e0-49f5-9e0a-89e34d91a2f9-converter-Map")
    public Map<TeamOrganizationDto, TeamOrganizationVo> convertToTeamOrganizationVoMap(
            List<TeamOrganizationDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<OrganizationBaseDto, OrganizationBaseVo> organizationMap =
                organizationBaseVoConverter.convertToOrganizationBaseVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(TeamOrganizationDto::getOrganization)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<TeamOrganizationDto, TeamOrganizationVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            TeamOrganizationVo vo = new TeamOrganizationVo();
                                            vo.setId(dto.getId());
                                            vo.setTeamId(dto.getTeamId());
                                            vo.setOrganization(
                                                    dto.getOrganization() == null
                                                            ? null
                                                            : organizationMap.get(
                                                                    dto.getOrganization()));
                                            vo.setSortNumber(dto.getSortNumber());
                                            vo.setRegisterTypeIdList(dto.getRegisterTypeIdList());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setDefaultFlag(dto.getDefaultFlag());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setDeletedBy(dto.getDeletedBy());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把TeamOrganizationDto转换成TeamOrganizationVo */
    @AutoGenerated(locked = true, uuid = "6fe8a5a8-b6e0-49f5-9e0a-89e34d91a2f9-converter-list")
    public List<TeamOrganizationVo> convertToTeamOrganizationVoList(
            List<TeamOrganizationDto> dtoList) {
        return new ArrayList<>(convertToTeamOrganizationVoMap(dtoList).values());
    }

    /** 使用默认方式组装TeamOrganizationVo数据 */
    @AutoGenerated(locked = true, uuid = "a815e52f-1963-3e2d-acf1-10a3d95dff40")
    public TeamOrganizationVo convertAndAssembleData(TeamOrganizationDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把TeamOrganizationDto转换成TeamOrganizationVo */
    @AutoGenerated(locked = true, uuid = "d1d49847-9ce6-3a5e-9520-8cda3c9a6300")
    public TeamOrganizationVo convertToTeamOrganizationVo(TeamOrganizationDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToTeamOrganizationVoList(List.of(dto)).stream().findAny().orElse(null);
    }
}
