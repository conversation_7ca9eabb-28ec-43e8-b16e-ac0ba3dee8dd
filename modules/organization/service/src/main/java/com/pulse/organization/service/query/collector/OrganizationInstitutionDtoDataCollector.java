package com.pulse.organization.service.query.collector;

import com.pulse.organization.manager.dto.InstitutionBaseDto;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.service.InstitutionBaseDtoService;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.pulse.organization.service.query.assembler.OrganizationInstitutionDtoDataAssembler.OrganizationInstitutionDtoDataHolder;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装OrganizationInstitutionDto所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "d97b9b9c-7a45-3da9-907d-6f2388accb81")
public class OrganizationInstitutionDtoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private InstitutionBaseDtoService institutionBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationInstitutionDtoDataCollector organizationInstitutionDtoDataCollector;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "6e1e4dda-39c9-35bf-ac92-0e13f68f3c2e")
    private void fillDataWhenNecessary(OrganizationInstitutionDtoDataHolder dataHolder) {
        List<OrganizationBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.institution == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(OrganizationBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<InstitutionBaseDto> baseDtoList =
                    institutionBaseDtoService.getByOrganizationIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(InstitutionBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<InstitutionBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(InstitutionBaseDto::getOrganizationId));
            dataHolder.institution =
                    rootDtoList.stream()
                            .map(OrganizationBaseDto::getId)
                            .distinct()
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "9f92d7ce-93fa-3ea3-9770-2ee70b88e1c5")
    public void collectDataDefault(OrganizationInstitutionDtoDataHolder dataHolder) {
        organizationInstitutionDtoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
