package com.pulse.organization.service.query.assembler;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.manager.dto.TeamBaseDto;
import com.pulse.organization.manager.dto.TeamMemberBaseDto;
import com.pulse.organization.manager.dto.TeamWithMemberDto;
import com.pulse.organization.service.TeamBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** TeamWithMemberDto数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "b344b4e7-d10e-302d-801f-17bb1836595d")
public class TeamWithMemberDtoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private TeamBaseDtoService teamBaseDtoService;

    /** 批量自定义组装TeamWithMemberDto数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "28f064d1-0063-3777-984d-1a0f7a1e382c")
    public void assembleDataCustomized(List<TeamWithMemberDto> dataList) {
        // 自定义数据组装

    }

    /** 组装TeamWithMemberDto数据 */
    @AutoGenerated(locked = true, uuid = "d87a87b0-c885-394c-bfd0-28eac62b3622")
    public void assembleData(
            List<TeamWithMemberDto> dtoList,
            TeamWithMemberDtoDataAssembler.TeamWithMemberDtoDataHolder dataHolder) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        Map<String, TeamBaseDto> baseDtoMap =
                dataHolder.getRootBaseDtoList().stream()
                        .collect(Collectors.toMap(TeamBaseDto::getId, Function.identity()));

        Map<String, List<TeamMemberBaseDto>> teamMemberList =
                dataHolder.teamMemberList.stream()
                        .collect(Collectors.groupingBy(TeamMemberBaseDto::getTeamId));

        for (TeamWithMemberDto dto : dtoList) {
            dto.setTeamMemberList(
                    Optional.ofNullable(teamMemberList.get(dto.getId())).orElse(new ArrayList<>()));
        }

        assembleDataCustomized(dtoList); // 自定义处理逻辑
    }

    /** 持有Dto需要的全部数据 */
    @Getter
    @Setter
    public static class TeamWithMemberDtoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<TeamBaseDto> rootBaseDtoList;

        /** 持有dto字段teamMemberList的Dto数据 */
        @AutoGenerated(locked = true)
        public List<TeamMemberBaseDto> teamMemberList;
    }
}
