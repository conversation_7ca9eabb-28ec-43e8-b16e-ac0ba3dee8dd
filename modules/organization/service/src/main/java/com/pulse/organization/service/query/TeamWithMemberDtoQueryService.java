package com.pulse.organization.service.query;

import com.pulse.organization.manager.converter.TeamWithMemberDtoConverter;
import com.pulse.organization.manager.dto.TeamBaseDto;
import com.pulse.organization.manager.dto.TeamWithMemberDto;
import com.pulse.organization.persist.qto.ListTeamWithMemberByStaffIdQto;
import com.pulse.organization.service.TeamBaseDtoService;
import com.pulse.organization.service.index.entity.ListTeamWithMemberByStaffIdQtoService;
import com.pulse.organization.service.query.assembler.TeamWithMemberDtoDataAssembler;
import com.pulse.organization.service.query.assembler.TeamWithMemberDtoDataAssembler.TeamWithMemberDtoDataHolder;
import com.pulse.organization.service.query.collector.TeamWithMemberDtoDataCollector;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** TeamWithMemberDto查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "7447a943-a683-3a11-bc50-218cd7619233")
public class TeamWithMemberDtoQueryService {
    @AutoGenerated(locked = true)
    @Resource
    private ListTeamWithMemberByStaffIdQtoService listTeamWithMemberByStaffIdQtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TeamBaseDtoService teamBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TeamWithMemberDtoConverter teamWithMemberDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TeamWithMemberDtoDataAssembler teamWithMemberDtoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private TeamWithMemberDtoDataCollector teamWithMemberDtoDataCollector;

    /** 根据ListTeamWithMemberByStaffIdQto查询TeamWithMemberDto列表,上限500 */
    @PublicInterface(
            id = "05841ab0-7696-4f1b-93a0-f62991a9bc92",
            module = "organization",
            moduleId = "a3b95408-2257-4bfa-aafe-59cc5547e63c",
            pubRpc = true,
            version = "1748497001003")
    @AutoGenerated(locked = false, uuid = "8ee5bdbe-a8fa-3baa-946a-9839f05c69d1")
    public List<TeamWithMemberDto> listTeamWithMemberByStaffId(
            @Valid @NotNull ListTeamWithMemberByStaffIdQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = listTeamWithMemberByStaffIdQtoService.query(qto);
        TeamWithMemberDtoDataHolder dataHolder = new TeamWithMemberDtoDataHolder();
        List<TeamWithMemberDto> result = toDtoList(ids, dataHolder);
        teamWithMemberDtoDataCollector.collectDataDefault(dataHolder);
        teamWithMemberDtoDataAssembler.assembleData(result, dataHolder);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 将ID列表转换为DtoList */
    @AutoGenerated(locked = true, uuid = "dbde8518-7711-3842-8d5e-4d9ad696fb4c")
    private List<TeamWithMemberDto> toDtoList(
            List<String> ids, TeamWithMemberDtoDataHolder dataHolder) {
        List<TeamBaseDto> baseDtoList = teamBaseDtoService.getByIds(ids);
        dataHolder.setRootBaseDtoList(baseDtoList);
        Map<String, TeamWithMemberDto> dtoMap =
                teamWithMemberDtoConverter
                        .convertFromTeamBaseDtoToTeamWithMemberDto(baseDtoList)
                        .stream()
                        .collect(Collectors.toMap(TeamWithMemberDto::getId, Function.identity()));
        return ids.stream()
                .map(id -> dtoMap.get(id))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
