package com.pulse.organization.service.index.entity;

import cn.hutool.core.lang.Assert;

import com.pulse.organization.persist.mapper.SearchRegisterDepartmentQtoDao;
import com.pulse.organization.persist.qto.SearchRegisterDepartmentQto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = true, uuid = "7aa02808-0d1a-493f-976c-1df693124ce9|QTO|SERVICE")
public class SearchRegisterDepartmentQtoService {
    @AutoGenerated(locked = true)
    @Resource
    private SearchRegisterDepartmentQtoDao searchRegisterDepartmentMapper;

    /** 不分页查询入口 */
    @AutoGenerated(locked = true, uuid = "7aa02808-0d1a-493f-976c-1df693124ce9-query")
    public List<String> query(SearchRegisterDepartmentQto qto) {
        return searchRegisterDepartmentMapper.query(qto);
    }

    /** 分页查询入口 */
    @AutoGenerated(locked = true, uuid = "7aa02808-0d1a-493f-976c-1df693124ce9-query-paged")
    public List<String> queryPaged(SearchRegisterDepartmentQto qto) {
        Assert.isTrue(qto.getSize() != null && qto.getSize() > 0, "size 字段必须大于0 ");
        Assert.isTrue(qto.getFrom() != null && qto.getFrom() >= 0, "from 字段必须大于等于0 ");
        return searchRegisterDepartmentMapper.queryPaged(qto);
    }

    /** 计算总数量 */
    @AutoGenerated(locked = true)
    public Integer count(SearchRegisterDepartmentQto qto) {
        return searchRegisterDepartmentMapper.count(qto);
    }
}
