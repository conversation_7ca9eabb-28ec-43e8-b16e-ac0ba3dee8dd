package com.pulse.organization.service.index.entity;

import com.pulse.organization.persist.mapper.SearchOrganizationRelationshipQtoDao;
import com.pulse.organization.persist.qto.SearchOrganizationRelationshipQto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = true, uuid = "3a3abd6f-f13e-41ca-886c-16d5f8b57ecf|QTO|SERVICE")
public class SearchOrganizationRelationshipQtoService {
    @AutoGenerated(locked = true)
    @Resource
    private SearchOrganizationRelationshipQtoDao searchOrganizationRelationshipMapper;

    /** 不分页查询入口 */
    @AutoGenerated(locked = true, uuid = "3a3abd6f-f13e-41ca-886c-16d5f8b57ecf-query")
    public List<String> query(SearchOrganizationRelationshipQto qto) {
        return searchOrganizationRelationshipMapper.query(qto);
    }

    /** 计算总数量 */
    @AutoGenerated(locked = true)
    public Integer count(SearchOrganizationRelationshipQto qto) {
        return searchOrganizationRelationshipMapper.count(qto);
    }
}
