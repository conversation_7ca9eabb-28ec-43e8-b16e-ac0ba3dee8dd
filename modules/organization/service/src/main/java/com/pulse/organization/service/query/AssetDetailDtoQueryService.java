package com.pulse.organization.service.query;

import com.pulse.organization.manager.converter.AssetDetailDtoConverter;
import com.pulse.organization.manager.dto.AssetBaseDto;
import com.pulse.organization.manager.dto.AssetDetailDto;
import com.pulse.organization.persist.qto.SearchAssetQto;
import com.pulse.organization.service.AssetBaseDtoService;
import com.pulse.organization.service.index.entity.SearchAssetQtoService;
import com.pulse.organization.service.query.assembler.AssetDetailDtoDataAssembler;
import com.pulse.organization.service.query.assembler.AssetDetailDtoDataAssembler.AssetDetailDtoDataHolder;
import com.pulse.organization.service.query.collector.AssetDetailDtoDataCollector;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** AssetDetailDto查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "33f217fd-9386-3a74-9548-a2e0266c8b23")
public class AssetDetailDtoQueryService {
    @AutoGenerated(locked = true)
    @Resource
    private AssetBaseDtoService assetBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private AssetDetailDtoConverter assetDetailDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private AssetDetailDtoDataAssembler assetDetailDtoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private AssetDetailDtoDataCollector assetDetailDtoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private SearchAssetQtoService searchAssetQtoService;

    /** 根据SearchAssetQto查询AssetDetailDto列表,分页 */
    @PublicInterface(id = "a3067fac-d26a-4606-b78e-80682201064d", module = "organization")
    @AutoGenerated(locked = false, uuid = "3e608205-383c-331f-87b3-6856da1906d2")
    public VSQueryResult<AssetDetailDto> searchAssetPaged(@Valid @NotNull SearchAssetQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchAssetQtoService.queryPaged(qto);
        AssetDetailDtoDataHolder dataHolder = new AssetDetailDtoDataHolder();
        List<AssetDetailDto> dtoList = toDtoList(ids, dataHolder);
        assetDetailDtoDataCollector.collectDataDefault(dataHolder);
        assetDetailDtoDataAssembler.assembleData(dtoList, dataHolder);
        VSQueryResult result = new VSQueryResult();
        result.setCount(searchAssetQtoService.count(qto));
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 根据SearchAssetQto查询AssetDetailDto列表,瀑布流 */
    @PublicInterface(id = "20511a04-7bdf-4336-867c-d84afb8f7ba3", module = "organization")
    @AutoGenerated(locked = false, uuid = "74ac6eb4-e4db-3e1a-b9da-b99dd1edd890")
    public VSQueryResult<AssetDetailDto> searchAssetWaterfall(@Valid @NotNull SearchAssetQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchAssetQtoService.queryWaterfall(qto);
        AssetDetailDtoDataHolder dataHolder = new AssetDetailDtoDataHolder();
        List<AssetDetailDto> dtoList = toDtoList(ids, dataHolder);
        assetDetailDtoDataCollector.collectDataDefault(dataHolder);
        assetDetailDtoDataAssembler.assembleData(dtoList, dataHolder);
        VSQueryResult result = new VSQueryResult();
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        result.setHasMore(searchAssetQtoService.hasMore(qto));
        result.setScrollId(qto.getScrollId());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 根据SearchAssetQto查询AssetDetailDto列表,上限500 */
    @PublicInterface(
            id = "9f791ae9-552a-4f30-bf0f-fb983ffed122",
            module = "organization",
            moduleId = "a3b95408-2257-4bfa-aafe-59cc5547e63c",
            pubRpc = true,
            version = "1748327111902")
    @AutoGenerated(locked = false, uuid = "8c0bd659-db37-3c06-bf30-d3d4b01e4f50")
    public List<AssetDetailDto> searchAsset(@Valid @NotNull SearchAssetQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchAssetQtoService.query(qto);
        AssetDetailDtoDataHolder dataHolder = new AssetDetailDtoDataHolder();
        List<AssetDetailDto> result = toDtoList(ids, dataHolder);
        assetDetailDtoDataCollector.collectDataDefault(dataHolder);
        assetDetailDtoDataAssembler.assembleData(result, dataHolder);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 根据SearchAssetQto查询数量 */
    @PublicInterface(id = "6f75378c-4d4b-4674-bb68-d5514cdf969b", module = "organization")
    @AutoGenerated(locked = false, uuid = "ae029018-2b26-3ab2-8e41-6378474fceb2")
    public Integer searchAssetCount(@Valid @NotNull SearchAssetQto qto) {
        return searchAssetQtoService.count(qto);
    }

    /** 将ID列表转换为DtoList */
    @AutoGenerated(locked = true, uuid = "b8c746bf-8755-3eb2-b7cc-c9772a5bd7be")
    private List<AssetDetailDto> toDtoList(List<String> ids, AssetDetailDtoDataHolder dataHolder) {
        List<AssetBaseDto> baseDtoList = assetBaseDtoService.getByIds(ids);
        dataHolder.setRootBaseDtoList(baseDtoList);
        Map<String, AssetDetailDto> dtoMap =
                assetDetailDtoConverter
                        .convertFromAssetBaseDtoToAssetDetailDto(baseDtoList)
                        .stream()
                        .collect(Collectors.toMap(AssetDetailDto::getId, Function.identity()));
        return ids.stream()
                .map(id -> dtoMap.get(id))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
