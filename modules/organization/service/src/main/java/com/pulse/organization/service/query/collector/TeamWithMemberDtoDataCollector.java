package com.pulse.organization.service.query.collector;

import com.pulse.organization.manager.dto.TeamBaseDto;
import com.pulse.organization.manager.dto.TeamMemberBaseDto;
import com.pulse.organization.service.TeamBaseDtoService;
import com.pulse.organization.service.TeamMemberBaseDtoService;
import com.pulse.organization.service.query.assembler.TeamWithMemberDtoDataAssembler.TeamWithMemberDtoDataHolder;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装TeamWithMemberDto所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "d250c5b4-2815-3043-b3e9-4bb3913a4431")
public class TeamWithMemberDtoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private TeamBaseDtoService teamBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TeamMemberBaseDtoService teamMemberBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TeamWithMemberDtoDataCollector teamWithMemberDtoDataCollector;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "3f0104c1-31ee-378d-86b0-2038a66e9adf")
    private void fillDataWhenNecessary(TeamWithMemberDtoDataHolder dataHolder) {
        List<TeamBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.teamMemberList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(TeamBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<TeamMemberBaseDto> baseDtoList =
                    teamMemberBaseDtoService.getByTeamIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(TeamMemberBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<TeamMemberBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(TeamMemberBaseDto::getTeamId));
            dataHolder.teamMemberList =
                    rootDtoList.stream()
                            .map(TeamBaseDto::getId)
                            .distinct()
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "599c84cd-21fd-303f-920e-a8f6d499339f")
    public void collectDataDefault(TeamWithMemberDtoDataHolder dataHolder) {
        teamWithMemberDtoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
