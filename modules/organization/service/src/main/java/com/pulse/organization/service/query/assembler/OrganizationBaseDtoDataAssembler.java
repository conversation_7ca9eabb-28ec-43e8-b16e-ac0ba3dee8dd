package com.pulse.organization.service.query.assembler;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

/** OrganizationBaseDto数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "dc35a813-94d2-346b-836e-3429d540e959")
public class OrganizationBaseDtoDataAssembler {

    /** 组装OrganizationBaseDto数据 */
    @AutoGenerated(locked = true, uuid = "079ccfd4-4a5f-3885-9e19-1fdbc2ed6f8a")
    public void assembleData(List<OrganizationBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        assembleDataCustomized(dtoList); // 自定义处理逻辑
    }

    /** 批量自定义组装OrganizationBaseDto数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "c31626c8-814a-3120-9f33-165f66a4112e")
    public void assembleDataCustomized(List<OrganizationBaseDto> dataList) {
        // 自定义数据组装

    }
}
