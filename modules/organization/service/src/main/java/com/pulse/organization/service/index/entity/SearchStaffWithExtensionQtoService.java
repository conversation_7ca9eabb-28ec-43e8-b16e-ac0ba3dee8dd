package com.pulse.organization.service.index.entity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;

import com.pulse.organization.persist.mapper.SearchStaffWithExtensionQtoDao;
import com.pulse.organization.persist.qto.SearchStaffWithExtensionQto;
import com.vs.code.AutoGenerated;
import com.vs.util.JedisUtil;

import org.springframework.stereotype.Component;

import redis.clients.jedis.JedisPool;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = true, uuid = "01a88b47-519a-4264-8199-c4f834121f0a|QTO|SERVICE")
public class SearchStaffWithExtensionQtoService {
    @AutoGenerated(locked = true)
    @Resource
    private JedisPool jedisPool;

    @AutoGenerated(locked = true)
    @Resource
    private SearchStaffWithExtensionQtoDao searchStaffWithExtensionMapper;

    /** 不分页查询入口 */
    @AutoGenerated(locked = true, uuid = "01a88b47-519a-4264-8199-c4f834121f0a-query")
    public List<String> query(SearchStaffWithExtensionQto qto) {
        return searchStaffWithExtensionMapper.query(qto);
    }

    /** 查询瀑布流更多数据 */
    @AutoGenerated(locked = true, uuid = "01a88b47-519a-4264-8199-c4f834121f0a-query-has-more")
    public Boolean hasMore(SearchStaffWithExtensionQto qto) {
        Integer from = qto.getFrom();
        Integer size = qto.getSize();
        String scrollId = qto.getScrollId();
        if (StrUtil.isEmpty(qto.getScrollId())) {
            qto.setScrollId(StrUtil.uuid());
        }
        Boolean cached = JedisUtil.useJedis(jedisPool, jedis -> jedis.exists(scrollId));
        if (cached) {
            Long count = JedisUtil.useJedis(jedisPool, jedis -> jedis.llen(scrollId));
            return count > from + size;
        } else {
            SearchStaffWithExtensionQto newQto =
                    BeanUtil.copyProperties(qto, SearchStaffWithExtensionQto.class);
            newQto.setFrom(0);
            newQto.setSize(5000);
            List<String> idList = searchStaffWithExtensionMapper.queryPaged(newQto);
            if (idList.isEmpty()) {
                return false;
            }
            JedisUtil.useJedis(
                    jedisPool,
                    jedis ->
                            jedis.rpush(
                                    scrollId,
                                    idList.stream().map(String::valueOf).toArray(String[]::new)));
            JedisUtil.useJedis(jedisPool, jedis -> jedis.expire(scrollId, 300));
            return idList.size() > from + size;
        }
    }

    /** 分页查询入口 */
    @AutoGenerated(locked = true, uuid = "01a88b47-519a-4264-8199-c4f834121f0a-query-paged")
    public List<String> queryPaged(SearchStaffWithExtensionQto qto) {
        Assert.isTrue(qto.getSize() != null && qto.getSize() > 0, "size 字段必须大于0 ");
        Assert.isTrue(qto.getFrom() != null && qto.getFrom() >= 0, "from 字段必须大于等于0 ");
        return searchStaffWithExtensionMapper.queryPaged(qto);
    }

    /** 查询瀑布流数据 */
    @AutoGenerated(locked = true, uuid = "01a88b47-519a-4264-8199-c4f834121f0a-query-waterfall")
    public List<String> queryWaterfall(SearchStaffWithExtensionQto qto) {
        Integer from = qto.getFrom();
        Integer size = qto.getSize();
        if (StrUtil.isEmpty(qto.getScrollId())) {
            qto.setScrollId(StrUtil.uuid());
        }
        Assert.isTrue(from != null && from >= 0, "from参数必传，且不能小于0");
        Assert.isTrue(size != null && size > 0, "size参数必传，且必须大于0");
        String scrollId = qto.getScrollId();
        Boolean cached = JedisUtil.useJedis(jedisPool, jedis -> jedis.exists(scrollId));
        if (cached) {
            List<String> idStrList =
                    JedisUtil.useJedis(
                            jedisPool, jedis -> jedis.lrange(scrollId, from, from + size));
            return idStrList.stream().collect(Collectors.toList());
        } else {
            SearchStaffWithExtensionQto newQto =
                    BeanUtil.copyProperties(qto, SearchStaffWithExtensionQto.class);
            newQto.setFrom(0);
            newQto.setSize(5000);
            List<String> idList = searchStaffWithExtensionMapper.queryPaged(newQto);
            if (idList.isEmpty()) {
                return List.of();
            }
            JedisUtil.useJedis(
                    jedisPool,
                    jedis ->
                            jedis.rpush(
                                    scrollId,
                                    idList.stream().map(String::valueOf).toArray(String[]::new)));
            JedisUtil.useJedis(jedisPool, jedis -> jedis.expire(scrollId, 300));
            return ListUtil.sub(idList, from, from + size);
        }
    }

    /** 计算总数量 */
    @AutoGenerated(locked = true)
    public Integer count(SearchStaffWithExtensionQto qto) {
        return searchStaffWithExtensionMapper.count(qto);
    }
}
