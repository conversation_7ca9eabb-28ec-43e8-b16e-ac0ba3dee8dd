package com.pulse.organization.service.query.assembler;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.StaffBaseDto;
import com.pulse.organization.manager.dto.StaffExtensionBaseDto;
import com.pulse.organization.manager.dto.StaffWithExtensionDto;
import com.pulse.organization.service.StaffBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** StaffWithExtensionDto数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "f8f4078e-1b75-3b21-b972-b6ec58f6cf2e")
public class StaffWithExtensionDtoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private StaffBaseDtoService staffBaseDtoService;

    /** 批量自定义组装StaffWithExtensionDto数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "b76da9ce-6d45-3de6-a58c-dd43de04ba45")
    public void assembleDataCustomized(List<StaffWithExtensionDto> dataList) {
        // 自定义数据组装

    }

    /** 组装StaffWithExtensionDto数据 */
    @AutoGenerated(locked = true, uuid = "f9e42f1d-e734-3b8c-8e31-a03c076fcd6a")
    public void assembleData(
            List<StaffWithExtensionDto> dtoList,
            StaffWithExtensionDtoDataAssembler.StaffWithExtensionDtoDataHolder dataHolder) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        Map<String, StaffBaseDto> baseDtoMap =
                dataHolder.getRootBaseDtoList().stream()
                        .collect(Collectors.toMap(StaffBaseDto::getId, Function.identity()));

        Map<String, StaffExtensionBaseDto> staffExtension =
                dataHolder.staffExtension.stream()
                        .collect(
                                Collectors.toMap(
                                        StaffExtensionBaseDto::getStaffId, Function.identity()));
        Map<String, OrganizationBaseDto> organization =
                dataHolder.organization.stream()
                        .collect(Collectors.toMap(OrganizationBaseDto::getId, Function.identity()));
        Map<String, OrganizationBaseDto> accountingOrganization =
                dataHolder.accountingOrganization.stream()
                        .collect(Collectors.toMap(OrganizationBaseDto::getId, Function.identity()));
        Map<String, OrganizationBaseDto> hrOrganization =
                dataHolder.hrOrganization.stream()
                        .collect(Collectors.toMap(OrganizationBaseDto::getId, Function.identity()));

        for (StaffWithExtensionDto dto : dtoList) {
            dto.setStaffExtension(
                    Optional.ofNullable(staffExtension.get(dto.getId())).orElse(null));
            dto.setOrganization(
                    Optional.ofNullable(
                                    organization.get(
                                            baseDtoMap.get(dto.getId()).getOrganizationId()))
                            .orElse(null));
            dto.setAccountingOrganization(
                    Optional.ofNullable(
                                    accountingOrganization.get(
                                            baseDtoMap
                                                    .get(dto.getId())
                                                    .getAccountingOrganizationId()))
                            .orElse(null));
            dto.setHrOrganization(
                    Optional.ofNullable(
                                    hrOrganization.get(
                                            baseDtoMap.get(dto.getId()).getHrOrganizationId()))
                            .orElse(null));
        }

        assembleDataCustomized(dtoList); // 自定义处理逻辑
    }

    /** 持有Dto需要的全部数据 */
    @Getter
    @Setter
    public static class StaffWithExtensionDtoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<StaffBaseDto> rootBaseDtoList;

        /** 持有dto字段staffExtension的Dto数据 */
        @AutoGenerated(locked = true)
        public List<StaffExtensionBaseDto> staffExtension;

        /** 持有dto字段organization的Dto数据 */
        @AutoGenerated(locked = true)
        public List<OrganizationBaseDto> organization;

        /** 持有dto字段accountingOrganization的Dto数据 */
        @AutoGenerated(locked = true)
        public List<OrganizationBaseDto> accountingOrganization;

        /** 持有dto字段hrOrganization的Dto数据 */
        @AutoGenerated(locked = true)
        public List<OrganizationBaseDto> hrOrganization;
    }
}
