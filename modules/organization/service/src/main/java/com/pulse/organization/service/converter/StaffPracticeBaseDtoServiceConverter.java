package com.pulse.organization.service.converter;

import com.pulse.organization.manager.dto.StaffPracticeBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "4c97a2d0-14f6-349d-9de6-f1bf9f2ab4aa")
public class StaffPracticeBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<StaffPracticeBaseDto> StaffPracticeBaseDtoConverter(
            List<StaffPracticeBaseDto> staffPracticeBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return staffPracticeBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
