package com.pulse.organization.service.query.collector;

import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.WardBaseDto;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.pulse.organization.service.WardBaseDtoService;
import com.pulse.organization.service.query.assembler.OrganizationWardWithDepartmentListDtoDataAssembler.OrganizationWardWithDepartmentListDtoDataHolder;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装OrganizationWardWithDepartmentListDto所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "af1f50a6-87e5-3f30-86ff-fb8178bcca71")
public class OrganizationWardWithDepartmentListDtoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationWardWithDepartmentListDtoDataCollector
            organizationWardWithDepartmentListDtoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private WardBaseDtoService wardBaseDtoService;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "5a1863b6-217e-3af3-8c26-bb32a8ed6fa2")
    private void fillDataWhenNecessary(OrganizationWardWithDepartmentListDtoDataHolder dataHolder) {
        List<OrganizationBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.ward == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(OrganizationBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<WardBaseDto> baseDtoList =
                    wardBaseDtoService.getByOrganizationIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(WardBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<WardBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(WardBaseDto::getOrganizationId));
            dataHolder.ward =
                    rootDtoList.stream()
                            .map(OrganizationBaseDto::getId)
                            .distinct()
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "f7da11f2-f44a-32e1-a3aa-eb215712c173")
    public void collectDataDefault(OrganizationWardWithDepartmentListDtoDataHolder dataHolder) {
        organizationWardWithDepartmentListDtoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
