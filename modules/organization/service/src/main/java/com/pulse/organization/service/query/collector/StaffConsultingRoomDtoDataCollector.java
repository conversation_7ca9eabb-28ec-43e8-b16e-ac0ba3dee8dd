package com.pulse.organization.service.query.collector;

import com.pulse.consulting_room.manager.dto.ConsultingRoomBaseDto;
import com.pulse.organization.manager.dto.StaffBaseDto;
import com.pulse.organization.manager.dto.StaffConsultingRoomBaseDto;
import com.pulse.organization.manager.facade.consulting_room.ConsultingRoomBaseDtoServiceInOrganizationRpcAdapter;
import com.pulse.organization.service.StaffBaseDtoService;
import com.pulse.organization.service.StaffConsultingRoomBaseDtoService;
import com.pulse.organization.service.query.assembler.StaffConsultingRoomDtoDataAssembler.StaffConsultingRoomDtoDataHolder;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装StaffConsultingRoomDto所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "418f7c92-f841-3b7f-b336-f0c1d3d75065")
public class StaffConsultingRoomDtoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private ConsultingRoomBaseDtoServiceInOrganizationRpcAdapter
            consultingRoomBaseDtoServiceInOrganizationRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffBaseDtoService staffBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffConsultingRoomBaseDtoService staffConsultingRoomBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffConsultingRoomDtoDataCollector staffConsultingRoomDtoDataCollector;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "414b23c3-a9ca-3825-ae65-a2669e97d08a")
    private void fillDataWhenNecessary(StaffConsultingRoomDtoDataHolder dataHolder) {
        List<StaffConsultingRoomBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.consultingRoom == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(StaffConsultingRoomBaseDto::getConsultingRoomId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ConsultingRoomBaseDto> baseDtoList =
                    consultingRoomBaseDtoServiceInOrganizationRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(ConsultingRoomBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, ConsultingRoomBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            ConsultingRoomBaseDto::getId, Function.identity()));
            dataHolder.consultingRoom =
                    rootDtoList.stream()
                            .map(StaffConsultingRoomBaseDto::getConsultingRoomId)
                            .distinct()
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
        if (dataHolder.staff == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(StaffConsultingRoomBaseDto::getStaffId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<StaffBaseDto> baseDtoList =
                    staffBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(StaffBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, StaffBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.toMap(StaffBaseDto::getId, Function.identity()));
            dataHolder.staff =
                    rootDtoList.stream()
                            .map(StaffConsultingRoomBaseDto::getStaffId)
                            .distinct()
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "84c16340-3c02-353b-841b-9d6480cb432d")
    public void collectDataDefault(StaffConsultingRoomDtoDataHolder dataHolder) {
        staffConsultingRoomDtoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
