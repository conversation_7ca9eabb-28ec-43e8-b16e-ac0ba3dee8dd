package com.pulse.organization.service.query.collector;

import com.pulse.application.manager.dto.ApplicationBaseDto;
import com.pulse.organization.manager.converter.AssetOrganizationDtoConverter;
import com.pulse.organization.manager.dto.AssetBaseDto;
import com.pulse.organization.manager.dto.AssetOrganizationBaseDto;
import com.pulse.organization.manager.dto.AssetOrganizationDto;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.facade.application.ApplicationBaseDtoServiceInOrganizationRpcAdapter;
import com.pulse.organization.service.AssetBaseDtoService;
import com.pulse.organization.service.AssetOrganizationBaseDtoService;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.pulse.organization.service.query.assembler.AssetDetailDtoDataAssembler.AssetDetailDtoDataHolder;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装AssetDetailDto所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "98450661-b78c-3c98-a688-eac29a6c026e")
public class AssetDetailDtoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private ApplicationBaseDtoServiceInOrganizationRpcAdapter
            applicationBaseDtoServiceInOrganizationRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private AssetBaseDtoService assetBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private AssetDetailDtoDataCollector assetDetailDtoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private AssetOrganizationBaseDtoService assetOrganizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private AssetOrganizationDtoConverter assetOrganizationDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "40146933-be09-3fe3-8389-3e591261d968")
    public void collectDataDefault(AssetDetailDtoDataHolder dataHolder) {
        assetDetailDtoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "75ebb432-f886-337d-84cc-82e9efedd3f2")
    private void fillDataWhenNecessary(AssetDetailDtoDataHolder dataHolder) {
        List<AssetBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.organization == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(AssetBaseDto::getOrganizationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            dataHolder.organization =
                    rootDtoList.stream()
                            .map(AssetBaseDto::getOrganizationId)
                            .distinct()
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
        if (dataHolder.assetOrganizationList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(AssetBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<AssetOrganizationBaseDto> baseDtoList =
                    assetOrganizationBaseDtoService.getByAssetIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(AssetOrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<AssetOrganizationBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(AssetOrganizationBaseDto::getAssetId));
            Map<String, AssetOrganizationDto> assetOrganizationDtoMap =
                    assetOrganizationDtoConverter
                            .convertFromAssetOrganizationBaseDtoToAssetOrganizationDto(baseDtoList)
                            .stream()
                            .collect(
                                    Collectors.toMap(
                                            AssetOrganizationDto::getId, Function.identity()));
            dataHolder.assetOrganizationList =
                    rootDtoList.stream()
                            .map(AssetBaseDto::getId)
                            .distinct()
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> assetOrganizationDtoMap.get(baseDto.getId()),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.assetOrganizationList2Application == null) {
            Set<String> ids =
                    dataHolder.assetOrganizationList.keySet().stream()
                            .map(AssetOrganizationBaseDto::getApplicationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ApplicationBaseDto> baseDtoList =
                    applicationBaseDtoServiceInOrganizationRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(ApplicationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, ApplicationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            ApplicationBaseDto::getId, Function.identity()));
            dataHolder.assetOrganizationList2Application =
                    dataHolder.assetOrganizationList.keySet().stream()
                            .map(AssetOrganizationBaseDto::getApplicationId)
                            .distinct()
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
        if (dataHolder.assetOrganizationList2Organization == null) {
            Set<String> ids =
                    dataHolder.assetOrganizationList.keySet().stream()
                            .map(AssetOrganizationBaseDto::getOrganizationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            dataHolder.assetOrganizationList2Organization =
                    dataHolder.assetOrganizationList.keySet().stream()
                            .map(AssetOrganizationBaseDto::getOrganizationId)
                            .distinct()
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
    }
}
