package com.pulse.organization.service.query;

import com.pulse.organization.manager.converter.OrganizationWardDtoConverter;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.OrganizationWardDto;
import com.pulse.organization.persist.qto.SearchOrganizationWardQto;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.pulse.organization.service.index.entity.SearchOrganizationWardQtoService;
import com.pulse.organization.service.query.assembler.OrganizationWardDtoDataAssembler;
import com.pulse.organization.service.query.assembler.OrganizationWardDtoDataAssembler.OrganizationWardDtoDataHolder;
import com.pulse.organization.service.query.collector.OrganizationWardDtoDataCollector;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** OrganizationWardDto查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "8728cc31-a05f-33d8-b6c5-74c080eabe5a")
public class OrganizationWardDtoQueryService {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationWardDtoConverter organizationWardDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationWardDtoDataAssembler organizationWardDtoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationWardDtoDataCollector organizationWardDtoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private SearchOrganizationWardQtoService searchOrganizationWardQtoService;

    /** 根据SearchOrganizationWardQto查询OrganizationWardDto列表,上限500 */
    @PublicInterface(id = "2d6e6a5a-5968-427b-9b4f-7711cc60956f", module = "organization")
    @AutoGenerated(locked = false, uuid = "1070dc5c-d646-3e7b-8f4c-07d85a06f2cc")
    public List<OrganizationWardDto> searchOrganizationWard(
            @Valid @NotNull SearchOrganizationWardQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchOrganizationWardQtoService.query(qto);
        OrganizationWardDtoDataHolder dataHolder = new OrganizationWardDtoDataHolder();
        List<OrganizationWardDto> result = toDtoList(ids, dataHolder);
        organizationWardDtoDataCollector.collectDataDefault(dataHolder);
        organizationWardDtoDataAssembler.assembleData(result, dataHolder);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 根据SearchOrganizationWardQto查询OrganizationWardDto列表,分页 */
    @PublicInterface(id = "86be96fc-93bf-4911-a597-ebf92547de8a", module = "organization")
    @AutoGenerated(locked = false, uuid = "50b4b61f-1382-338c-9603-2240716c4ba6")
    public VSQueryResult<OrganizationWardDto> searchOrganizationWardPaged(
            @Valid @NotNull SearchOrganizationWardQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchOrganizationWardQtoService.queryPaged(qto);
        OrganizationWardDtoDataHolder dataHolder = new OrganizationWardDtoDataHolder();
        List<OrganizationWardDto> dtoList = toDtoList(ids, dataHolder);
        organizationWardDtoDataCollector.collectDataDefault(dataHolder);
        organizationWardDtoDataAssembler.assembleData(dtoList, dataHolder);
        VSQueryResult result = new VSQueryResult();
        result.setCount(searchOrganizationWardQtoService.count(qto));
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 根据SearchOrganizationWardQto查询OrganizationWardDto列表,瀑布流 */
    @PublicInterface(id = "cc90bdd9-608b-4002-bcdb-f09df8df14bc", module = "organization")
    @AutoGenerated(locked = false, uuid = "6cf964a1-6989-3522-ad90-3483abb63b93")
    public VSQueryResult<OrganizationWardDto> searchOrganizationWardWaterfall(
            @Valid @NotNull SearchOrganizationWardQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchOrganizationWardQtoService.queryWaterfall(qto);
        OrganizationWardDtoDataHolder dataHolder = new OrganizationWardDtoDataHolder();
        List<OrganizationWardDto> dtoList = toDtoList(ids, dataHolder);
        organizationWardDtoDataCollector.collectDataDefault(dataHolder);
        organizationWardDtoDataAssembler.assembleData(dtoList, dataHolder);
        VSQueryResult result = new VSQueryResult();
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        result.setHasMore(searchOrganizationWardQtoService.hasMore(qto));
        result.setScrollId(qto.getScrollId());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 根据SearchOrganizationWardQto查询数量 */
    @PublicInterface(id = "db02fcf1-64ed-4e38-825d-f25353530756", module = "organization")
    @AutoGenerated(locked = false, uuid = "993717f2-8e36-3777-bb64-26e1bcf2400e")
    public Integer searchOrganizationWardCount(@Valid @NotNull SearchOrganizationWardQto qto) {
        return searchOrganizationWardQtoService.count(qto);
    }

    /** 将ID列表转换为DtoList */
    @AutoGenerated(locked = true, uuid = "afcfb9e1-f8eb-38be-9f39-46bfc75a738f")
    private List<OrganizationWardDto> toDtoList(
            List<String> ids, OrganizationWardDtoDataHolder dataHolder) {
        List<OrganizationBaseDto> baseDtoList = organizationBaseDtoService.getByIds(ids);
        dataHolder.setRootBaseDtoList(baseDtoList);
        Map<String, OrganizationWardDto> dtoMap =
                organizationWardDtoConverter
                        .convertFromOrganizationBaseDtoToOrganizationWardDto(baseDtoList)
                        .stream()
                        .collect(Collectors.toMap(OrganizationWardDto::getId, Function.identity()));
        return ids.stream()
                .map(id -> dtoMap.get(id))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
