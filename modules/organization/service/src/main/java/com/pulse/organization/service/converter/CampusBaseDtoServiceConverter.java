package com.pulse.organization.service.converter;

import com.pulse.organization.manager.dto.CampusBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "a248ecd9-2afa-33d5-a56f-0abde7d8712f")
public class CampusBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<CampusBaseDto> CampusBaseDtoConverter(List<CampusBaseDto> campusBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return campusBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
