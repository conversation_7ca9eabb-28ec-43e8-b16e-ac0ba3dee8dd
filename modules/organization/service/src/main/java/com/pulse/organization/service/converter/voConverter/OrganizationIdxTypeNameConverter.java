package com.pulse.organization.service.converter.voConverter;

import com.pulse.organization.persist.dos.Organization;
import com.pulse.organization.persist.dos.Organization.NameAndType;
import com.pulse.organization.persist.eo.IdxTypeNameEo;
import com.vs.code.AutoGenerated;

@AutoGenerated(locked = true, uuid = "c4a09e80-71bb-3495-8574-38cdccc5729b")
public class OrganizationIdxTypeNameConverter {

    @AutoGenerated(locked = true)
    public static Organization.NameAndType convertFromIdxTypeNameToInner(
            IdxTypeNameEo idxTypeName) {
        if (null == idxTypeName) {
            return null;
        }

        NameAndType nameAndType = new NameAndType();
        nameAndType.setType(idxTypeName.getType());
        nameAndType.setName(idxTypeName.getName());
        return nameAndType;
    }
}
