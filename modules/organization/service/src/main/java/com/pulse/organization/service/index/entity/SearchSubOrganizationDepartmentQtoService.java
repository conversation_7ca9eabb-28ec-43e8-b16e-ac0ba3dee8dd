package com.pulse.organization.service.index.entity;

import com.pulse.organization.persist.mapper.SearchSubOrganizationDepartmentQtoDao;
import com.pulse.organization.persist.qto.SearchSubOrganizationDepartmentQto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = true, uuid = "*************-4600-ba42-bb8914abd93e|QTO|SERVICE")
public class SearchSubOrganizationDepartmentQtoService {
    @AutoGenerated(locked = true)
    @Resource
    private SearchSubOrganizationDepartmentQtoDao searchSubOrganizationDepartmentMapper;

    /** 不分页查询入口 */
    @AutoGenerated(locked = true, uuid = "*************-4600-ba42-bb8914abd93e-query")
    public List<String> query(SearchSubOrganizationDepartmentQto qto) {
        return searchSubOrganizationDepartmentMapper.query(qto);
    }

    /** 计算总数量 */
    @AutoGenerated(locked = true)
    public Integer count(SearchSubOrganizationDepartmentQto qto) {
        return searchSubOrganizationDepartmentMapper.count(qto);
    }
}
