package com.pulse.organization.service.converter;

import com.pulse.organization.manager.dto.StaffUserBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "0239544b-fe69-37df-afc3-423ea2447eb1")
public class StaffUserBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<StaffUserBaseDto> StaffUserBaseDtoConverter(
            List<StaffUserBaseDto> staffUserBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return staffUserBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
