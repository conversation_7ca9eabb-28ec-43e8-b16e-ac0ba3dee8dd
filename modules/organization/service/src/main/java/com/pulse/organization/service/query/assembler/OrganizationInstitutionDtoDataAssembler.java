package com.pulse.organization.service.query.assembler;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.manager.dto.InstitutionBaseDto;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.OrganizationInstitutionDto;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** OrganizationInstitutionDto数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "543e14f5-74b1-34c0-b552-c0a0af04104f")
public class OrganizationInstitutionDtoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    /** 批量自定义组装OrganizationInstitutionDto数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "b6a0eaa1-9784-39cd-9b3d-ffc3d37f3e27")
    public void assembleDataCustomized(List<OrganizationInstitutionDto> dataList) {
        // 自定义数据组装

    }

    /** 组装OrganizationInstitutionDto数据 */
    @AutoGenerated(locked = true, uuid = "fc4e561e-bdbe-3386-9af3-22c02d3b8ada")
    public void assembleData(
            List<OrganizationInstitutionDto> dtoList,
            OrganizationInstitutionDtoDataAssembler.OrganizationInstitutionDtoDataHolder
                    dataHolder) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        Map<String, OrganizationBaseDto> baseDtoMap =
                dataHolder.getRootBaseDtoList().stream()
                        .collect(Collectors.toMap(OrganizationBaseDto::getId, Function.identity()));

        Map<String, InstitutionBaseDto> institution =
                dataHolder.institution.stream()
                        .collect(
                                Collectors.toMap(
                                        InstitutionBaseDto::getOrganizationId,
                                        Function.identity()));

        for (OrganizationInstitutionDto dto : dtoList) {
            dto.setInstitution(Optional.ofNullable(institution.get(dto.getId())).orElse(null));
        }

        assembleDataCustomized(dtoList); // 自定义处理逻辑
    }

    /** 持有Dto需要的全部数据 */
    @Getter
    @Setter
    public static class OrganizationInstitutionDtoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<OrganizationBaseDto> rootBaseDtoList;

        /** 持有dto字段institution的Dto数据 */
        @AutoGenerated(locked = true)
        public List<InstitutionBaseDto> institution;
    }
}
