package com.pulse.organization.service.query.collector;

import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.StaffBaseDto;
import com.pulse.organization.manager.dto.StaffExtensionBaseDto;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.pulse.organization.service.StaffBaseDtoService;
import com.pulse.organization.service.StaffExtensionBaseDtoService;
import com.pulse.organization.service.query.assembler.StaffWithExtensionDtoDataAssembler.StaffWithExtensionDtoDataHolder;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装StaffWithExtensionDto所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "54080a7c-4a5f-3720-b142-47c3c8e001b7")
public class StaffWithExtensionDtoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffBaseDtoService staffBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffExtensionBaseDtoService staffExtensionBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffWithExtensionDtoDataCollector staffWithExtensionDtoDataCollector;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "3a3ce21c-1132-3c54-a2d4-c424e7ebb690")
    private void fillDataWhenNecessary(StaffWithExtensionDtoDataHolder dataHolder) {
        List<StaffBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.staffExtension == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<StaffExtensionBaseDto> baseDtoList =
                    staffExtensionBaseDtoService.getByStaffIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(StaffExtensionBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<StaffExtensionBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(StaffExtensionBaseDto::getStaffId));
            dataHolder.staffExtension =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getId)
                            .distinct()
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
        if (dataHolder.organization == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getOrganizationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            dataHolder.organization =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getOrganizationId)
                            .distinct()
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
        if (dataHolder.accountingOrganization == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getAccountingOrganizationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            dataHolder.accountingOrganization =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getAccountingOrganizationId)
                            .distinct()
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
        if (dataHolder.hrOrganization == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getHrOrganizationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            dataHolder.hrOrganization =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getHrOrganizationId)
                            .distinct()
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "7fbdc30f-a204-3a55-aaec-f692a2a0e48d")
    public void collectDataDefault(StaffWithExtensionDtoDataHolder dataHolder) {
        staffWithExtensionDtoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
