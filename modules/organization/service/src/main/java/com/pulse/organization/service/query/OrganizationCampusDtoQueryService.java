package com.pulse.organization.service.query;

import com.pulse.organization.manager.converter.OrganizationCampusDtoConverter;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.OrganizationCampusDto;
import com.pulse.organization.persist.qto.SearchOrganizationCampusQto;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.pulse.organization.service.index.entity.SearchOrganizationCampusQtoService;
import com.pulse.organization.service.query.assembler.OrganizationCampusDtoDataAssembler;
import com.pulse.organization.service.query.assembler.OrganizationCampusDtoDataAssembler.OrganizationCampusDtoDataHolder;
import com.pulse.organization.service.query.collector.OrganizationCampusDtoDataCollector;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** OrganizationCampusDto查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "ebc7ccf9-f669-3b8a-a16a-8436f6815907")
public class OrganizationCampusDtoQueryService {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationCampusDtoConverter organizationCampusDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationCampusDtoDataAssembler organizationCampusDtoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationCampusDtoDataCollector organizationCampusDtoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private SearchOrganizationCampusQtoService searchOrganizationCampusQtoService;

    /** 根据SearchOrganizationCampusQto查询OrganizationCampusDto列表,瀑布流 */
    @PublicInterface(
            id = "bddb9773-997e-4bec-bca9-d70db8419bc5",
            module = "organization",
            moduleId = "a3b95408-2257-4bfa-aafe-59cc5547e63c",
            pubRpc = true,
            version = "1747363849315")
    @AutoGenerated(locked = false, uuid = "0b10ba03-bf30-330a-9d5e-01f569256ed4")
    public VSQueryResult<OrganizationCampusDto> searchOrganizationCampusWaterfall(
            @Valid @NotNull SearchOrganizationCampusQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchOrganizationCampusQtoService.queryWaterfall(qto);
        OrganizationCampusDtoDataHolder dataHolder = new OrganizationCampusDtoDataHolder();
        List<OrganizationCampusDto> dtoList = toDtoList(ids, dataHolder);
        organizationCampusDtoDataCollector.collectDataDefault(dataHolder);
        organizationCampusDtoDataAssembler.assembleData(dtoList, dataHolder);
        VSQueryResult result = new VSQueryResult();
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        result.setHasMore(searchOrganizationCampusQtoService.hasMore(qto));
        result.setScrollId(qto.getScrollId());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 将ID列表转换为DtoList */
    @AutoGenerated(locked = true, uuid = "b1051365-da7e-3146-bd82-177fd74b6360")
    private List<OrganizationCampusDto> toDtoList(
            List<String> ids, OrganizationCampusDtoDataHolder dataHolder) {
        List<OrganizationBaseDto> baseDtoList = organizationBaseDtoService.getByIds(ids);
        dataHolder.setRootBaseDtoList(baseDtoList);
        Map<String, OrganizationCampusDto> dtoMap =
                organizationCampusDtoConverter
                        .convertFromOrganizationBaseDtoToOrganizationCampusDto(baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        OrganizationCampusDto::getId, Function.identity()));
        return ids.stream()
                .map(id -> dtoMap.get(id))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 根据SearchOrganizationCampusQto查询OrganizationCampusDto列表,分页 */
    @PublicInterface(
            id = "ad1f4247-75bf-4f5c-84e8-31de2161bd21",
            module = "organization",
            moduleId = "a3b95408-2257-4bfa-aafe-59cc5547e63c",
            pubRpc = true,
            version = "1747363849308")
    @AutoGenerated(locked = false, uuid = "bc83de9a-76af-304c-bd31-62bfa82b596a")
    public VSQueryResult<OrganizationCampusDto> searchOrganizationCampusPaged(
            @Valid @NotNull SearchOrganizationCampusQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchOrganizationCampusQtoService.queryPaged(qto);
        OrganizationCampusDtoDataHolder dataHolder = new OrganizationCampusDtoDataHolder();
        List<OrganizationCampusDto> dtoList = toDtoList(ids, dataHolder);
        organizationCampusDtoDataCollector.collectDataDefault(dataHolder);
        organizationCampusDtoDataAssembler.assembleData(dtoList, dataHolder);
        VSQueryResult result = new VSQueryResult();
        result.setCount(searchOrganizationCampusQtoService.count(qto));
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 根据SearchOrganizationCampusQto查询数量 */
    @PublicInterface(
            id = "5b66f990-169a-4bd1-bc02-4f1081ddc829",
            module = "organization",
            moduleId = "a3b95408-2257-4bfa-aafe-59cc5547e63c",
            pubRpc = true,
            version = "1747363849318")
    @AutoGenerated(locked = false, uuid = "d6d5cffc-7004-3c2c-8808-5afcb09a47a2")
    public Integer searchOrganizationCampusCount(@Valid @NotNull SearchOrganizationCampusQto qto) {
        return searchOrganizationCampusQtoService.count(qto);
    }

    /** 根据SearchOrganizationCampusQto查询OrganizationCampusDto列表,上限500 */
    @PublicInterface(
            id = "2db7381e-0d49-4a88-8ce1-e4f10f9e5503",
            module = "organization",
            moduleId = "a3b95408-2257-4bfa-aafe-59cc5547e63c",
            pubRpc = true,
            version = "1747363849302")
    @AutoGenerated(locked = false, uuid = "dee5c949-3c9f-380f-b626-68251bc71bde")
    public List<OrganizationCampusDto> searchOrganizationCampus(
            @Valid @NotNull SearchOrganizationCampusQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchOrganizationCampusQtoService.query(qto);
        OrganizationCampusDtoDataHolder dataHolder = new OrganizationCampusDtoDataHolder();
        List<OrganizationCampusDto> result = toDtoList(ids, dataHolder);
        organizationCampusDtoDataCollector.collectDataDefault(dataHolder);
        organizationCampusDtoDataAssembler.assembleData(result, dataHolder);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
