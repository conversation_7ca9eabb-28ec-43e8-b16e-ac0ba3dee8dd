package com.pulse.organization.service.query.collector;

import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.WardBaseDto;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.pulse.organization.service.WardBaseDtoService;
import com.pulse.organization.service.query.assembler.OrganizationWardDtoDataAssembler.OrganizationWardDtoDataHolder;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装OrganizationWardDto所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "91ac27c7-8217-3b4b-b324-e9703bf04bde")
public class OrganizationWardDtoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationWardDtoDataCollector organizationWardDtoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private WardBaseDtoService wardBaseDtoService;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "49367104-6ff0-34be-83d1-fa6ed5012a53")
    private void fillDataWhenNecessary(OrganizationWardDtoDataHolder dataHolder) {
        List<OrganizationBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.ward == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(OrganizationBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<WardBaseDto> baseDtoList =
                    wardBaseDtoService.getByOrganizationIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(WardBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<WardBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(WardBaseDto::getOrganizationId));
            dataHolder.ward =
                    rootDtoList.stream()
                            .map(OrganizationBaseDto::getId)
                            .distinct()
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "6b60ae21-f254-3ccf-82dd-8381cee475b1")
    public void collectDataDefault(OrganizationWardDtoDataHolder dataHolder) {
        organizationWardDtoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
