package com.pulse.organization.service.bto;

import com.pulse.organization.common.enums.AssetStatusEnum;
import com.pulse.organization.common.enums.AssetTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> Asset
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "d5a11d05-f1a0-48d3-b6c7-10eadd13d7db|BTO|DEFINITION")
public class UpdateAssetBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    @Valid
    @AutoGenerated(locked = true, uuid = "bc785aca-b893-4012-91f9-6cf59580ced1")
    private List<UpdateAssetBto.AssetOrganizationBto> assetOrganizationBtoList;

    /** 资产类型 */
    @AutoGenerated(locked = true, uuid = "4e67a8fb-deb5-48a4-9cd6-c098a6cb4ed1")
    private AssetTypeEnum assetType;

    /** 创建者ID */
    @AutoGenerated(locked = true, uuid = "44e6fb2d-8cc4-4955-80ff-7f453dd3e0fc")
    private String createdBy;

    /** 删除者ID */
    @AutoGenerated(locked = true, uuid = "f62d3c00-f881-450e-8d45-390d763ad1cc")
    private String deletedBy;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "c044bae0-7e16-489c-b245-89a6fb3df7d3")
    private String id;

    /** 标签列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "91c23422-1308-42a3-81c5-ed652e85ab4c")
    private List<String> labelList;

    /** 名称 */
    @AutoGenerated(locked = true, uuid = "3380b66c-823c-41e9-b1b9-ec6716902ca9")
    private String name;

    /** 所属组织ID */
    @AutoGenerated(locked = true, uuid = "40cb1e7a-29c6-4b3d-89f7-9dbdba2ee36a")
    private String organizationId;

    /** 采购时间 */
    @AutoGenerated(locked = true, uuid = "25ebc99f-e05f-4ccd-a635-f4a7f610a5b2")
    private Date purchaseTime;

    /** 备注 */
    @AutoGenerated(locked = true, uuid = "ac7d3b03-87e8-4b67-a76b-57c9339ad25c")
    private String remark;

    /** 规格 */
    @AutoGenerated(locked = true, uuid = "1cf14c77-c5be-42a0-b9cd-b062dc206e81")
    private String specification;

    /** 状态 */
    @AutoGenerated(locked = true, uuid = "8f6eff9b-7825-4680-acb5-826d54ede29a")
    private AssetStatusEnum status;

    /** 唯一资产编号 */
    @AutoGenerated(locked = true, uuid = "9caab563-86e4-49f0-aa93-7f8bb6f6b157")
    private String uniqueAssetNumber;

    /** 更新者ID */
    @AutoGenerated(locked = true, uuid = "5cc1c5fd-8cd4-403a-8c56-f3e564e2b310")
    private String updatedBy;

    @AutoGenerated(locked = true)
    public void setAssetOrganizationBtoList(
            List<UpdateAssetBto.AssetOrganizationBto> assetOrganizationBtoList) {
        this.__$validPropertySet.add("assetOrganizationBtoList");
        this.assetOrganizationBtoList = assetOrganizationBtoList;
    }

    @AutoGenerated(locked = true)
    public void setAssetType(AssetTypeEnum assetType) {
        this.__$validPropertySet.add("assetType");
        this.assetType = assetType;
    }

    @AutoGenerated(locked = true)
    public void setCreatedBy(String createdBy) {
        this.__$validPropertySet.add("createdBy");
        this.createdBy = createdBy;
    }

    @AutoGenerated(locked = true)
    public void setDeletedBy(String deletedBy) {
        this.__$validPropertySet.add("deletedBy");
        this.deletedBy = deletedBy;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setLabelList(List<String> labelList) {
        this.__$validPropertySet.add("labelList");
        this.labelList = labelList;
    }

    @AutoGenerated(locked = true)
    public void setName(String name) {
        this.__$validPropertySet.add("name");
        this.name = name;
    }

    @AutoGenerated(locked = true)
    public void setOrganizationId(String organizationId) {
        this.__$validPropertySet.add("organizationId");
        this.organizationId = organizationId;
    }

    @AutoGenerated(locked = true)
    public void setPurchaseTime(Date purchaseTime) {
        this.__$validPropertySet.add("purchaseTime");
        this.purchaseTime = purchaseTime;
    }

    @AutoGenerated(locked = true)
    public void setRemark(String remark) {
        this.__$validPropertySet.add("remark");
        this.remark = remark;
    }

    @AutoGenerated(locked = true)
    public void setSpecification(String specification) {
        this.__$validPropertySet.add("specification");
        this.specification = specification;
    }

    @AutoGenerated(locked = true)
    public void setStatus(AssetStatusEnum status) {
        this.__$validPropertySet.add("status");
        this.status = status;
    }

    @AutoGenerated(locked = true)
    public void setUniqueAssetNumber(String uniqueAssetNumber) {
        this.__$validPropertySet.add("uniqueAssetNumber");
        this.uniqueAssetNumber = uniqueAssetNumber;
    }

    @AutoGenerated(locked = true)
    public void setUpdatedBy(String updatedBy) {
        this.__$validPropertySet.add("updatedBy");
        this.updatedBy = updatedBy;
    }

    /**
     * <b>[源自]</b> AssetOrganization
     *
     * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE | ON_MISS_DELETE_ALL
     */
    @Getter
    @NoArgsConstructor
    public static class AssetOrganizationBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "9c5e9f21-f8b3-473b-bf88-980348a94767")
        private String id;

        /** 组织ID */
        @AutoGenerated(locked = true, uuid = "f9fbbded-9b14-4fd3-845e-f4602dba982b")
        private String organizationId;

        /** 应用ID */
        @AutoGenerated(locked = true, uuid = "7e724281-8071-4abb-bdfc-bffec79d6961")
        private String applicationId;

        /** 创建者ID */
        @AutoGenerated(locked = true, uuid = "3848f9ab-aae0-42e2-a2a9-c0c4087574f9")
        private String createdBy;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setOrganizationId(String organizationId) {
            this.__$validPropertySet.add("organizationId");
            this.organizationId = organizationId;
        }

        @AutoGenerated(locked = true)
        public void setApplicationId(String applicationId) {
            this.__$validPropertySet.add("applicationId");
            this.applicationId = applicationId;
        }

        @AutoGenerated(locked = true)
        public void setCreatedBy(String createdBy) {
            this.__$validPropertySet.add("createdBy");
            this.createdBy = createdBy;
        }
    }
}
