package com.pulse.organization.service.query.assembler;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.StaffBaseDto;
import com.pulse.organization.manager.dto.StaffExtensionBaseDto;
import com.pulse.organization.manager.dto.StaffWithExtensionDto;
import com.pulse.organization.manager.dto.TeamBaseDto;
import com.pulse.organization.manager.dto.TeamDetailDto;
import com.pulse.organization.manager.dto.TeamMemberBaseDto;
import com.pulse.organization.manager.dto.TeamMemberWithStaffDto;
import com.pulse.organization.manager.dto.TeamOrganizationBaseDto;
import com.pulse.organization.manager.dto.TeamOrganizationDto;
import com.pulse.organization.service.TeamBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** TeamDetailDto数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "87c9347a-78c5-3408-ac91-f22b7be5b302")
public class TeamDetailDtoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private TeamBaseDtoService teamBaseDtoService;

    /** 组装TeamDetailDto数据 */
    @AutoGenerated(locked = true, uuid = "1ccc018a-e929-335b-909d-5b1300de2257")
    public void assembleData(
            List<TeamDetailDto> dtoList,
            TeamDetailDtoDataAssembler.TeamDetailDtoDataHolder dataHolder) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        Map<String, TeamBaseDto> baseDtoMap =
                dataHolder.getRootBaseDtoList().stream()
                        .collect(Collectors.toMap(TeamBaseDto::getId, Function.identity()));

        Map<String, List<Pair<TeamMemberBaseDto, TeamMemberWithStaffDto>>> teamMemberWithStaffList =
                dataHolder.teamMemberWithStaffList.keySet().stream()
                        .collect(
                                Collectors.groupingBy(
                                        dto -> dto.getTeamId(),
                                        Collectors.mapping(
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder.teamMemberWithStaffList
                                                                        .get(dto)),
                                                Collectors.toCollection(ArrayList::new))));
        Map<String, List<Pair<TeamOrganizationBaseDto, TeamOrganizationDto>>> teamOrganizationList =
                dataHolder.teamOrganizationList.keySet().stream()
                        .collect(
                                Collectors.groupingBy(
                                        dto -> dto.getTeamId(),
                                        Collectors.mapping(
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder.teamOrganizationList.get(
                                                                        dto)),
                                                Collectors.toCollection(ArrayList::new))));
        Map<String, OrganizationBaseDto> leadOrganization =
                dataHolder.leadOrganization.stream()
                        .collect(Collectors.toMap(OrganizationBaseDto::getId, Function.identity()));

        for (TeamDetailDto dto : dtoList) {
            dto.setTeamMemberWithStaffList(
                    Optional.ofNullable(teamMemberWithStaffList.get(dto.getId()))
                            .map(
                                    tmp ->
                                            tmp.stream()
                                                    .map(pair -> pair.getRight())
                                                    .collect(Collectors.toList()))
                            .orElse(new ArrayList<>()));
            dto.setTeamOrganizationList(
                    Optional.ofNullable(teamOrganizationList.get(dto.getId()))
                            .map(
                                    tmp ->
                                            tmp.stream()
                                                    .map(pair -> pair.getRight())
                                                    .collect(Collectors.toList()))
                            .orElse(new ArrayList<>()));
            dto.setLeadOrganization(
                    Optional.ofNullable(
                                    leadOrganization.get(
                                            baseDtoMap.get(dto.getId()).getLeadOrganizationId()))
                            .orElse(null));
        }

        assembleTeamMemberWithStaffListData(dataHolder);
        assembleTeamOrganizationListData(dataHolder);

        assembleDataCustomized(dtoList); // 自定义处理逻辑
    }

    /** 组装teamMemberWithStaffList数据 */
    @AutoGenerated(locked = true, uuid = "8a591841-064c-3bbb-b27e-290100a08a4a")
    private void assembleTeamMemberWithStaffListData(
            TeamDetailDtoDataAssembler.TeamDetailDtoDataHolder dataHolder) {
        Map<String, Pair<StaffBaseDto, StaffWithExtensionDto>> teamMemberWithStaffList2Staff =
                dataHolder.teamMemberWithStaffList2Staff.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getId(),
                                        dto ->
                                                Pair.of(
                                                        dto,
                                                        dataHolder.teamMemberWithStaffList2Staff
                                                                .get(dto)),
                                        (o1, o2) -> o1));
        for (Map.Entry<TeamMemberBaseDto, TeamMemberWithStaffDto> teamMemberWithStaffList :
                dataHolder.teamMemberWithStaffList.entrySet()) {
            TeamMemberWithStaffDto dto = teamMemberWithStaffList.getValue();
            dto.setStaff(
                    Optional.ofNullable(
                                    teamMemberWithStaffList2Staff.get(
                                            teamMemberWithStaffList.getKey().getStaffId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }
        assembleTeamMemberWithStaffList2StaffData(dataHolder);
    }

    /** 组装teamMemberWithStaffList2Staff数据 */
    @AutoGenerated(locked = true, uuid = "adc96925-ddd2-3e45-887a-944b51b66657")
    private void assembleTeamMemberWithStaffList2StaffData(
            TeamDetailDtoDataAssembler.TeamDetailDtoDataHolder dataHolder) {
        Map<String, StaffExtensionBaseDto> teamMemberWithStaffList2Staff2StaffExtension =
                dataHolder.teamMemberWithStaffList2Staff2StaffExtension.stream()
                        .collect(
                                Collectors.toMap(
                                        StaffExtensionBaseDto::getStaffId,
                                        Function.identity(),
                                        (o1, o2) -> o1));
        Map<String, OrganizationBaseDto> teamMemberWithStaffList2Staff2Organization =
                dataHolder.teamMemberWithStaffList2Staff2Organization.stream()
                        .collect(
                                Collectors.toMap(
                                        OrganizationBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));
        Map<String, OrganizationBaseDto> teamMemberWithStaffList2Staff2AccountingOrganization =
                dataHolder.teamMemberWithStaffList2Staff2AccountingOrganization.stream()
                        .collect(
                                Collectors.toMap(
                                        OrganizationBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));
        Map<String, OrganizationBaseDto> teamMemberWithStaffList2Staff2HrOrganization =
                dataHolder.teamMemberWithStaffList2Staff2HrOrganization.stream()
                        .collect(
                                Collectors.toMap(
                                        OrganizationBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));
        for (Map.Entry<StaffBaseDto, StaffWithExtensionDto> teamMemberWithStaffList2Staff :
                dataHolder.teamMemberWithStaffList2Staff.entrySet()) {
            StaffWithExtensionDto dto = teamMemberWithStaffList2Staff.getValue();
            dto.setStaffExtension(
                    Optional.ofNullable(
                                    teamMemberWithStaffList2Staff2StaffExtension.get(dto.getId()))
                            .orElse(null));
            dto.setOrganization(
                    Optional.ofNullable(
                                    teamMemberWithStaffList2Staff2Organization.get(
                                            teamMemberWithStaffList2Staff
                                                    .getKey()
                                                    .getOrganizationId()))
                            .orElse(null));
            dto.setAccountingOrganization(
                    Optional.ofNullable(
                                    teamMemberWithStaffList2Staff2AccountingOrganization.get(
                                            teamMemberWithStaffList2Staff
                                                    .getKey()
                                                    .getAccountingOrganizationId()))
                            .orElse(null));
            dto.setHrOrganization(
                    Optional.ofNullable(
                                    teamMemberWithStaffList2Staff2HrOrganization.get(
                                            teamMemberWithStaffList2Staff
                                                    .getKey()
                                                    .getHrOrganizationId()))
                            .orElse(null));
        }
    }

    /** 组装teamOrganizationList数据 */
    @AutoGenerated(locked = true, uuid = "b3607e8e-9535-33aa-b5d8-b84288b09a02")
    private void assembleTeamOrganizationListData(
            TeamDetailDtoDataAssembler.TeamDetailDtoDataHolder dataHolder) {
        Map<String, OrganizationBaseDto> teamOrganizationList2Organization =
                dataHolder.teamOrganizationList2Organization.stream()
                        .collect(
                                Collectors.toMap(
                                        OrganizationBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));
        for (Map.Entry<TeamOrganizationBaseDto, TeamOrganizationDto> teamOrganizationList :
                dataHolder.teamOrganizationList.entrySet()) {
            TeamOrganizationDto dto = teamOrganizationList.getValue();
            dto.setOrganization(
                    Optional.ofNullable(
                                    teamOrganizationList2Organization.get(
                                            teamOrganizationList.getKey().getOrganizationId()))
                            .orElse(null));
        }
    }

    /** 批量自定义组装TeamDetailDto数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "c300cbaa-c3bb-38c4-b30e-93bf5722663c")
    public void assembleDataCustomized(List<TeamDetailDto> dataList) {
        // 自定义数据组装

    }

    /** 持有Dto需要的全部数据 */
    @Getter
    @Setter
    public static class TeamDetailDtoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<TeamBaseDto> rootBaseDtoList;

        /** 持有dto字段staffExtension的Dto数据 */
        @AutoGenerated(locked = true)
        public List<StaffExtensionBaseDto> teamMemberWithStaffList2Staff2StaffExtension;

        /** 持有dto字段organization的Dto数据 */
        @AutoGenerated(locked = true)
        public List<OrganizationBaseDto> teamMemberWithStaffList2Staff2Organization;

        /** 持有dto字段accountingOrganization的Dto数据 */
        @AutoGenerated(locked = true)
        public List<OrganizationBaseDto> teamMemberWithStaffList2Staff2AccountingOrganization;

        /** 持有dto字段hrOrganization的Dto数据 */
        @AutoGenerated(locked = true)
        public List<OrganizationBaseDto> teamMemberWithStaffList2Staff2HrOrganization;

        /** 持有dto字段staff的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<StaffBaseDto, StaffWithExtensionDto> teamMemberWithStaffList2Staff;

        /** 持有dto字段teamMemberWithStaffList的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<TeamMemberBaseDto, TeamMemberWithStaffDto> teamMemberWithStaffList;

        /** 持有dto字段organization的Dto数据 */
        @AutoGenerated(locked = true)
        public List<OrganizationBaseDto> teamOrganizationList2Organization;

        /** 持有dto字段teamOrganizationList的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<TeamOrganizationBaseDto, TeamOrganizationDto> teamOrganizationList;

        /** 持有dto字段leadOrganization的Dto数据 */
        @AutoGenerated(locked = true)
        public List<OrganizationBaseDto> leadOrganization;
    }
}
