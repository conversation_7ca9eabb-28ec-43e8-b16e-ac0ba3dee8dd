package com.pulse.organization.service.query;

import com.pulse.organization.manager.converter.StaffConsultingRoomDtoConverter;
import com.pulse.organization.manager.dto.StaffConsultingRoomBaseDto;
import com.pulse.organization.manager.dto.StaffConsultingRoomDto;
import com.pulse.organization.persist.qto.SearchStaffConsultingRoomQto;
import com.pulse.organization.service.StaffConsultingRoomBaseDtoService;
import com.pulse.organization.service.index.entity.SearchStaffConsultingRoomQtoService;
import com.pulse.organization.service.query.assembler.StaffConsultingRoomDtoDataAssembler;
import com.pulse.organization.service.query.assembler.StaffConsultingRoomDtoDataAssembler.StaffConsultingRoomDtoDataHolder;
import com.pulse.organization.service.query.collector.StaffConsultingRoomDtoDataCollector;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** StaffConsultingRoomDto查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "6f934130-590c-3522-8507-903f7b766582")
public class StaffConsultingRoomDtoQueryService {
    @AutoGenerated(locked = true)
    @Resource
    private SearchStaffConsultingRoomQtoService searchStaffConsultingRoomQtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffConsultingRoomBaseDtoService staffConsultingRoomBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffConsultingRoomDtoConverter staffConsultingRoomDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffConsultingRoomDtoDataAssembler staffConsultingRoomDtoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private StaffConsultingRoomDtoDataCollector staffConsultingRoomDtoDataCollector;

    /** 根据SearchStaffConsultingRoomQto查询StaffConsultingRoomDto列表,上限500 */
    @PublicInterface(id = "3fbec08e-33bb-4c9f-bfc9-3a7bd823c011", module = "organization")
    @AutoGenerated(locked = false, uuid = "4cc0704c-5175-3659-8c57-b721cff03f02")
    public List<StaffConsultingRoomDto> searchStaffConsultingRoom(
            @Valid @NotNull SearchStaffConsultingRoomQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchStaffConsultingRoomQtoService.query(qto);
        StaffConsultingRoomDtoDataHolder dataHolder = new StaffConsultingRoomDtoDataHolder();
        List<StaffConsultingRoomDto> result = toDtoList(ids, dataHolder);
        staffConsultingRoomDtoDataCollector.collectDataDefault(dataHolder);
        staffConsultingRoomDtoDataAssembler.assembleData(result, dataHolder);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 将ID列表转换为DtoList */
    @AutoGenerated(locked = true, uuid = "e7e97b84-cc9e-3afd-98cc-8048738b8135")
    private List<StaffConsultingRoomDto> toDtoList(
            List<String> ids, StaffConsultingRoomDtoDataHolder dataHolder) {
        List<StaffConsultingRoomBaseDto> baseDtoList =
                staffConsultingRoomBaseDtoService.getByIds(ids);
        dataHolder.setRootBaseDtoList(baseDtoList);
        Map<String, StaffConsultingRoomDto> dtoMap =
                staffConsultingRoomDtoConverter
                        .convertFromStaffConsultingRoomBaseDtoToStaffConsultingRoomDto(baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        StaffConsultingRoomDto::getId, Function.identity()));
        return ids.stream()
                .map(id -> dtoMap.get(id))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
