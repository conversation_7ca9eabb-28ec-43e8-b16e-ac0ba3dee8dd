package com.pulse.organization.service.query;

import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.persist.qto.ListOrganizationByTypeAndStatusQto;
import com.pulse.organization.persist.qto.SearchOrganizationQto;
import com.pulse.organization.persist.qto.SearchSubOrganizationQto;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.pulse.organization.service.index.entity.ListOrganizationByTypeAndStatusQtoService;
import com.pulse.organization.service.index.entity.SearchOrganizationQtoService;
import com.pulse.organization.service.index.entity.SearchSubOrganizationQtoService;
import com.pulse.organization.service.query.assembler.OrganizationBaseDtoDataAssembler;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** OrganizationBaseDto查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "cc0c5190-a8e1-3ab0-86b9-d0368c50d1b6")
public class OrganizationBaseDtoQueryService {
    @AutoGenerated(locked = true)
    @Resource
    private ListOrganizationByTypeAndStatusQtoService listOrganizationByTypeAndStatusQtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoDataAssembler organizationBaseDtoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private SearchOrganizationQtoService searchOrganizationQtoService;

    @AutoGenerated(locked = true)
    @Resource
    private SearchSubOrganizationQtoService searchSubOrganizationQtoService;

    /** 根据ListOrganizationByTypeAndStatusQto查询OrganizationBaseDto列表,瀑布流 */
    @PublicInterface(id = "371d7d2c-9505-4b5e-ac11-1968938f3a24", module = "organization")
    @AutoGenerated(locked = false, uuid = "12ba6c6a-a7e5-3e9e-a1a5-3f01ad4acb72")
    public VSQueryResult<OrganizationBaseDto> listOrganizationByTypeAndStatusWaterfall(
            @Valid @NotNull ListOrganizationByTypeAndStatusQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = listOrganizationByTypeAndStatusQtoService.queryWaterfall(qto);
        List<OrganizationBaseDto> dtoList = toDtoList(ids);
        organizationBaseDtoDataAssembler.assembleData(dtoList);
        VSQueryResult result = new VSQueryResult();
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        result.setHasMore(listOrganizationByTypeAndStatusQtoService.hasMore(qto));
        result.setScrollId(qto.getScrollId());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 根据ListOrganizationByTypeAndStatusQto查询数量 */
    @PublicInterface(id = "a85c14f4-d258-4e44-bf98-c7b8f70a35f1", module = "organization")
    @AutoGenerated(locked = false, uuid = "1cda45e1-24c3-3681-b9e8-0a46b6491731")
    public Integer listOrganizationByTypeAndStatusCount(
            @Valid @NotNull ListOrganizationByTypeAndStatusQto qto) {
        return listOrganizationByTypeAndStatusQtoService.count(qto);
    }

    /** 将ID列表转换为DtoList */
    @AutoGenerated(locked = true, uuid = "1e00996d-2dd7-3b1f-8f4c-a2b1c4314fec")
    private List<OrganizationBaseDto> toDtoList(List<String> ids) {
        List<OrganizationBaseDto> baseDtoList = organizationBaseDtoService.getByIds(ids);
        Map<String, OrganizationBaseDto> dtoMap =
                baseDtoList.stream()
                        .collect(Collectors.toMap(OrganizationBaseDto::getId, Function.identity()));
        return ids.stream()
                .map(id -> dtoMap.get(id))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 根据ListOrganizationByTypeAndStatusQto查询OrganizationBaseDto列表,上限500 */
    @PublicInterface(id = "55710d11-b2a3-4a87-a36b-7d325a1376e1", module = "organization")
    @AutoGenerated(locked = false, uuid = "2f113a06-877a-3ff3-bb9e-e4b9309a8fd9")
    public List<OrganizationBaseDto> listOrganizationByTypeAndStatus(
            @Valid @NotNull ListOrganizationByTypeAndStatusQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = listOrganizationByTypeAndStatusQtoService.query(qto);
        List<OrganizationBaseDto> result = toDtoList(ids);
        organizationBaseDtoDataAssembler.assembleData(result);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 根据SearchSubOrganizationQto查询OrganizationBaseDto列表,瀑布流 */
    @PublicInterface(id = "ff392485-0a73-4802-9df2-c7b22bd8257e", module = "organization")
    @AutoGenerated(locked = false, uuid = "5266dee4-1d79-32b9-a53d-144e77cd7e05")
    public VSQueryResult<OrganizationBaseDto> searchSubOrganizationWaterfall(
            @Valid @NotNull SearchSubOrganizationQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchSubOrganizationQtoService.queryWaterfall(qto);
        List<OrganizationBaseDto> dtoList = toDtoList(ids);
        organizationBaseDtoDataAssembler.assembleData(dtoList);
        VSQueryResult result = new VSQueryResult();
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        result.setHasMore(searchSubOrganizationQtoService.hasMore(qto));
        result.setScrollId(qto.getScrollId());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 根据SearchSubOrganizationQto查询OrganizationBaseDto列表,上限500 */
    @PublicInterface(id = "d0b709dc-d8d9-4950-9f50-5abd223de764", module = "organization")
    @AutoGenerated(locked = false, uuid = "5a1d7715-e13e-3647-af52-31f4c5ddb080")
    public List<OrganizationBaseDto> searchSubOrganization(
            @Valid @NotNull SearchSubOrganizationQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchSubOrganizationQtoService.query(qto);
        List<OrganizationBaseDto> result = toDtoList(ids);
        organizationBaseDtoDataAssembler.assembleData(result);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 根据SearchSubOrganizationQto查询数量 */
    @PublicInterface(id = "e7224767-68cc-4635-b7a2-d06d4c9dfa4a", module = "organization")
    @AutoGenerated(locked = false, uuid = "5a605e8b-e5de-3d43-95dd-9bb0b31cec39")
    public Integer searchSubOrganizationCount(@Valid @NotNull SearchSubOrganizationQto qto) {
        return searchSubOrganizationQtoService.count(qto);
    }

    /** 根据SearchOrganizationQto查询OrganizationBaseDto列表,分页 */
    @PublicInterface(id = "f1c22d2c-fa6a-4d8b-b6de-62e5d3e93dd4", module = "organization")
    @AutoGenerated(locked = false, uuid = "5cc70a77-46e0-3afd-ae00-9e219b0eae36")
    public VSQueryResult<OrganizationBaseDto> searchOrganizationPaged(
            @Valid @NotNull SearchOrganizationQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchOrganizationQtoService.queryPaged(qto);
        List<OrganizationBaseDto> dtoList = toDtoList(ids);
        organizationBaseDtoDataAssembler.assembleData(dtoList);
        VSQueryResult result = new VSQueryResult();
        result.setCount(searchOrganizationQtoService.count(qto));
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 根据ListOrganizationByTypeAndStatusQto查询OrganizationBaseDto列表,分页 */
    @PublicInterface(id = "d6b1cf7c-dbde-4b6f-9ef1-f1f0366b5392", module = "organization")
    @AutoGenerated(locked = false, uuid = "68640c08-4270-327f-a9a1-2b7584303a16")
    public VSQueryResult<OrganizationBaseDto> listOrganizationByTypeAndStatusPaged(
            @Valid @NotNull ListOrganizationByTypeAndStatusQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = listOrganizationByTypeAndStatusQtoService.queryPaged(qto);
        List<OrganizationBaseDto> dtoList = toDtoList(ids);
        organizationBaseDtoDataAssembler.assembleData(dtoList);
        VSQueryResult result = new VSQueryResult();
        result.setCount(listOrganizationByTypeAndStatusQtoService.count(qto));
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 根据SearchSubOrganizationQto查询OrganizationBaseDto列表,分页 */
    @PublicInterface(id = "b1f68ca2-1ca3-4643-bd12-944b3a941893", module = "organization")
    @AutoGenerated(locked = false, uuid = "6c6d1a30-f6d2-356e-8f9e-d84b2342a879")
    public VSQueryResult<OrganizationBaseDto> searchSubOrganizationPaged(
            @Valid @NotNull SearchSubOrganizationQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchSubOrganizationQtoService.queryPaged(qto);
        List<OrganizationBaseDto> dtoList = toDtoList(ids);
        organizationBaseDtoDataAssembler.assembleData(dtoList);
        VSQueryResult result = new VSQueryResult();
        result.setCount(searchSubOrganizationQtoService.count(qto));
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 根据SearchOrganizationQto查询OrganizationBaseDto列表,瀑布流 */
    @PublicInterface(id = "e0b728cf-a9aa-4a6a-948d-bd1a3924f9f5", module = "organization")
    @AutoGenerated(locked = false, uuid = "bdd82429-0c8e-3f70-a9e3-3ca9aa63eb73")
    public VSQueryResult<OrganizationBaseDto> searchOrganizationWaterfall(
            @Valid @NotNull SearchOrganizationQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchOrganizationQtoService.queryWaterfall(qto);
        List<OrganizationBaseDto> dtoList = toDtoList(ids);
        organizationBaseDtoDataAssembler.assembleData(dtoList);
        VSQueryResult result = new VSQueryResult();
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        result.setHasMore(searchOrganizationQtoService.hasMore(qto));
        result.setScrollId(qto.getScrollId());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 根据SearchOrganizationQto查询数量 */
    @PublicInterface(id = "f1371bcd-ff67-473e-abba-9a1ff1db3ba7", module = "organization")
    @AutoGenerated(locked = false, uuid = "d940c21a-fc8d-3d77-a273-c2ed2d17390e")
    public Integer searchOrganizationCount(@Valid @NotNull SearchOrganizationQto qto) {
        return searchOrganizationQtoService.count(qto);
    }

    /** 根据SearchOrganizationQto查询OrganizationBaseDto列表,上限500 */
    @PublicInterface(id = "a9c6b320-00e2-430e-835c-b38d4f199d30", module = "organization")
    @AutoGenerated(locked = false, uuid = "f7829d9f-d03c-3965-9518-f29dd464cc9e")
    public List<OrganizationBaseDto> searchOrganization(@Valid @NotNull SearchOrganizationQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchOrganizationQtoService.query(qto);
        List<OrganizationBaseDto> result = toDtoList(ids);
        organizationBaseDtoDataAssembler.assembleData(result);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
