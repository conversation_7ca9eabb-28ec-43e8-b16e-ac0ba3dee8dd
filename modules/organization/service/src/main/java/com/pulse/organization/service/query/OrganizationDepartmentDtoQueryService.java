package com.pulse.organization.service.query;

import com.pulse.organization.manager.converter.OrganizationDepartmentDtoConverter;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.OrganizationDepartmentDto;
import com.pulse.organization.persist.qto.SearchOrganizationDepartmentQto;
import com.pulse.organization.persist.qto.SearchPharmacyAndStorageQto;
import com.pulse.organization.persist.qto.SearchRegisterDepartmentQto;
import com.pulse.organization.persist.qto.SearchSubOrganizationDepartmentQto;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.pulse.organization.service.index.entity.SearchOrganizationDepartmentQtoService;
import com.pulse.organization.service.index.entity.SearchPharmacyAndStorageQtoService;
import com.pulse.organization.service.index.entity.SearchRegisterDepartmentQtoService;
import com.pulse.organization.service.index.entity.SearchSubOrganizationDepartmentQtoService;
import com.pulse.organization.service.query.assembler.OrganizationDepartmentDtoDataAssembler;
import com.pulse.organization.service.query.assembler.OrganizationDepartmentDtoDataAssembler.OrganizationDepartmentDtoDataHolder;
import com.pulse.organization.service.query.collector.OrganizationDepartmentDtoDataCollector;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** OrganizationDepartmentDto查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "cd1762e2-a77d-311b-8fca-bf725631352e")
public class OrganizationDepartmentDtoQueryService {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationDepartmentDtoConverter organizationDepartmentDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationDepartmentDtoDataAssembler organizationDepartmentDtoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationDepartmentDtoDataCollector organizationDepartmentDtoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private SearchOrganizationDepartmentQtoService searchOrganizationDepartmentQtoService;

    @AutoGenerated(locked = true)
    @Resource
    private SearchPharmacyAndStorageQtoService searchPharmacyAndStorageQtoService;

    @AutoGenerated(locked = true)
    @Resource
    private SearchRegisterDepartmentQtoService searchRegisterDepartmentQtoService;

    @AutoGenerated(locked = true)
    @Resource
    private SearchSubOrganizationDepartmentQtoService searchSubOrganizationDepartmentQtoService;

    /** 将ID列表转换为DtoList */
    @AutoGenerated(locked = true, uuid = "28dcfa13-bd3d-305c-8216-417ae748925b")
    private List<OrganizationDepartmentDto> toDtoList(
            List<String> ids, OrganizationDepartmentDtoDataHolder dataHolder) {
        List<OrganizationBaseDto> baseDtoList = organizationBaseDtoService.getByIds(ids);
        dataHolder.setRootBaseDtoList(baseDtoList);
        Map<String, OrganizationDepartmentDto> dtoMap =
                organizationDepartmentDtoConverter
                        .convertFromOrganizationBaseDtoToOrganizationDepartmentDto(baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        OrganizationDepartmentDto::getId, Function.identity()));
        return ids.stream()
                .map(id -> dtoMap.get(id))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 根据SearchOrganizationDepartmentQto查询OrganizationDepartmentDto列表,瀑布流 */
    @PublicInterface(id = "ede2d71b-7c72-4106-8901-793b0ee219e5", module = "organization")
    @AutoGenerated(locked = false, uuid = "2b0a1477-9ef3-3fab-98b2-7dbab63ab9a9")
    public VSQueryResult<OrganizationDepartmentDto> searchOrganizationDepartmentWaterfall(
            @Valid @NotNull SearchOrganizationDepartmentQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchOrganizationDepartmentQtoService.queryWaterfall(qto);
        OrganizationDepartmentDtoDataHolder dataHolder = new OrganizationDepartmentDtoDataHolder();
        List<OrganizationDepartmentDto> dtoList = toDtoList(ids, dataHolder);
        organizationDepartmentDtoDataCollector.collectDataDefault(dataHolder);
        organizationDepartmentDtoDataAssembler.assembleData(dtoList, dataHolder);
        VSQueryResult result = new VSQueryResult();
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        result.setHasMore(searchOrganizationDepartmentQtoService.hasMore(qto));
        result.setScrollId(qto.getScrollId());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 根据SearchRegisterDepartmentQto查询OrganizationDepartmentDto列表,分页 */
    @PublicInterface(id = "ce0e0ecc-e44b-405d-a8d7-4e08dc7683b0", module = "organization")
    @AutoGenerated(locked = false, uuid = "324bbd8c-2106-3e55-8953-5d376ff5abb1")
    public VSQueryResult<OrganizationDepartmentDto> searchRegisterDepartmentPaged(
            @Valid @NotNull SearchRegisterDepartmentQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchRegisterDepartmentQtoService.queryPaged(qto);
        OrganizationDepartmentDtoDataHolder dataHolder = new OrganizationDepartmentDtoDataHolder();
        List<OrganizationDepartmentDto> dtoList = toDtoList(ids, dataHolder);
        organizationDepartmentDtoDataCollector.collectDataDefault(dataHolder);
        organizationDepartmentDtoDataAssembler.assembleData(dtoList, dataHolder);
        VSQueryResult result = new VSQueryResult();
        result.setCount(searchRegisterDepartmentQtoService.count(qto));
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 根据SearchPharmacyAndStorageQto查询OrganizationDepartmentDto列表,分页 */
    @PublicInterface(id = "407e812d-f30a-44d4-a097-5a18bcff01d5", module = "organization")
    @AutoGenerated(locked = false, uuid = "3f92a3e2-a827-3484-927f-18c4aa5f5e72")
    public VSQueryResult<OrganizationDepartmentDto> searchPharmacyAndStoragePaged(
            @Valid @NotNull SearchPharmacyAndStorageQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchPharmacyAndStorageQtoService.queryPaged(qto);
        OrganizationDepartmentDtoDataHolder dataHolder = new OrganizationDepartmentDtoDataHolder();
        List<OrganizationDepartmentDto> dtoList = toDtoList(ids, dataHolder);
        organizationDepartmentDtoDataCollector.collectDataDefault(dataHolder);
        organizationDepartmentDtoDataAssembler.assembleData(dtoList, dataHolder);
        VSQueryResult result = new VSQueryResult();
        result.setCount(searchPharmacyAndStorageQtoService.count(qto));
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 根据SearchOrganizationDepartmentQto查询数量 */
    @PublicInterface(id = "cef15075-8902-4ffb-911a-91ab72b98ea1", module = "organization")
    @AutoGenerated(locked = false, uuid = "*************-3c1a-8a53-7d0033111dab")
    public Integer searchOrganizationDepartmentCount(
            @Valid @NotNull SearchOrganizationDepartmentQto qto) {
        return searchOrganizationDepartmentQtoService.count(qto);
    }

    /** 根据SearchRegisterDepartmentQto查询OrganizationDepartmentDto列表,上限500 */
    @PublicInterface(id = "9fadb9a8-1965-4ef5-9db3-780ea2cf156f", module = "organization")
    @AutoGenerated(locked = false, uuid = "a7a0d150-d306-35dd-84d5-e7b7b1b5de17")
    public List<OrganizationDepartmentDto> searchRegisterDepartment(
            @Valid @NotNull SearchRegisterDepartmentQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchRegisterDepartmentQtoService.query(qto);
        OrganizationDepartmentDtoDataHolder dataHolder = new OrganizationDepartmentDtoDataHolder();
        List<OrganizationDepartmentDto> result = toDtoList(ids, dataHolder);
        organizationDepartmentDtoDataCollector.collectDataDefault(dataHolder);
        organizationDepartmentDtoDataAssembler.assembleData(result, dataHolder);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 根据SearchOrganizationDepartmentQto查询OrganizationDepartmentDto列表,上限2000 */
    @PublicInterface(
            id = "f149a2bd-8f1d-4a3e-a849-ecc8a7d266ab",
            module = "organization",
            moduleId = "a3b95408-2257-4bfa-aafe-59cc5547e63c",
            pubRpc = true,
            version = "1748572642705")
    @AutoGenerated(locked = false, uuid = "a9172c1a-aee5-3a46-bfa0-2582f2c53ec3")
    public List<OrganizationDepartmentDto> searchOrganizationDepartment(
            @Valid @NotNull SearchOrganizationDepartmentQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchOrganizationDepartmentQtoService.query(qto);
        OrganizationDepartmentDtoDataHolder dataHolder = new OrganizationDepartmentDtoDataHolder();
        List<OrganizationDepartmentDto> result = toDtoList(ids, dataHolder);
        organizationDepartmentDtoDataCollector.collectDataDefault(dataHolder);
        organizationDepartmentDtoDataAssembler.assembleData(result, dataHolder);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 根据SearchSubOrganizationDepartmentQto查询数量 */
    @PublicInterface(id = "e630267c-6f85-404b-bf26-a2de2e4b4129", module = "organization")
    @AutoGenerated(locked = false, uuid = "b8b2e97d-94cb-3ac9-9bd4-1a618733a364")
    public Integer searchSubOrganizationDepartmentCount(
            @Valid @NotNull SearchSubOrganizationDepartmentQto qto) {
        return searchSubOrganizationDepartmentQtoService.count(qto);
    }

    /** 根据SearchOrganizationDepartmentQto查询OrganizationDepartmentDto列表,分页 */
    @PublicInterface(id = "4ebff018-d605-46ba-b75e-4368e0546930", module = "organization")
    @AutoGenerated(locked = false, uuid = "bfc030d3-7968-35f1-ba1e-0e63123cba4d")
    public VSQueryResult<OrganizationDepartmentDto> searchOrganizationDepartmentPaged(
            @Valid @NotNull SearchOrganizationDepartmentQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchOrganizationDepartmentQtoService.queryPaged(qto);
        OrganizationDepartmentDtoDataHolder dataHolder = new OrganizationDepartmentDtoDataHolder();
        List<OrganizationDepartmentDto> dtoList = toDtoList(ids, dataHolder);
        organizationDepartmentDtoDataCollector.collectDataDefault(dataHolder);
        organizationDepartmentDtoDataAssembler.assembleData(dtoList, dataHolder);
        VSQueryResult result = new VSQueryResult();
        result.setCount(searchOrganizationDepartmentQtoService.count(qto));
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 根据SearchPharmacyAndStorageQto查询OrganizationDepartmentDto列表,上限500 */
    @PublicInterface(
            id = "5d7e3252-5d65-4071-82c6-d2b2c380cc48",
            module = "organization",
            moduleId = "a3b95408-2257-4bfa-aafe-59cc5547e63c",
            pubRpc = true,
            version = "1747622965055")
    @AutoGenerated(locked = false, uuid = "e2ecf346-7699-39bc-a37b-deac6327fdb6")
    public List<OrganizationDepartmentDto> searchPharmacyAndStorage(
            @Valid @NotNull SearchPharmacyAndStorageQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchPharmacyAndStorageQtoService.query(qto);
        OrganizationDepartmentDtoDataHolder dataHolder = new OrganizationDepartmentDtoDataHolder();
        List<OrganizationDepartmentDto> result = toDtoList(ids, dataHolder);
        organizationDepartmentDtoDataCollector.collectDataDefault(dataHolder);
        organizationDepartmentDtoDataAssembler.assembleData(result, dataHolder);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 根据SearchSubOrganizationDepartmentQto查询OrganizationDepartmentDto列表,上限500 */
    @PublicInterface(
            id = "1d8c02de-857c-4941-8edc-bf7d0c79fd84",
            module = "organization",
            moduleId = "a3b95408-2257-4bfa-aafe-59cc5547e63c",
            pubRpc = true,
            version = "1746771309762")
    @AutoGenerated(locked = false, uuid = "f5c150fe-021f-3fa8-8137-e55e8fb74c64")
    public List<OrganizationDepartmentDto> searchSubOrganizationDepartment(
            @Valid @NotNull SearchSubOrganizationDepartmentQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchSubOrganizationDepartmentQtoService.query(qto);
        OrganizationDepartmentDtoDataHolder dataHolder = new OrganizationDepartmentDtoDataHolder();
        List<OrganizationDepartmentDto> result = toDtoList(ids, dataHolder);
        organizationDepartmentDtoDataCollector.collectDataDefault(dataHolder);
        organizationDepartmentDtoDataAssembler.assembleData(result, dataHolder);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
