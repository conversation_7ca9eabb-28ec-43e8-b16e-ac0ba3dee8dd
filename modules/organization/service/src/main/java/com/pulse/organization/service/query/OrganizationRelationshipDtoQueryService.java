package com.pulse.organization.service.query;

import com.pulse.organization.manager.converter.OrganizationRelationshipDtoConverter;
import com.pulse.organization.manager.dto.OrganizationRelationshipBaseDto;
import com.pulse.organization.manager.dto.OrganizationRelationshipDto;
import com.pulse.organization.persist.qto.SearchOrganizationRelationshipQto;
import com.pulse.organization.service.OrganizationRelationshipBaseDtoService;
import com.pulse.organization.service.index.entity.SearchOrganizationRelationshipQtoService;
import com.pulse.organization.service.query.assembler.OrganizationRelationshipDtoDataAssembler;
import com.pulse.organization.service.query.assembler.OrganizationRelationshipDtoDataAssembler.OrganizationRelationshipDtoDataHolder;
import com.pulse.organization.service.query.collector.OrganizationRelationshipDtoDataCollector;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** OrganizationRelationshipDto查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "38f7f0b4-3041-3122-b231-ba4c6478b9b0")
public class OrganizationRelationshipDtoQueryService {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationRelationshipBaseDtoService organizationRelationshipBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationRelationshipDtoConverter organizationRelationshipDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationRelationshipDtoDataAssembler organizationRelationshipDtoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationRelationshipDtoDataCollector organizationRelationshipDtoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private SearchOrganizationRelationshipQtoService searchOrganizationRelationshipQtoService;

    /** 根据SearchOrganizationRelationshipQto查询OrganizationRelationshipDto列表,上限500 */
    @PublicInterface(id = "6d22921b-a35b-42e2-a9e9-34e1d0db4d21", module = "organization")
    @AutoGenerated(locked = false, uuid = "bc649a24-fa30-3668-a462-f06767a1304d")
    public List<OrganizationRelationshipDto> searchOrganizationRelationship(
            @Valid @NotNull SearchOrganizationRelationshipQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchOrganizationRelationshipQtoService.query(qto);
        OrganizationRelationshipDtoDataHolder dataHolder =
                new OrganizationRelationshipDtoDataHolder();
        List<OrganizationRelationshipDto> result = toDtoList(ids, dataHolder);
        organizationRelationshipDtoDataCollector.collectDataDefault(dataHolder);
        organizationRelationshipDtoDataAssembler.assembleData(result, dataHolder);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 将ID列表转换为DtoList */
    @AutoGenerated(locked = true, uuid = "dab73d4f-54c7-3323-b56a-a6d0f07198a3")
    private List<OrganizationRelationshipDto> toDtoList(
            List<String> ids, OrganizationRelationshipDtoDataHolder dataHolder) {
        List<OrganizationRelationshipBaseDto> baseDtoList =
                organizationRelationshipBaseDtoService.getByIds(ids);
        dataHolder.setRootBaseDtoList(baseDtoList);
        Map<String, OrganizationRelationshipDto> dtoMap =
                organizationRelationshipDtoConverter
                        .convertFromOrganizationRelationshipBaseDtoToOrganizationRelationshipDto(
                                baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        OrganizationRelationshipDto::getId, Function.identity()));
        return ids.stream()
                .map(id -> dtoMap.get(id))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
