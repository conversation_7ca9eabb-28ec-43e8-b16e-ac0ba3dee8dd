package com.pulse.organization.service.index.entity;

import com.pulse.organization.persist.mapper.SearchOrganizationInstitutionQtoDao;
import com.pulse.organization.persist.qto.SearchOrganizationInstitutionQto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = true, uuid = "ce827df2-7950-41dd-bfb0-dd6313b4a654|QTO|SERVICE")
public class SearchOrganizationInstitutionQtoService {
    @AutoGenerated(locked = true)
    @Resource
    private SearchOrganizationInstitutionQtoDao searchOrganizationInstitutionMapper;

    /** 不分页查询入口 */
    @AutoGenerated(locked = true, uuid = "ce827df2-7950-41dd-bfb0-dd6313b4a654-query")
    public List<String> query(SearchOrganizationInstitutionQto qto) {
        return searchOrganizationInstitutionMapper.query(qto);
    }

    /** 计算总数量 */
    @AutoGenerated(locked = true)
    public Integer count(SearchOrganizationInstitutionQto qto) {
        return searchOrganizationInstitutionMapper.count(qto);
    }
}
