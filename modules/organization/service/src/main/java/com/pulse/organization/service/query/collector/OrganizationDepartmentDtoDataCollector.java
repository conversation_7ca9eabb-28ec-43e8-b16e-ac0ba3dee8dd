package com.pulse.organization.service.query.collector;

import com.pulse.organization.manager.converter.DepartmentDtoConverter;
import com.pulse.organization.manager.dto.DepartmentBaseDto;
import com.pulse.organization.manager.dto.DepartmentDto;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.service.DepartmentBaseDtoService;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.pulse.organization.service.query.assembler.OrganizationDepartmentDtoDataAssembler.OrganizationDepartmentDtoDataHolder;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装OrganizationDepartmentDto所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "40f949ea-54fa-3f0a-8fe1-f276b8ddf687")
public class OrganizationDepartmentDtoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private DepartmentBaseDtoService departmentBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DepartmentDtoConverter departmentDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationDepartmentDtoDataCollector organizationDepartmentDtoDataCollector;

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "4e2cc125-ac7b-3ffa-9bdc-41b1eede0a89")
    public void collectDataDefault(OrganizationDepartmentDtoDataHolder dataHolder) {
        organizationDepartmentDtoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "d8c1000f-101f-3b3f-b08d-d5eb80f5a946")
    private void fillDataWhenNecessary(OrganizationDepartmentDtoDataHolder dataHolder) {
        List<OrganizationBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.department == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(OrganizationBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DepartmentBaseDto> baseDtoList =
                    departmentBaseDtoService.getByOrganizationIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(DepartmentBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<DepartmentBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(DepartmentBaseDto::getOrganizationId));
            Map<String, DepartmentDto> departmentDtoMap =
                    departmentDtoConverter
                            .convertFromDepartmentBaseDtoToDepartmentDto(baseDtoList)
                            .stream()
                            .collect(Collectors.toMap(DepartmentDto::getId, Function.identity()));
            dataHolder.department =
                    rootDtoList.stream()
                            .map(OrganizationBaseDto::getId)
                            .distinct()
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> departmentDtoMap.get(baseDto.getId()),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }
}
