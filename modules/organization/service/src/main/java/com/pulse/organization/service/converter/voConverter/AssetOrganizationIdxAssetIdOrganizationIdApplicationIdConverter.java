package com.pulse.organization.service.converter.voConverter;

import com.pulse.organization.persist.dos.AssetOrganization;
import com.pulse.organization.persist.dos.AssetOrganization.ApplicationIdAndAssetIdAndOrganizationId;
import com.pulse.organization.persist.eo.IdxAssetIdOrganizationIdApplicationIdEo;
import com.vs.code.AutoGenerated;

@AutoGenerated(locked = true, uuid = "45b1fbfb-bf1d-3ce7-9bc9-8a450d7be10f")
public class AssetOrganizationIdxAssetIdOrganizationIdApplicationIdConverter {

    @AutoGenerated(locked = true)
    public static AssetOrganization.ApplicationIdAndAssetIdAndOrganizationId
            convertFromIdxAssetIdOrganizationIdApplicationIdToInner(
                    IdxAssetIdOrganizationIdApplicationIdEo idxAssetIdOrganizationIdApplicationId) {
        if (null == idxAssetIdOrganizationIdApplicationId) {
            return null;
        }

        ApplicationIdAndAssetIdAndOrganizationId applicationIdAndAssetIdAndOrganizationId =
                new ApplicationIdAndAssetIdAndOrganizationId();
        applicationIdAndAssetIdAndOrganizationId.setAssetId(
                idxAssetIdOrganizationIdApplicationId.getAssetId());
        applicationIdAndAssetIdAndOrganizationId.setOrganizationId(
                idxAssetIdOrganizationIdApplicationId.getOrganizationId());
        applicationIdAndAssetIdAndOrganizationId.setApplicationId(
                idxAssetIdOrganizationIdApplicationId.getApplicationId());
        return applicationIdAndAssetIdAndOrganizationId;
    }
}
