package com.pulse.organization.service.query.assembler;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.consulting_room.manager.dto.ConsultingRoomBaseDto;
import com.pulse.organization.manager.dto.StaffBaseDto;
import com.pulse.organization.manager.dto.StaffConsultingRoomBaseDto;
import com.pulse.organization.manager.dto.StaffConsultingRoomDto;
import com.pulse.organization.service.StaffConsultingRoomBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** StaffConsultingRoomDto数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "ecbb5ea8-8821-3202-833d-73cfd18d3e84")
public class StaffConsultingRoomDtoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private StaffConsultingRoomBaseDtoService staffConsultingRoomBaseDtoService;

    /** 批量自定义组装StaffConsultingRoomDto数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "82f9cda1-73d8-33e1-9a0e-8f2d5a3533b2")
    public void assembleDataCustomized(List<StaffConsultingRoomDto> dataList) {
        // 自定义数据组装

    }

    /** 组装StaffConsultingRoomDto数据 */
    @AutoGenerated(locked = true, uuid = "88a3fb97-632b-319a-8c18-69bd56e0b669")
    public void assembleData(
            List<StaffConsultingRoomDto> dtoList,
            StaffConsultingRoomDtoDataAssembler.StaffConsultingRoomDtoDataHolder dataHolder) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        Map<String, StaffConsultingRoomBaseDto> baseDtoMap =
                dataHolder.getRootBaseDtoList().stream()
                        .collect(
                                Collectors.toMap(
                                        StaffConsultingRoomBaseDto::getId, Function.identity()));

        Map<String, ConsultingRoomBaseDto> consultingRoom =
                dataHolder.consultingRoom.stream()
                        .collect(
                                Collectors.toMap(
                                        ConsultingRoomBaseDto::getId, Function.identity()));
        Map<String, StaffBaseDto> staff =
                dataHolder.staff.stream()
                        .collect(Collectors.toMap(StaffBaseDto::getId, Function.identity()));

        for (StaffConsultingRoomDto dto : dtoList) {
            dto.setConsultingRoom(
                    Optional.ofNullable(
                                    consultingRoom.get(
                                            baseDtoMap.get(dto.getId()).getConsultingRoomId()))
                            .orElse(null));
            dto.setStaff(
                    Optional.ofNullable(staff.get(baseDtoMap.get(dto.getId()).getStaffId()))
                            .orElse(null));
        }

        assembleDataCustomized(dtoList); // 自定义处理逻辑
    }

    /** 持有Dto需要的全部数据 */
    @Getter
    @Setter
    public static class StaffConsultingRoomDtoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<StaffConsultingRoomBaseDto> rootBaseDtoList;

        /** 持有dto字段consultingRoom的Dto数据 */
        @AutoGenerated(locked = true)
        public List<ConsultingRoomBaseDto> consultingRoom;

        /** 持有dto字段staff的Dto数据 */
        @AutoGenerated(locked = true)
        public List<StaffBaseDto> staff;
    }
}
