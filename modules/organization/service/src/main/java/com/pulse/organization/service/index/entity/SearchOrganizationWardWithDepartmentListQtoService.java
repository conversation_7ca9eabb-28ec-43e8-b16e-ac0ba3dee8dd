package com.pulse.organization.service.index.entity;

import cn.hutool.core.lang.Assert;

import com.pulse.organization.persist.mapper.SearchOrganizationWardWithDepartmentListQtoDao;
import com.pulse.organization.persist.qto.SearchOrganizationWardWithDepartmentListQto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = true, uuid = "bfcedab5-01cc-4eca-961f-1de66307ab8b|QTO|SERVICE")
public class SearchOrganizationWardWithDepartmentListQtoService {
    @AutoGenerated(locked = true)
    @Resource
    private SearchOrganizationWardWithDepartmentListQtoDao
            searchOrganizationWardWithDepartmentListMapper;

    /** 不分页查询入口 */
    @AutoGenerated(locked = true, uuid = "bfcedab5-01cc-4eca-961f-1de66307ab8b-query")
    public List<String> query(SearchOrganizationWardWithDepartmentListQto qto) {
        return searchOrganizationWardWithDepartmentListMapper.query(qto);
    }

    /** 分页查询入口 */
    @AutoGenerated(locked = true, uuid = "bfcedab5-01cc-4eca-961f-1de66307ab8b-query-paged")
    public List<String> queryPaged(SearchOrganizationWardWithDepartmentListQto qto) {
        Assert.isTrue(qto.getSize() != null && qto.getSize() > 0, "size 字段必须大于0 ");
        Assert.isTrue(qto.getFrom() != null && qto.getFrom() >= 0, "from 字段必须大于等于0 ");
        return searchOrganizationWardWithDepartmentListMapper.queryPaged(qto);
    }

    /** 计算总数量 */
    @AutoGenerated(locked = true)
    public Integer count(SearchOrganizationWardWithDepartmentListQto qto) {
        return searchOrganizationWardWithDepartmentListMapper.count(qto);
    }
}
