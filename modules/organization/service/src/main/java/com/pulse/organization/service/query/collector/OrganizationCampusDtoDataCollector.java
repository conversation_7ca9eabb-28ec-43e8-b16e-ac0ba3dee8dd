package com.pulse.organization.service.query.collector;

import com.pulse.organization.manager.dto.CampusBaseDto;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.service.CampusBaseDtoService;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.pulse.organization.service.query.assembler.OrganizationCampusDtoDataAssembler.OrganizationCampusDtoDataHolder;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装OrganizationCampusDto所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "aba54244-7570-3b20-a576-b491375dfb64")
public class OrganizationCampusDtoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private CampusBaseDtoService campusBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationCampusDtoDataCollector organizationCampusDtoDataCollector;

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "0bbed48c-2218-35e0-8793-4682c6ac07f3")
    public void collectDataDefault(OrganizationCampusDtoDataHolder dataHolder) {
        organizationCampusDtoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "28e16e73-6c99-3809-abbf-356b52b3b0b7")
    private void fillDataWhenNecessary(OrganizationCampusDtoDataHolder dataHolder) {
        List<OrganizationBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.campus == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(OrganizationBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<CampusBaseDto> baseDtoList =
                    campusBaseDtoService.getByOrganizationIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(CampusBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<CampusBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(CampusBaseDto::getOrganizationId));
            dataHolder.campus =
                    rootDtoList.stream()
                            .map(OrganizationBaseDto::getId)
                            .distinct()
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
    }
}
