package com.pulse.organization.service.query.assembler;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.manager.dto.DepartmentBaseDto;
import com.pulse.organization.manager.dto.DepartmentDto;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.OrganizationDepartmentDto;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** OrganizationDepartmentDto数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "56168f55-9e73-3c2d-920c-b66d968f9cf6")
public class OrganizationDepartmentDtoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    /** 组装OrganizationDepartmentDto数据 */
    @AutoGenerated(locked = true, uuid = "899467c2-7cc2-360a-9eab-4f7d1cb6e8b7")
    public void assembleData(
            List<OrganizationDepartmentDto> dtoList,
            OrganizationDepartmentDtoDataAssembler.OrganizationDepartmentDtoDataHolder dataHolder) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        Map<String, OrganizationBaseDto> baseDtoMap =
                dataHolder.getRootBaseDtoList().stream()
                        .collect(Collectors.toMap(OrganizationBaseDto::getId, Function.identity()));

        Map<String, Pair<DepartmentBaseDto, DepartmentDto>> department =
                dataHolder.department.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getOrganizationId(),
                                        dto -> Pair.of(dto, dataHolder.department.get(dto))));

        for (OrganizationDepartmentDto dto : dtoList) {
            dto.setDepartment(
                    Optional.ofNullable(department.get(dto.getId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDataCustomized(dtoList); // 自定义处理逻辑
    }

    /** 批量自定义组装OrganizationDepartmentDto数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "a201bee0-f1c0-3e58-825b-b9b96ad75ea6")
    public void assembleDataCustomized(List<OrganizationDepartmentDto> dataList) {
        // 自定义数据组装

    }

    /** 持有Dto需要的全部数据 */
    @Getter
    @Setter
    public static class OrganizationDepartmentDtoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<OrganizationBaseDto> rootBaseDtoList;

        /** 持有dto字段department的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DepartmentBaseDto, DepartmentDto> department;
    }
}
