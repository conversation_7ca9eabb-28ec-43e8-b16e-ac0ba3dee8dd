package com.pulse.organization.service.converter;

import com.pulse.organization.manager.dto.TeamOrganizationDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "4e1d9216-dfca-3717-a33c-f4ab84fa36da")
public class TeamOrganizationDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<TeamOrganizationDto> TeamOrganizationDtoConverter(
            List<TeamOrganizationDto> teamOrganizationDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return teamOrganizationDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
