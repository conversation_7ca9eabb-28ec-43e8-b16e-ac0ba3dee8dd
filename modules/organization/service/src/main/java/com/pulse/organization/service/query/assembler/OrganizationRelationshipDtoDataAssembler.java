package com.pulse.organization.service.query.assembler;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.OrganizationRelationshipBaseDto;
import com.pulse.organization.manager.dto.OrganizationRelationshipDto;
import com.pulse.organization.service.OrganizationRelationshipBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** OrganizationRelationshipDto数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "1baaf627-d8d7-3df5-a141-d0f7408605ea")
public class OrganizationRelationshipDtoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationRelationshipBaseDtoService organizationRelationshipBaseDtoService;

    /** 批量自定义组装OrganizationRelationshipDto数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "605d5dd3-0a4e-3d1e-840b-58a0304c96c0")
    public void assembleDataCustomized(List<OrganizationRelationshipDto> dataList) {
        // 自定义数据组装

    }

    /** 组装OrganizationRelationshipDto数据 */
    @AutoGenerated(locked = true, uuid = "7f247f01-731e-3812-a4cd-0f605afa3532")
    public void assembleData(
            List<OrganizationRelationshipDto> dtoList,
            OrganizationRelationshipDtoDataAssembler.OrganizationRelationshipDtoDataHolder
                    dataHolder) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        Map<String, OrganizationRelationshipBaseDto> baseDtoMap =
                dataHolder.getRootBaseDtoList().stream()
                        .collect(
                                Collectors.toMap(
                                        OrganizationRelationshipBaseDto::getId,
                                        Function.identity()));

        Map<String, OrganizationBaseDto> targetOrganization =
                dataHolder.targetOrganization.stream()
                        .collect(Collectors.toMap(OrganizationBaseDto::getId, Function.identity()));
        Map<String, OrganizationBaseDto> sourceOrganization =
                dataHolder.sourceOrganization.stream()
                        .collect(Collectors.toMap(OrganizationBaseDto::getId, Function.identity()));

        for (OrganizationRelationshipDto dto : dtoList) {
            dto.setTargetOrganization(
                    Optional.ofNullable(
                                    targetOrganization.get(
                                            baseDtoMap.get(dto.getId()).getTargetOrganizationId()))
                            .orElse(null));
            dto.setSourceOrganization(
                    Optional.ofNullable(
                                    sourceOrganization.get(
                                            baseDtoMap.get(dto.getId()).getSourceOrganizationId()))
                            .orElse(null));
        }

        assembleDataCustomized(dtoList); // 自定义处理逻辑
    }

    /** 持有Dto需要的全部数据 */
    @Getter
    @Setter
    public static class OrganizationRelationshipDtoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<OrganizationRelationshipBaseDto> rootBaseDtoList;

        /** 持有dto字段targetOrganization的Dto数据 */
        @AutoGenerated(locked = true)
        public List<OrganizationBaseDto> targetOrganization;

        /** 持有dto字段sourceOrganization的Dto数据 */
        @AutoGenerated(locked = true)
        public List<OrganizationBaseDto> sourceOrganization;
    }
}
