package com.pulse.organization.service.converter;

import com.pulse.organization.manager.dto.TeamWithMemberDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "6a997e48-2517-39f4-844a-007fce227239")
public class TeamWithMemberDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<TeamWithMemberDto> TeamWithMemberDtoConverter(
            List<TeamWithMemberDto> teamWithMemberDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return teamWithMemberDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
