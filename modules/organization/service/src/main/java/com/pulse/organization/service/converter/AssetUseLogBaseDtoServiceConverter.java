package com.pulse.organization.service.converter;

import com.pulse.organization.manager.dto.AssetUseLogBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "0dff11bb-716e-3b36-b202-35b3b00c59df")
public class AssetUseLogBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<AssetUseLogBaseDto> AssetUseLogBaseDtoConverter(
            List<AssetUseLogBaseDto> assetUseLogBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return assetUseLogBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
