package com.pulse.organization.service.query.assembler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;

import com.pulse.application.manager.dto.ApplicationBaseDto;
import com.pulse.organization.manager.dto.AssetBaseDto;
import com.pulse.organization.manager.dto.AssetDetailDto;
import com.pulse.organization.manager.dto.AssetOrganizationBaseDto;
import com.pulse.organization.manager.dto.AssetOrganizationDto;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.service.AssetBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** AssetDetailDto数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "e768cbeb-f85f-36b2-b9b3-8d86fc459504")
public class AssetDetailDtoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private AssetBaseDtoService assetBaseDtoService;

    /** 组装AssetDetailDto数据 */
    @AutoGenerated(locked = true, uuid = "369ad2b0-0aa6-35bd-8d7f-26497d5db683")
    public void assembleData(
            List<AssetDetailDto> dtoList,
            AssetDetailDtoDataAssembler.AssetDetailDtoDataHolder dataHolder) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        Map<String, AssetBaseDto> baseDtoMap =
                dataHolder.getRootBaseDtoList().stream()
                        .collect(Collectors.toMap(AssetBaseDto::getId, Function.identity()));

        Map<String, OrganizationBaseDto> organization =
                dataHolder.organization.stream()
                        .collect(Collectors.toMap(OrganizationBaseDto::getId, Function.identity()));
        Map<String, List<Pair<AssetOrganizationBaseDto, AssetOrganizationDto>>>
                assetOrganizationList =
                        dataHolder.assetOrganizationList.keySet().stream()
                                .collect(
                                        Collectors.groupingBy(
                                                dto -> dto.getAssetId(),
                                                Collectors.mapping(
                                                        dto ->
                                                                Pair.of(
                                                                        dto,
                                                                        dataHolder
                                                                                .assetOrganizationList
                                                                                .get(dto)),
                                                        Collectors.toCollection(ArrayList::new))));

        for (AssetDetailDto dto : dtoList) {
            dto.setOrganization(
                    Optional.ofNullable(
                                    organization.get(
                                            baseDtoMap.get(dto.getId()).getOrganizationId()))
                            .orElse(null));
            dto.setAssetOrganizationList(
                    Optional.ofNullable(assetOrganizationList.get(dto.getId()))
                            .map(
                                    tmp ->
                                            tmp.stream()
                                                    .map(pair -> pair.getRight())
                                                    .collect(Collectors.toList()))
                            .orElse(new ArrayList<>()));
        }

        assembleAssetOrganizationListData(dataHolder);

        assembleDataCustomized(dtoList); // 自定义处理逻辑
    }

    /** 批量自定义组装AssetDetailDto数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "ae11962e-9945-383e-a0b8-5d8662eda09e")
    public void assembleDataCustomized(List<AssetDetailDto> dataList) {
        // 自定义数据组装
        for (AssetDetailDto assetDetail : dataList) {
            if (CollUtil.isNotEmpty(assetDetail.getAssetOrganizationList())) {
                List<String> organizationNameList =
                        assetDetail.getAssetOrganizationList().stream()
                                .filter(item -> item.getOrganization() != null)
                                .map(item -> item.getOrganization().getName())
                                .filter(name -> name != null)
                                .distinct()
                                .collect(Collectors.toList());
                String spliceOrganizationName = String.join(",", organizationNameList);
                assetDetail.setAssetOrganizationSpliceName(spliceOrganizationName);

                List<String> applicationNameList =
                        assetDetail.getAssetOrganizationList().stream()
                                .filter(item -> item.getApplication() != null)
                                .map(item -> item.getApplication().getName())
                                .filter(name -> name != null)
                                .distinct()
                                .collect(Collectors.toList());
                String spliceApplicationName = String.join(",", applicationNameList);
                assetDetail.setAssetApplicationSpliceName(spliceApplicationName);
            } else {
                assetDetail.setAssetOrganizationSpliceName(null);
                assetDetail.setAssetApplicationSpliceName(null);
            }
        }
    }

    /** 组装assetOrganizationList数据 */
    @AutoGenerated(locked = true, uuid = "faae06f5-5cb7-365e-8db8-0831b1afb33d")
    private void assembleAssetOrganizationListData(
            AssetDetailDtoDataAssembler.AssetDetailDtoDataHolder dataHolder) {
        Map<String, ApplicationBaseDto> assetOrganizationList2Application =
                dataHolder.assetOrganizationList2Application.stream()
                        .collect(
                                Collectors.toMap(
                                        ApplicationBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));
        Map<String, OrganizationBaseDto> assetOrganizationList2Organization =
                dataHolder.assetOrganizationList2Organization.stream()
                        .collect(
                                Collectors.toMap(
                                        OrganizationBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));
        for (Map.Entry<AssetOrganizationBaseDto, AssetOrganizationDto> assetOrganizationList :
                dataHolder.assetOrganizationList.entrySet()) {
            AssetOrganizationDto dto = assetOrganizationList.getValue();
            dto.setApplication(
                    Optional.ofNullable(
                                    assetOrganizationList2Application.get(
                                            assetOrganizationList.getKey().getApplicationId()))
                            .orElse(null));
            dto.setOrganization(
                    Optional.ofNullable(
                                    assetOrganizationList2Organization.get(
                                            assetOrganizationList.getKey().getOrganizationId()))
                            .orElse(null));
        }
    }

    /** 持有Dto需要的全部数据 */
    @Getter
    @Setter
    public static class AssetDetailDtoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<AssetBaseDto> rootBaseDtoList;

        /** 持有dto字段organization的Dto数据 */
        @AutoGenerated(locked = true)
        public List<OrganizationBaseDto> organization;

        /** 持有dto字段application的Dto数据 */
        @AutoGenerated(locked = true)
        public List<ApplicationBaseDto> assetOrganizationList2Application;

        /** 持有dto字段organization的Dto数据 */
        @AutoGenerated(locked = true)
        public List<OrganizationBaseDto> assetOrganizationList2Organization;

        /** 持有dto字段assetOrganizationList的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<AssetOrganizationBaseDto, AssetOrganizationDto> assetOrganizationList;
    }
}
