package com.pulse.organization.service.query.collector;

import com.pulse.organization.manager.converter.StaffWithExtensionDtoConverter;
import com.pulse.organization.manager.converter.TeamMemberWithStaffDtoConverter;
import com.pulse.organization.manager.converter.TeamOrganizationDtoConverter;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.StaffBaseDto;
import com.pulse.organization.manager.dto.StaffExtensionBaseDto;
import com.pulse.organization.manager.dto.StaffWithExtensionDto;
import com.pulse.organization.manager.dto.TeamBaseDto;
import com.pulse.organization.manager.dto.TeamMemberBaseDto;
import com.pulse.organization.manager.dto.TeamMemberWithStaffDto;
import com.pulse.organization.manager.dto.TeamOrganizationBaseDto;
import com.pulse.organization.manager.dto.TeamOrganizationDto;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.pulse.organization.service.StaffBaseDtoService;
import com.pulse.organization.service.StaffExtensionBaseDtoService;
import com.pulse.organization.service.TeamBaseDtoService;
import com.pulse.organization.service.TeamMemberBaseDtoService;
import com.pulse.organization.service.TeamOrganizationBaseDtoService;
import com.pulse.organization.service.query.assembler.TeamDetailDtoDataAssembler.TeamDetailDtoDataHolder;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装TeamDetailDto所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "8efa4712-2ef2-353e-be63-ce1d4660fa18")
public class TeamDetailDtoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffBaseDtoService staffBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffExtensionBaseDtoService staffExtensionBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffWithExtensionDtoConverter staffWithExtensionDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TeamBaseDtoService teamBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TeamDetailDtoDataCollector teamDetailDtoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private TeamMemberBaseDtoService teamMemberBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TeamMemberWithStaffDtoConverter teamMemberWithStaffDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TeamOrganizationBaseDtoService teamOrganizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TeamOrganizationDtoConverter teamOrganizationDtoConverter;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "2d3f5df2-df8f-3166-be22-daa6e28bee2d")
    private void fillDataWhenNecessary(TeamDetailDtoDataHolder dataHolder) {
        List<TeamBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.teamMemberWithStaffList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(TeamBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<TeamMemberBaseDto> baseDtoList =
                    teamMemberBaseDtoService.getByTeamIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(TeamMemberBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<TeamMemberBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(TeamMemberBaseDto::getTeamId));
            Map<String, TeamMemberWithStaffDto> teamMemberWithStaffDtoMap =
                    teamMemberWithStaffDtoConverter
                            .convertFromTeamMemberBaseDtoToTeamMemberWithStaffDto(baseDtoList)
                            .stream()
                            .collect(
                                    Collectors.toMap(
                                            TeamMemberWithStaffDto::getId, Function.identity()));
            dataHolder.teamMemberWithStaffList =
                    rootDtoList.stream()
                            .map(TeamBaseDto::getId)
                            .distinct()
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    teamMemberWithStaffDtoMap.get(baseDto.getId()),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.teamOrganizationList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(TeamBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<TeamOrganizationBaseDto> baseDtoList =
                    teamOrganizationBaseDtoService.getByTeamIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(TeamOrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<TeamOrganizationBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(TeamOrganizationBaseDto::getTeamId));
            Map<String, TeamOrganizationDto> teamOrganizationDtoMap =
                    teamOrganizationDtoConverter
                            .convertFromTeamOrganizationBaseDtoToTeamOrganizationDto(baseDtoList)
                            .stream()
                            .collect(
                                    Collectors.toMap(
                                            TeamOrganizationDto::getId, Function.identity()));
            dataHolder.teamOrganizationList =
                    rootDtoList.stream()
                            .map(TeamBaseDto::getId)
                            .distinct()
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> teamOrganizationDtoMap.get(baseDto.getId()),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.leadOrganization == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(TeamBaseDto::getLeadOrganizationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            dataHolder.leadOrganization =
                    rootDtoList.stream()
                            .map(TeamBaseDto::getLeadOrganizationId)
                            .distinct()
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
        if (dataHolder.teamMemberWithStaffList2Staff == null) {
            Set<String> ids =
                    dataHolder.teamMemberWithStaffList.keySet().stream()
                            .map(TeamMemberBaseDto::getStaffId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<StaffBaseDto> baseDtoList =
                    staffBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(StaffBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, StaffBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.toMap(StaffBaseDto::getId, Function.identity()));
            Map<String, StaffWithExtensionDto> staffWithExtensionDtoMap =
                    staffWithExtensionDtoConverter
                            .convertFromStaffBaseDtoToStaffWithExtensionDto(baseDtoList)
                            .stream()
                            .collect(
                                    Collectors.toMap(
                                            StaffWithExtensionDto::getId, Function.identity()));
            dataHolder.teamMemberWithStaffList2Staff =
                    dataHolder.teamMemberWithStaffList.keySet().stream()
                            .map(TeamMemberBaseDto::getStaffId)
                            .distinct()
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    staffWithExtensionDtoMap.get(baseDto.getId()),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.teamOrganizationList2Organization == null) {
            Set<String> ids =
                    dataHolder.teamOrganizationList.keySet().stream()
                            .map(TeamOrganizationBaseDto::getOrganizationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            dataHolder.teamOrganizationList2Organization =
                    dataHolder.teamOrganizationList.keySet().stream()
                            .map(TeamOrganizationBaseDto::getOrganizationId)
                            .distinct()
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
        if (dataHolder.teamMemberWithStaffList2Staff2StaffExtension == null) {
            Set<String> ids =
                    dataHolder.teamMemberWithStaffList2Staff.keySet().stream()
                            .map(StaffBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<StaffExtensionBaseDto> baseDtoList =
                    staffExtensionBaseDtoService.getByStaffIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(StaffExtensionBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<StaffExtensionBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(StaffExtensionBaseDto::getStaffId));
            dataHolder.teamMemberWithStaffList2Staff2StaffExtension =
                    dataHolder.teamMemberWithStaffList2Staff.keySet().stream()
                            .map(StaffBaseDto::getId)
                            .distinct()
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
        if (dataHolder.teamMemberWithStaffList2Staff2Organization == null) {
            Set<String> ids =
                    dataHolder.teamMemberWithStaffList2Staff.keySet().stream()
                            .map(StaffBaseDto::getOrganizationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            dataHolder.teamMemberWithStaffList2Staff2Organization =
                    dataHolder.teamMemberWithStaffList2Staff.keySet().stream()
                            .map(StaffBaseDto::getOrganizationId)
                            .distinct()
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
        if (dataHolder.teamMemberWithStaffList2Staff2AccountingOrganization == null) {
            Set<String> ids =
                    dataHolder.teamMemberWithStaffList2Staff.keySet().stream()
                            .map(StaffBaseDto::getAccountingOrganizationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            dataHolder.teamMemberWithStaffList2Staff2AccountingOrganization =
                    dataHolder.teamMemberWithStaffList2Staff.keySet().stream()
                            .map(StaffBaseDto::getAccountingOrganizationId)
                            .distinct()
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
        if (dataHolder.teamMemberWithStaffList2Staff2HrOrganization == null) {
            Set<String> ids =
                    dataHolder.teamMemberWithStaffList2Staff.keySet().stream()
                            .map(StaffBaseDto::getHrOrganizationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            dataHolder.teamMemberWithStaffList2Staff2HrOrganization =
                    dataHolder.teamMemberWithStaffList2Staff.keySet().stream()
                            .map(StaffBaseDto::getHrOrganizationId)
                            .distinct()
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "c36d837d-6c0c-3c7f-8492-5320084686d7")
    public void collectDataDefault(TeamDetailDtoDataHolder dataHolder) {
        teamDetailDtoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
