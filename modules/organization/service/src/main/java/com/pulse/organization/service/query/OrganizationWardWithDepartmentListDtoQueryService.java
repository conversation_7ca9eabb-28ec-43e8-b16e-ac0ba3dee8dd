package com.pulse.organization.service.query;

import com.pulse.organization.manager.converter.OrganizationWardWithDepartmentListDtoConverter;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.OrganizationWardWithDepartmentListDto;
import com.pulse.organization.persist.qto.SearchOrganizationWardWithDepartmentListQto;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.pulse.organization.service.index.entity.SearchOrganizationWardWithDepartmentListQtoService;
import com.pulse.organization.service.query.assembler.OrganizationWardWithDepartmentListDtoDataAssembler;
import com.pulse.organization.service.query.assembler.OrganizationWardWithDepartmentListDtoDataAssembler.OrganizationWardWithDepartmentListDtoDataHolder;
import com.pulse.organization.service.query.collector.OrganizationWardWithDepartmentListDtoDataCollector;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** OrganizationWardWithDepartmentListDto查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "4293ea9a-8602-3ad7-ac8f-fea5b150d2d1")
public class OrganizationWardWithDepartmentListDtoQueryService {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationWardWithDepartmentListDtoConverter
            organizationWardWithDepartmentListDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationWardWithDepartmentListDtoDataAssembler
            organizationWardWithDepartmentListDtoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationWardWithDepartmentListDtoDataCollector
            organizationWardWithDepartmentListDtoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private SearchOrganizationWardWithDepartmentListQtoService
            searchOrganizationWardWithDepartmentListQtoService;

    /**
     * 根据SearchOrganizationWardWithDepartmentListQto查询OrganizationWardWithDepartmentListDto列表,上限500
     */
    @PublicInterface(id = "b5e16f8f-fb71-47d6-addb-139a3f0ad099", module = "organization")
    @AutoGenerated(locked = false, uuid = "4153c5b8-764f-3648-ad23-f5af4106944e")
    public List<OrganizationWardWithDepartmentListDto> searchOrganizationWardWithDepartmentList(
            @Valid @NotNull SearchOrganizationWardWithDepartmentListQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchOrganizationWardWithDepartmentListQtoService.query(qto);
        OrganizationWardWithDepartmentListDtoDataHolder dataHolder =
                new OrganizationWardWithDepartmentListDtoDataHolder();
        List<OrganizationWardWithDepartmentListDto> result = toDtoList(ids, dataHolder);
        organizationWardWithDepartmentListDtoDataCollector.collectDataDefault(dataHolder);
        organizationWardWithDepartmentListDtoDataAssembler.assembleData(result, dataHolder);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 将ID列表转换为DtoList */
    @AutoGenerated(locked = true, uuid = "6591bc80-611d-3324-8b7b-966477afbf38")
    private List<OrganizationWardWithDepartmentListDto> toDtoList(
            List<String> ids, OrganizationWardWithDepartmentListDtoDataHolder dataHolder) {
        List<OrganizationBaseDto> baseDtoList = organizationBaseDtoService.getByIds(ids);
        dataHolder.setRootBaseDtoList(baseDtoList);
        Map<String, OrganizationWardWithDepartmentListDto> dtoMap =
                organizationWardWithDepartmentListDtoConverter
                        .convertFromOrganizationBaseDtoToOrganizationWardWithDepartmentListDto(
                                baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        OrganizationWardWithDepartmentListDto::getId,
                                        Function.identity()));
        return ids.stream()
                .map(id -> dtoMap.get(id))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 根据SearchOrganizationWardWithDepartmentListQto查询OrganizationWardWithDepartmentListDto列表,分页 */
    @PublicInterface(id = "e0983933-8529-4be9-9e80-e3315cbfd8e5", module = "organization")
    @AutoGenerated(locked = false, uuid = "fab31088-eb31-3025-baef-5c0dc8b2908c")
    public VSQueryResult<OrganizationWardWithDepartmentListDto>
            searchOrganizationWardWithDepartmentListPaged(
                    @Valid @NotNull SearchOrganizationWardWithDepartmentListQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchOrganizationWardWithDepartmentListQtoService.queryPaged(qto);
        OrganizationWardWithDepartmentListDtoDataHolder dataHolder =
                new OrganizationWardWithDepartmentListDtoDataHolder();
        List<OrganizationWardWithDepartmentListDto> dtoList = toDtoList(ids, dataHolder);
        organizationWardWithDepartmentListDtoDataCollector.collectDataDefault(dataHolder);
        organizationWardWithDepartmentListDtoDataAssembler.assembleData(dtoList, dataHolder);
        VSQueryResult result = new VSQueryResult();
        result.setCount(searchOrganizationWardWithDepartmentListQtoService.count(qto));
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
