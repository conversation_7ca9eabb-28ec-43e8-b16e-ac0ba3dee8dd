package com.pulse.organization.service.query.assembler;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.OrganizationWardWithDepartmentListDto;
import com.pulse.organization.manager.dto.WardBaseDto;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** OrganizationWardWithDepartmentListDto数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "8d44710b-1a20-3c9f-9c21-1abdf0c7554a")
public class OrganizationWardWithDepartmentListDtoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    /** 批量自定义组装OrganizationWardWithDepartmentListDto数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "7cf51d08-aec1-327c-95fd-e2bdaceb0655")
    public void assembleDataCustomized(List<OrganizationWardWithDepartmentListDto> dataList) {
        // 自定义数据组装

    }

    /** 组装OrganizationWardWithDepartmentListDto数据 */
    @AutoGenerated(locked = true, uuid = "974e728a-f517-3038-990e-c43adcdc67e9")
    public void assembleData(
            List<OrganizationWardWithDepartmentListDto> dtoList,
            OrganizationWardWithDepartmentListDtoDataAssembler
                            .OrganizationWardWithDepartmentListDtoDataHolder
                    dataHolder) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        Map<String, OrganizationBaseDto> baseDtoMap =
                dataHolder.getRootBaseDtoList().stream()
                        .collect(Collectors.toMap(OrganizationBaseDto::getId, Function.identity()));

        Map<String, WardBaseDto> ward =
                dataHolder.ward.stream()
                        .collect(
                                Collectors.toMap(
                                        WardBaseDto::getOrganizationId, Function.identity()));

        for (OrganizationWardWithDepartmentListDto dto : dtoList) {
            dto.setWard(Optional.ofNullable(ward.get(dto.getId())).orElse(null));
        }

        assembleDataCustomized(dtoList); // 自定义处理逻辑
    }

    /** 持有Dto需要的全部数据 */
    @Getter
    @Setter
    public static class OrganizationWardWithDepartmentListDtoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<OrganizationBaseDto> rootBaseDtoList;

        /** 持有dto字段ward的Dto数据 */
        @AutoGenerated(locked = true)
        public List<WardBaseDto> ward;
    }
}
