package com.pulse.organization.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "campus", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "966fdd34-0bc5-456d-8e8a-1e6e7a0295ac|ENTITY|DEFINITION")
public class Campus {
    @AutoGenerated(locked = true, uuid = "6a41dc14-f0a9-4c6f-bc28-cd7751d4d553")
    @TableField(value = "campus_number")
    private String campusNumber;

    @AutoGenerated(locked = true, uuid = "cab33553-cb25-5a03-8253-a0c1d0abd8b0")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "e7e15186-feaa-4365-8842-af274fc43e79")
    @TableField(value = "created_by")
    private String createdBy;

    @AutoGenerated(locked = true, uuid = "118d06ec-65b7-5c2f-9845-9fc85812162e")
    @TableField(value = "deleted_at")
    private Long deletedAt;

    @AutoGenerated(locked = true, uuid = "80b84f84-4582-4ef8-89ac-265c16cb4d1f")
    @TableField(value = "deleted_by")
    private String deletedBy;

    @AutoGenerated(locked = true, uuid = "f20600f2-0e9e-4c2e-952b-574ceafadc67")
    @TableId(value = "id")
    private String id;

    @AutoGenerated(locked = true, uuid = "f433e732-18c5-4649-bc9a-00edda22536d")
    @TableField(value = "medical_insurance_code")
    private String medicalInsuranceCode;

    @AutoGenerated(locked = true, uuid = "847a1a3b-c4c6-4f84-b1c0-c9c2213f1b55")
    @TableField(value = "organization_id")
    private String organizationId;

    @AutoGenerated(locked = true, uuid = "40759f5c-3ee1-5de1-9ad8-38163884b287")
    @TableField(value = "updated_at")
    private Date updatedAt;

    @AutoGenerated(locked = true, uuid = "9c62dd34-99c8-4224-8653-1ded6f126b6f")
    @TableField(value = "updated_by")
    private String updatedBy;
}
