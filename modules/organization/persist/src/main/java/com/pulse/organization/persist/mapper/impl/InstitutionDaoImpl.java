package com.pulse.organization.persist.mapper.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pulse.organization.persist.dos.Institution;
import com.pulse.organization.persist.mapper.InstitutionDao;
import com.pulse.organization.persist.mapper.mybatis.InstitutionMapper;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "8f2bfc26-dccf-49e4-8472-272ed594db3a|ENTITY|DAO")
public class InstitutionDaoImpl implements InstitutionDao {
    @AutoGenerated(locked = true)
    @Resource
    private InstitutionMapper institutionMapper;

    @AutoGenerated(locked = true, uuid = "137ad604-f3fb-313a-9f50-1c8768f213ba")
    @Override
    public List<Institution> getByIds(List<String> id) {
        QueryWrapper<Institution> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", id).eq("deleted_at", 0L).orderByAsc("id");
        return institutionMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "2967a8aa-6682-312c-8682-18232b9eb11b")
    @Override
    public Institution getByOrganizationId(String organizationId) {
        QueryWrapper<Institution> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("organization_id", organizationId).eq("deleted_at", 0L);
        return institutionMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "48fdcb0b-9003-3000-89a6-7e62de1fbe1f")
    @Override
    public Institution getById(String id) {
        QueryWrapper<Institution> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id).eq("deleted_at", 0L);
        return institutionMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "9b459a22-368c-3af5-b737-e9b657e72bb9")
    @Override
    public List<Institution> getByOrganizationIds(List<String> organizationId) {
        QueryWrapper<Institution> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("organization_id", organizationId).eq("deleted_at", 0L).orderByAsc("id");
        return institutionMapper.selectList(queryWrapper);
    }
}
