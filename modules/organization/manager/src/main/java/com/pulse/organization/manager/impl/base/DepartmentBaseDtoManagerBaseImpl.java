package com.pulse.organization.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.manager.DepartmentBaseDtoManager;
import com.pulse.organization.manager.converter.DepartmentBaseDtoConverter;
import com.pulse.organization.manager.dto.DepartmentBaseDto;
import com.pulse.organization.persist.dos.Department;
import com.pulse.organization.persist.mapper.DepartmentDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "d42fc739-6714-4273-b32e-470a8fe339f8|DTO|BASE_MANAGER_IMPL")
public abstract class DepartmentBaseDtoManagerBaseImpl implements DepartmentBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private DepartmentBaseDtoConverter departmentBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private DepartmentDao departmentDao;

    @AutoGenerated(locked = true, uuid = "10f21982-0790-36ee-a278-9716ea05b6d6")
    public List<DepartmentBaseDto> doConvertFromDepartmentToDepartmentBaseDto(
            List<Department> departmentList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(departmentList)) {
            return Collections.emptyList();
        }

        Map<String, DepartmentBaseDto> dtoMap =
                departmentBaseDtoConverter
                        .convertFromDepartmentToDepartmentBaseDto(departmentList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        DepartmentBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<DepartmentBaseDto> departmentBaseDtoList = new ArrayList<>();
        for (Department i : departmentList) {
            DepartmentBaseDto departmentBaseDto = dtoMap.get(i.getId());
            if (departmentBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            departmentBaseDtoList.add(departmentBaseDto);
        }
        return departmentBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "25a8b5a5-364d-3abf-9ac7-f424c3697954")
    @Override
    public List<DepartmentBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<Department> departmentList = departmentDao.getByIds(id);
        if (CollectionUtil.isEmpty(departmentList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, Department> departmentMap =
                departmentList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        departmentList =
                id.stream()
                        .map(i -> departmentMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromDepartmentToDepartmentBaseDto(departmentList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "2c6c0a6b-d863-30b0-b775-b661441671ad")
    @Override
    public DepartmentBaseDto getByOrganizationId(String organizationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DepartmentBaseDto> ret = getByOrganizationIds(Arrays.asList(organizationId));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        DepartmentBaseDto departmentBaseDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return departmentBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "9787b5e6-f5e2-38db-b7cc-bb00f551e9ed")
    @Override
    public List<DepartmentBaseDto> getByOrganizationIds(List<String> organizationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(organizationId)) {
            return Collections.emptyList();
        }

        List<Department> departmentList = departmentDao.getByOrganizationIds(organizationId);
        if (CollectionUtil.isEmpty(departmentList)) {
            return Collections.emptyList();
        }

        return doConvertFromDepartmentToDepartmentBaseDto(departmentList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "a6c3991e-d3cb-38c6-bbe3-09cfba62dbc0")
    @Override
    public DepartmentBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DepartmentBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        DepartmentBaseDto departmentBaseDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return departmentBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
