package com.pulse.organization.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.manager.dto.TeamOrganizationBaseDto;
import com.pulse.organization.persist.dos.TeamOrganization;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "cb630569-18df-4cf0-b091-60ff70555912|DTO|BASE_CONVERTER")
public class TeamOrganizationBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public TeamOrganizationBaseDto convertFromTeamOrganizationToTeamOrganizationBaseDto(
            TeamOrganization teamOrganization) {
        return convertFromTeamOrganizationToTeamOrganizationBaseDto(List.of(teamOrganization))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<TeamOrganizationBaseDto> convertFromTeamOrganizationToTeamOrganizationBaseDto(
            List<TeamOrganization> teamOrganizationList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(teamOrganizationList)) {
            return new ArrayList<>();
        }
        List<TeamOrganizationBaseDto> teamOrganizationBaseDtoList = new ArrayList<>();
        for (TeamOrganization teamOrganization : teamOrganizationList) {
            if (teamOrganization == null) {
                continue;
            }
            TeamOrganizationBaseDto teamOrganizationBaseDto = new TeamOrganizationBaseDto();
            teamOrganizationBaseDto.setId(teamOrganization.getId());
            teamOrganizationBaseDto.setTeamId(teamOrganization.getTeamId());
            teamOrganizationBaseDto.setOrganizationId(teamOrganization.getOrganizationId());
            teamOrganizationBaseDto.setSortNumber(teamOrganization.getSortNumber());
            teamOrganizationBaseDto.setRegisterTypeIdList(teamOrganization.getRegisterTypeIdList());
            teamOrganizationBaseDto.setDefaultFlag(teamOrganization.getDefaultFlag());
            teamOrganizationBaseDto.setCreatedBy(teamOrganization.getCreatedBy());
            teamOrganizationBaseDto.setUpdatedBy(teamOrganization.getUpdatedBy());
            teamOrganizationBaseDto.setDeletedBy(teamOrganization.getDeletedBy());
            teamOrganizationBaseDto.setCreatedAt(teamOrganization.getCreatedAt());
            teamOrganizationBaseDto.setDeletedAt(teamOrganization.getDeletedAt());
            teamOrganizationBaseDto.setUpdatedAt(teamOrganization.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            teamOrganizationBaseDtoList.add(teamOrganizationBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return teamOrganizationBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
