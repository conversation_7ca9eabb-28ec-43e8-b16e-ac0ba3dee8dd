package com.pulse.user.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.manager.dto.StaffUserBaseDto;
import com.pulse.user.entrance.web.query.assembler.StaffUserBaseVoDataAssembler;
import com.pulse.user.entrance.web.vo.StaffUserBaseVo;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到StaffUserBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "030e2b4f-5a7d-4022-a7b0-3ed6159fdc2e|VO|CONVERTER")
public class StaffUserBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private StaffUserBaseVoDataAssembler staffUserBaseVoDataAssembler;

    /** 把StaffUserBaseDto转换成StaffUserBaseVo */
    @AutoGenerated(locked = false, uuid = "030e2b4f-5a7d-4022-a7b0-3ed6159fdc2e-converter-Map")
    public Map<StaffUserBaseDto, StaffUserBaseVo> convertToStaffUserBaseVoMap(
            List<StaffUserBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<StaffUserBaseDto, StaffUserBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            StaffUserBaseVo vo = new StaffUserBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setStaffId(dto.getStaffId());
                                            vo.setUserId(dto.getUserId());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把StaffUserBaseDto转换成StaffUserBaseVo */
    @AutoGenerated(locked = true, uuid = "030e2b4f-5a7d-4022-a7b0-3ed6159fdc2e-converter-list")
    public List<StaffUserBaseVo> convertToStaffUserBaseVoList(List<StaffUserBaseDto> dtoList) {
        return new ArrayList<>(convertToStaffUserBaseVoMap(dtoList).values());
    }

    /** 使用默认方式组装StaffUserBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "0d67531d-65a8-366c-bb7b-a04a80411b45")
    public List<StaffUserBaseVo> convertAndAssembleDataList(List<StaffUserBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, StaffUserBaseVo> voMap =
                convertToStaffUserBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        staffUserBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把StaffUserBaseDto转换成StaffUserBaseVo */
    @AutoGenerated(locked = true, uuid = "49fd0cd9-d22f-3c5a-b1c7-5a89ed1da946")
    public StaffUserBaseVo convertToStaffUserBaseVo(StaffUserBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToStaffUserBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装StaffUserBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "7905e810-88e5-3408-ade4-b2bf3ad1a727")
    public StaffUserBaseVo convertAndAssembleData(StaffUserBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }
}
