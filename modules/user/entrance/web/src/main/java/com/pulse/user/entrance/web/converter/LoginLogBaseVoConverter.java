package com.pulse.user.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.user.entrance.web.query.assembler.LoginLogBaseVoDataAssembler;
import com.pulse.user.entrance.web.vo.LoginLogBaseVo;
import com.pulse.user.manager.dto.LoginLogBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到LoginLogBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "0f08031a-8d30-4fcc-b49f-40fed29f185c|VO|CONVERTER")
public class LoginLogBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private LoginLogBaseVoDataAssembler loginLogBaseVoDataAssembler;

    /** 把LoginLogBaseDto转换成LoginLogBaseVo */
    @AutoGenerated(locked = false, uuid = "0f08031a-8d30-4fcc-b49f-40fed29f185c-converter-Map")
    public Map<LoginLogBaseDto, LoginLogBaseVo> convertToLoginLogBaseVoMap(
            List<LoginLogBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<LoginLogBaseDto, LoginLogBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            LoginLogBaseVo vo = new LoginLogBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setSourceBusinessId(dto.getSourceBusinessId());
                                            vo.setActionTime(dto.getActionTime());
                                            vo.setActionType(dto.getActionType());
                                            vo.setAfterActionInfo(dto.getAfterActionInfo());
                                            vo.setBusinessScene(dto.getBusinessScene());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setName(dto.getName());
                                            vo.setIP(dto.getIP());
                                            vo.setRemark(dto.getRemark());
                                            vo.setDevice(dto.getDevice());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把LoginLogBaseDto转换成LoginLogBaseVo */
    @AutoGenerated(locked = true, uuid = "0f08031a-8d30-4fcc-b49f-40fed29f185c-converter-list")
    public List<LoginLogBaseVo> convertToLoginLogBaseVoList(List<LoginLogBaseDto> dtoList) {
        return new ArrayList<>(convertToLoginLogBaseVoMap(dtoList).values());
    }

    /** 使用默认方式组装LoginLogBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "375190f9-7344-3ffa-96ab-e3efc8b01115")
    public LoginLogBaseVo convertAndAssembleData(LoginLogBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装LoginLogBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "7276f5de-3601-3c6d-a680-826e7fbea7f6")
    public List<LoginLogBaseVo> convertAndAssembleDataList(List<LoginLogBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, LoginLogBaseVo> voMap =
                convertToLoginLogBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        loginLogBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把LoginLogBaseDto转换成LoginLogBaseVo */
    @AutoGenerated(locked = true, uuid = "e445ce83-8667-3ce7-9321-504d72d8e317")
    public LoginLogBaseVo convertToLoginLogBaseVo(LoginLogBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToLoginLogBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }
}
