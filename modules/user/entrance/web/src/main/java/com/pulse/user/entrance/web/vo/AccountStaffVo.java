package com.pulse.user.entrance.web.vo;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.dictionary_business.persist.eo.AddressEo;
import com.pulse.organization.common.enums.GenderEnum;
import com.pulse.organization.common.enums.StaffStatusEnum;
import com.pulse.user.common.enums.AccountStatusEnum;
import com.pulse.user.common.enums.UserStatusEnum;
import com.pulse.user.entrance.web.vo.AccountStaffVo.UserInfoStaffVo;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "2f2a2dd4-e934-448d-bb72-98217db20436|VO|DEFINITION")
public class AccountStaffVo {
    /** 访问令牌 */
    @AutoGenerated(locked = true, uuid = "eca29abf-ba3e-4e19-aef9-b2fb80596cc5")
    private String accessToken;

    /** 访问令牌有效期 */
    @AutoGenerated(locked = true, uuid = "a364a4f9-6a1b-4af7-a5f1-2c36c7e1f8e5")
    private Long accessTokenValidPeriod;

    /** 账户名称 */
    @AutoGenerated(locked = true, uuid = "5d80e07a-b06f-49bd-8ac9-9eb565bae14c")
    private String accountName;

    /** 手机号 */
    @AutoGenerated(locked = true, uuid = "cd568f83-244c-452b-b0b5-21e77afcda5d")
    private String cellphone;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "3404cbee-b922-4472-af68-89880bafe6a4")
    private String id;

    /** 状态 */
    @AutoGenerated(locked = true, uuid = "000bf7de-454a-43b9-a1e3-5d26848154d7")
    private AccountStatusEnum status;

    /** 用户ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "308c7c5f-0a3b-4131-940e-44ca121c5b8c")
    private UserInfoStaffVo user;

    @Setter
    @Getter
    public static class StaffBaseVo {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "60df0677-562f-4abc-9a01-02abcb3dc0f7")
        private String id;

        /** 出生日期 */
        @AutoGenerated(locked = true, uuid = "b9aab2dd-555d-4eff-832d-4856cf0c7bd8")
        private Date birthDate;

        /** 创建时间 */
        @AutoGenerated(locked = true, uuid = "e99965e6-fc46-45d8-a012-f518e751b85b")
        private Date createdAt;

        /** 介绍描述 */
        @AutoGenerated(locked = true, uuid = "a3a915b5-7956-421b-8e2f-45cd4ab3814e")
        private String description;

        /** 电子邮件地址 */
        @AutoGenerated(locked = true, uuid = "75252ce8-d0e0-4c6a-8635-33669fccebe5")
        private String emailAddress;

        /** 性别 */
        @AutoGenerated(locked = true, uuid = "25b20de3-c304-4b48-9201-8e7111773eb8")
        private GenderEnum gender;

        /** 证件编号 */
        @AutoGenerated(locked = true, uuid = "392f0201-0219-4041-9835-dee6b851115c")
        private String certificateNumber;

        /** 输入码 */
        @Valid
        @AutoGenerated(locked = true, uuid = "35a06653-66d8-4674-8af2-d13471a6ff1b")
        private InputCodeEo inputCode;

        /** 姓名 */
        @AutoGenerated(locked = true, uuid = "a4631ee4-f7d9-4af4-807b-424d4ec5f889")
        private String name;

        /** 电话号码 */
        @AutoGenerated(locked = true, uuid = "b0d9da7f-2dcc-4f14-9da3-be36154d689e")
        private String phoneNumber;

        /** 员工编号 */
        @AutoGenerated(locked = true, uuid = "65fffb33-9942-403f-aaa9-95ef0979a747")
        private String staffNumber;

        /** 状态 */
        @AutoGenerated(locked = true, uuid = "369c85d7-7fc8-4baf-9c93-47e153e3d309")
        private StaffStatusEnum status;

        /** 更新时间 */
        @AutoGenerated(locked = true, uuid = "bc872bf8-4184-40f1-9946-16b4155458e6")
        private Date updatedAt;

        /** 手机短号 */
        @AutoGenerated(locked = true, uuid = "ca85141d-5553-4c57-98db-dd8556478dee")
        private String shortPhoneNumber;

        /** 创建者ID */
        @AutoGenerated(locked = true, uuid = "581d622c-c782-4e2a-a4e6-d180ed5a1b0c")
        private String createdBy;

        /** 更新者ID */
        @AutoGenerated(locked = true, uuid = "3e05e958-d0a9-40ca-b0cc-b85399a57ea1")
        private String updateBy;

        /** 删除者ID */
        @AutoGenerated(locked = true, uuid = "2a3a0ddd-65fb-434c-b3ee-8155af8fe162")
        private String deletedBy;

        /** 证件类型id */
        @AutoGenerated(locked = true, uuid = "f08c8781-8692-4b83-92a7-845a004f2994")
        private String certificateTypeId;

        /** 职工类别id */
        @AutoGenerated(locked = true, uuid = "975bd302-4bf9-400e-9fb8-333170680d2f")
        private String staffTypeId;

        /** 专家标志 */
        @AutoGenerated(locked = true, uuid = "340460db-484e-4272-8f36-bd9a8b29c044")
        private Boolean expertFlag;

        /** 职称id */
        @AutoGenerated(locked = true, uuid = "453abec5-3d2f-4401-a54d-7a59cf9040b9")
        private String professionalTitleId;

        /** 序号 */
        @AutoGenerated(locked = true, uuid = "31410dda-f6e0-46c6-ba9f-6b9bd78282e4")
        private Long sortNumber;

        /** 所属组织id */
        @AutoGenerated(locked = true, uuid = "a71dec21-b08f-4010-9eac-a90575f0dd17")
        private String organizationId;

        /** 核算组织id */
        @AutoGenerated(locked = true, uuid = "2ca5c941-5d63-4eb0-a633-9085f732b42a")
        private String accountingOrganizationId;

        /** 人事组织id */
        @AutoGenerated(locked = true, uuid = "4dfe9a00-721d-4cfe-8fd5-c520adb36626")
        private String hrOrganizationId;

        /** 职务id */
        @AutoGenerated(locked = true, uuid = "a56f17fb-d96b-4949-b12d-11187f010b8f")
        private String positionId;

        /** 晋升日期 */
        @AutoGenerated(locked = true, uuid = "cb3b003a-35ec-496d-9198-486aad40dae3")
        private Date promotionDate;

        /** 挂号类别ID列表 */
        @Valid
        @AutoGenerated(locked = true, uuid = "e5c85093-f6fc-4ba6-953d-00ae68740d32")
        private List<String> registerTypeList;

        /** 省平台作废标识 */
        @AutoGenerated(locked = true, uuid = "010bb01b-6197-4f96-a69c-3b8c8b788146")
        private Boolean provincePlatformCancelFlag;

        /** 下乡开始日期 */
        @AutoGenerated(locked = true, uuid = "6716b562-5c3b-4bc6-8b77-8fac25d704d1")
        private Date countrysideStartDate;

        /** 医生备注 */
        @AutoGenerated(locked = true, uuid = "f2e94bca-c1fd-4f5c-b33e-0ba8d2acea7d")
        private String doctorRemark;

        /** 挂号医生标志 */
        @AutoGenerated(locked = true, uuid = "14d7f0ea-2234-48ec-87a3-9b3e3e8780c8")
        private Boolean registerDoctorFlag;

        /** 下乡结束日期 */
        @AutoGenerated(locked = true, uuid = "c55cce40-b0a2-4f34-87ab-38d8f5e2b9d7")
        private Date countrysideEndDate;

        /** 挂号医生启用标志 */
        @AutoGenerated(locked = true, uuid = "4d329c0c-8f95-4cb9-b4b1-c3649f10382a")
        private Boolean registerDoctorEnableFlag;
    }

    @Setter
    @Getter
    public static class StaffUserWithStaffVo {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "dd956627-6808-4a6f-879b-9e9bed7aaed1")
        private String id;

        /** 用户ID */
        @AutoGenerated(locked = true, uuid = "5dd6bc2e-4b1c-42e3-ae31-fbd7f5ecfa15")
        private String userId;

        /** 创建时间 */
        @AutoGenerated(locked = true, uuid = "ff42a766-115d-43cd-a826-982173db1094")
        private Date createdAt;

        /** 更新时间 */
        @AutoGenerated(locked = true, uuid = "e731fe62-0218-4249-a487-d4b891eb2b05")
        private Date updatedAt;

        /** 员工ID */
        @Valid
        @AutoGenerated(locked = true, uuid = "eb3e50d7-5f33-44a2-bf37-c16f34e82cd0")
        private StaffBaseVo staff;
    }

    @Setter
    @Getter
    public static class UserInfoStaffVo {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "6a3ac2a9-bb86-4548-bcaa-e0d4a4877d8d")
        private String id;

        /** 地址 */
        @Valid
        @AutoGenerated(locked = true, uuid = "72d430c7-ff0a-41ca-922e-b72b40069eb5")
        private AddressEo address;

        /** 生日 */
        @AutoGenerated(locked = true, uuid = "d1ec9865-71ea-4f54-9542-4e0072d2b65a")
        private Date birthDay;

        /** 创建时间 */
        @AutoGenerated(locked = true, uuid = "844f6a3d-a7a7-4fc7-bbbd-ef1f8ff55e40")
        private Date createdAt;

        /** 性别 */
        @AutoGenerated(locked = true, uuid = "f42fed0d-12be-45e9-a7e2-ffba7ae59503")
        private GenderEnum gender;

        /** 名字 */
        @AutoGenerated(locked = true, uuid = "66fdfab8-3de2-4e4a-a550-b5f9063e1052")
        private String names;

        /** 状态 */
        @AutoGenerated(locked = true, uuid = "ac83491c-83d9-4c5d-84d2-87033d548c09")
        private UserStatusEnum status;

        /** 更新时间 */
        @AutoGenerated(locked = true, uuid = "0e96fc59-19a0-40d1-b667-453a0cbe3de3")
        private Date updatedAt;

        /** 年龄 */
        @AutoGenerated(locked = true, uuid = "acd82570-f297-47cb-b29e-4e4d00801b1f")
        private Integer age;

        /** 手机号 */
        @AutoGenerated(locked = true, uuid = "02721b69-76e3-4014-a784-7621bfd81b0b")
        private String phoneNumber;

        /** 用户过期开始 */
        @AutoGenerated(locked = true, uuid = "6872ff2d-8b87-453e-a4e4-b9481fae2c76")
        private Date userExpirationStart;

        /** 用户过期结束 */
        @AutoGenerated(locked = true, uuid = "d17c2d5a-16b0-487e-8d72-251399005c1d")
        private Date userExpirationEnd;

        /** 用户姓名 */
        @AutoGenerated(locked = true, uuid = "d7c80031-4ef6-45ff-bae2-fc2f89ba3253")
        private String userName;

        /** 员工信息不带用户信息 */
        @Valid
        @AutoGenerated(locked = true, uuid = "f2033e04-9491-43a8-8823-3f6e5fc3f2b9")
        private StaffUserWithStaffVo staffUserStaffList;
    }
}
