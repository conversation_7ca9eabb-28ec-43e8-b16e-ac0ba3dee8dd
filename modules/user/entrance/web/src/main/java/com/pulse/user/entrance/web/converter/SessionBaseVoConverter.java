package com.pulse.user.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.user.entrance.web.query.assembler.SessionBaseVoDataAssembler;
import com.pulse.user.entrance.web.vo.SessionBaseVo;
import com.pulse.user.manager.dto.SessionBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到SessionBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "8f9deb15-fce0-4076-a515-0a930d713165|VO|CONVERTER")
public class SessionBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private SessionBaseVoDataAssembler sessionBaseVoDataAssembler;

    /** 使用默认方式组装SessionBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "6b18d5d0-f2bd-3a81-9b9b-1910870077f3")
    public SessionBaseVo convertAndAssembleData(SessionBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把SessionBaseDto转换成SessionBaseVo */
    @AutoGenerated(locked = false, uuid = "8f9deb15-fce0-4076-a515-0a930d713165-converter-Map")
    public Map<SessionBaseDto, SessionBaseVo> convertToSessionBaseVoMap(
            List<SessionBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<SessionBaseDto, SessionBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            SessionBaseVo vo = new SessionBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setSessionId(dto.getSessionId());
                                            vo.setClientDeviceType(dto.getClientDeviceType());
                                            vo.setClientIp(dto.getClientIp());
                                            vo.setClientUserProxyInfo(dto.getClientUserProxyInfo());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setLastAccessTime(dto.getLastAccessTime());
                                            vo.setUserId(dto.getUserId());
                                            vo.setExpireTime(dto.getExpireTime());
                                            vo.setStatus(dto.getStatus());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setUserLastActionTime(dto.getUserLastActionTime());
                                            vo.setName(dto.getName());
                                            vo.setCreateDate(dto.getCreateDate());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把SessionBaseDto转换成SessionBaseVo */
    @AutoGenerated(locked = true, uuid = "8f9deb15-fce0-4076-a515-0a930d713165-converter-list")
    public List<SessionBaseVo> convertToSessionBaseVoList(List<SessionBaseDto> dtoList) {
        return new ArrayList<>(convertToSessionBaseVoMap(dtoList).values());
    }

    /** 使用默认方式组装SessionBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "c335222a-6ed8-3894-b3db-781aca8a46a1")
    public List<SessionBaseVo> convertAndAssembleDataList(List<SessionBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, SessionBaseVo> voMap =
                convertToSessionBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        sessionBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把SessionBaseDto转换成SessionBaseVo */
    @AutoGenerated(locked = true, uuid = "cd754999-a601-3658-a227-9a54d8f9909a")
    public SessionBaseVo convertToSessionBaseVo(SessionBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToSessionBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }
}
