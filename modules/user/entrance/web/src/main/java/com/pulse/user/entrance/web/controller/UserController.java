package com.pulse.user.entrance.web.controller;

import com.pulse.organization.manager.dto.StaffUserBaseDto;
import com.pulse.organization.persist.qto.ListStaffUserByUserIdQto;
import com.pulse.organization.service.StaffBOService;
import com.pulse.organization.service.bto.CreateStaffUserBto;
import com.pulse.organization.service.bto.DeleteStaffUserBto;
import com.pulse.organization.service.query.StaffUserBaseDtoQueryService;
import com.pulse.user.entrance.web.converter.AccountStaffVoConverter;
import com.pulse.user.entrance.web.converter.AccountVoConverter;
import com.pulse.user.entrance.web.converter.SessionBaseVoConverter;
import com.pulse.user.entrance.web.converter.UserGroupBaseVoConverter;
import com.pulse.user.entrance.web.converter.UserPreferenceBaseVoConverter;
import com.pulse.user.entrance.web.converter.UserSimpleVoConverter;
import com.pulse.user.entrance.web.converter.UserStaffVoConverter;
import com.pulse.user.entrance.web.vo.AccountStaffVo;
import com.pulse.user.entrance.web.vo.AccountVo;
import com.pulse.user.entrance.web.vo.SessionBaseVo;
import com.pulse.user.entrance.web.vo.UserGroupBaseVo;
import com.pulse.user.entrance.web.vo.UserPreferenceBaseVo;
import com.pulse.user.entrance.web.vo.UserSimpleVo;
import com.pulse.user.entrance.web.vo.UserStaffVo;
import com.pulse.user.manager.dto.AccountDto;
import com.pulse.user.manager.dto.AccountStaffDto;
import com.pulse.user.manager.dto.SessionBaseDto;
import com.pulse.user.manager.dto.UserGroupBaseDto;
import com.pulse.user.manager.dto.UserPreferenceBaseDto;
import com.pulse.user.manager.dto.UserSimpleDto;
import com.pulse.user.manager.dto.UserStaffDto;
import com.pulse.user.persist.qto.*;
import com.pulse.user.service.AccountDtoService;
import com.pulse.user.service.AccountStaffDtoService;
import com.pulse.user.service.UserInfoBOService;
import com.pulse.user.service.UserSessionBOService;
import com.pulse.user.service.bto.CreateAccountBto;
import com.pulse.user.service.bto.CreatePreferenceBto;
import com.pulse.user.service.bto.CreateSessionBto;
import com.pulse.user.service.bto.CreateUserAccountBto;
import com.pulse.user.service.bto.CreateUserBto;
import com.pulse.user.service.bto.DeletePreferenceBto;
import com.pulse.user.service.bto.MergeAccountBto;
import com.pulse.user.service.bto.MergeUserBto;
import com.pulse.user.service.bto.UpdateAccountBto;
import com.pulse.user.service.bto.UpdateSessionBto;
import com.pulse.user.service.bto.UpdateUserStatusBto;
import com.pulse.user.service.query.SessionBaseDtoQueryService;
import com.pulse.user.service.query.UserGroupBaseDtoQueryService;
import com.pulse.user.service.query.UserPreferenceBaseDtoQueryService;
import com.pulse.user.service.query.UserSimpleDtoQueryService;
import com.pulse.user.service.query.UserStaffDtoQueryService;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "0cc0f1fc-dfb5-3199-9495-a84edbe6eb3d")
public class UserController {
    @AutoGenerated(locked = true)
    @Resource
    private AccountDtoService accountDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private AccountStaffDtoService accountStaffDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private AccountStaffVoConverter accountStaffVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private AccountVoConverter accountVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private SessionBaseDtoQueryService sessionBaseDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private SessionBaseVoConverter sessionBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private UserGroupBaseDtoQueryService userGroupBaseDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private UserGroupBaseVoConverter userGroupBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private UserInfoBOService userInfoBOService;

    @AutoGenerated(locked = true)
    @Resource
    private UserPreferenceBaseDtoQueryService userPreferenceBaseDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private UserPreferenceBaseVoConverter userPreferenceBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private UserSessionBOService userSessionBOService;

    @AutoGenerated(locked = true)
    @Resource
    private UserSimpleDtoQueryService userSimpleDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private UserSimpleVoConverter userSimpleVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private UserStaffDtoQueryService userStaffDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private UserStaffVoConverter userStaffVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffBOService staffBOService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffUserBaseDtoQueryService staffUserBaseDtoQueryService;

    /** 修改用户状态 */
    @PublicInterface(id = "090db9e3-2de3-409a-9a19-c6401e404e5b", version = "1743648967973")
    @AutoGenerated(locked = false, uuid = "090db9e3-2de3-409a-9a19-c6401e404e5b")
    @RequestMapping(
            value = {"/api/user/update-user-status"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String updateUserStatus(@Valid UpdateUserStatusBto updateUserStatusBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = userInfoBOService.updateUserStatus(updateUserStatusBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 根据账户名称获取账号 */
    @PublicInterface(id = "0d3744e5-cba5-442a-8705-f4de9fb0358c", version = "*************")
    @AutoGenerated(locked = false, uuid = "0d3744e5-cba5-442a-8705-f4de9fb0358c")
    @RequestMapping(
            value = {"/api/user/get-by-account-name"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public AccountStaffVo getByAccountName(String accountName) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        AccountStaffDto rpcResult = accountStaffDtoService.getByAccountName(accountName);
        AccountStaffVo result = accountStaffVoConverter.convertAndAssembleData(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 创建用户和账号 */
    @PublicInterface(id = "132de92e-4f3e-432e-9be5-6631dd903524", version = "*************")
    @AutoGenerated(locked = false, uuid = "132de92e-4f3e-432e-9be5-6631dd903524")
    @RequestMapping(
            value = {"/api/user/create-user-account"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String createUserAccount(@Valid CreateUserAccountBto createUserAccountBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = userInfoBOService.createUserAccount(createUserAccountBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 修改会话 */
    @PublicInterface(id = "20f73316-2df1-49aa-a5cd-c9a6ef32dbd1", version = "*************")
    @AutoGenerated(locked = false, uuid = "20f73316-2df1-49aa-a5cd-c9a6ef32dbd1")
    @RequestMapping(
            value = {"/api/user/update-session"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String updateSession(@Valid UpdateSessionBto updateSessionBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = userSessionBOService.updateSession(updateSessionBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 通过用户和类型获取偏好 */
    @PublicInterface(id = "2df3c111-07e8-468a-914d-1142021df0a9", version = "*************")
    @AutoGenerated(locked = false, uuid = "2df3c111-07e8-468a-914d-1142021df0a9")
    @RequestMapping(
            value = {"/api/user/query-list-preference-by-user"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<UserPreferenceBaseVo> listPreferenceByUser(@Valid ListPreferenceByUserQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<UserPreferenceBaseDto> rpcResult =
                userPreferenceBaseDtoQueryService.queryListPreferenceByUser(qto);
        List<UserPreferenceBaseVo> result =
                userPreferenceBaseVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** merge用户 */
    @PublicInterface(id = "33b37727-d89d-44df-b10f-ee4bcbc239ba", version = "1743648820783")
    @AutoGenerated(locked = false, uuid = "33b37727-d89d-44df-b10f-ee4bcbc239ba")
    @RequestMapping(
            value = {"/api/user/merge-user"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeUser(@Valid MergeUserBto mergeUserBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = userInfoBOService.mergeUser(mergeUserBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        if (!mergeUserBto.getStaffId().isEmpty() && mergeUserBto.getStaffId() != null) {
            // 先删除原有关联
            ListStaffUserByUserIdQto qto = new ListStaffUserByUserIdQto();
            qto.setSize(10);
            qto.setFrom(0);
            qto.setUserIdIs(result);
            var delList = staffUserBaseDtoQueryService.listStaffUserByUserId(qto);
            for (StaffUserBaseDto staffUserBaseDto : delList.getResult()) {
                DeleteStaffUserBto.StaffUserBto deleteStaffUserBto =
                        new DeleteStaffUserBto.StaffUserBto();
                deleteStaffUserBto.setId(staffUserBaseDto.getId());
                staffBOService.deleteStaffUser(deleteStaffUserBto);
            }
            // 再新建新关联
            CreateStaffUserBto.StaffUserBto staffUserBto = new CreateStaffUserBto.StaffUserBto();
            staffUserBto.setUserId(result);
            staffUserBto.setStaffId(mergeUserBto.getStaffId());
            var resultStaff = staffBOService.createStaffUser(staffUserBto);
            result += "-" + resultStaff;
        }
        return result;
    }

    /** 模糊查询会话 */
    @PublicInterface(id = "3b7e880e-2301-430e-ac3b-0425757765f0", version = "1744094678743")
    @AutoGenerated(locked = false, uuid = "3b7e880e-2301-430e-ac3b-0425757765f0")
    @RequestMapping(
            value = {"/api/user/search-session-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<SessionBaseVo> searchSessionPaged(@Valid SearchSessionQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<SessionBaseDto> dtoResult =
                sessionBaseDtoQueryService.searchSessionPaged(qto);
        VSQueryResult<SessionBaseVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(sessionBaseVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** merge账号 */
    @PublicInterface(id = "4fbaf931-2e5a-492f-8e51-e01d2c84b87d", version = "*************")
    @AutoGenerated(locked = false, uuid = "4fbaf931-2e5a-492f-8e51-e01d2c84b87d")
    @RequestMapping(
            value = {"/api/user/merge-account"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeAccount(@Valid MergeAccountBto mergeAccountBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = userInfoBOService.mergeAccount(mergeAccountBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 删除收藏 */
    @PublicInterface(id = "72052a23-6b60-4d71-877d-1bab0d63cb0f", version = "*************")
    @AutoGenerated(locked = false, uuid = "72052a23-6b60-4d71-877d-1bab0d63cb0f")
    @RequestMapping(
            value = {"/api/user/delete-preference"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String deletePreference(@Valid DeletePreferenceBto.UserPreferenceBto userPreferenceBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = userInfoBOService.deletePreference(userPreferenceBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 获取用户信息（带员工信息） */
    @PublicInterface(id = "738a2af0-3457-44dd-b689-77a692fac7f3", version = "*************")
    @AutoGenerated(locked = false, uuid = "738a2af0-3457-44dd-b689-77a692fac7f3")
    @RequestMapping(
            value = {"/api/user/list-user-staff-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<UserStaffVo> listUserStaffPaged(@Valid ListUserStaffQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<UserStaffDto> dtoResult = userStaffDtoQueryService.listUserStaffPaged(qto);
        VSQueryResult<UserStaffVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(userStaffVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 获取所有用户组 */
    @PublicInterface(id = "77bb6339-3836-43f7-bc60-a4a69724cd5e", version = "1743500286330")
    @AutoGenerated(locked = false, uuid = "77bb6339-3836-43f7-bc60-a4a69724cd5e")
    @RequestMapping(
            value = {"/api/user/list-user-group-all"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<UserGroupBaseVo> listUserGroupAll(@Valid ListUserGroupAllQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<UserGroupBaseDto> rpcResult =
                userGroupBaseDtoQueryService.queryUserGroupBaseDtoByListUserGroupAll(qto);
        List<UserGroupBaseVo> result =
                userGroupBaseVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 用户下拉框用 */
    @PublicInterface(id = "84330e52-0160-4fa9-a5c8-cc144e1939ef", version = "1746498112959")
    @AutoGenerated(locked = false, uuid = "84330e52-0160-4fa9-a5c8-cc144e1939ef")
    @RequestMapping(
            value = {"/api/user/user-staff-simple-waterfall"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<UserSimpleVo> userStaffSimpleWaterfall(@Valid UserStaffSimpleQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<UserSimpleDto> dtoResult =
                userSimpleDtoQueryService.userStaffSimpleWaterfall(qto);
        VSQueryResult<UserSimpleVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(userSimpleVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 修改账号密码 */
    @PublicInterface(id = "8edb9110-e636-4c4b-b75b-8b34f42c6ad4", version = "*************")
    @AutoGenerated(locked = false, uuid = "8edb9110-e636-4c4b-b75b-8b34f42c6ad4")
    @RequestMapping(
            value = {"/api/user/update-account"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String updateAccount(@Valid UpdateAccountBto.AccountBto accountBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = userInfoBOService.updateAccount(accountBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 模糊查询会话 */
    @PublicInterface(id = "9ba8ed7b-cdcb-4818-b20a-7610c45c2ab3", version = "*************")
    @AutoGenerated(locked = false, uuid = "9ba8ed7b-cdcb-4818-b20a-7610c45c2ab3")
    @RequestMapping(
            value = {"/api/user/list-session-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<SessionBaseVo> listSessionPaged(@Valid ListSessionQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<SessionBaseDto> dtoResult = sessionBaseDtoQueryService.listSessionPaged(qto);
        VSQueryResult<SessionBaseVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(sessionBaseVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 通过用户和类型获取偏好 */
    @PublicInterface(id = "a744c466-4db4-4325-b718-1380bdb70255", version = "1746669639560")
    @AutoGenerated(locked = false, uuid = "a744c466-4db4-4325-b718-1380bdb70255")
    @RequestMapping(
            value = {"/api/user/list-preference-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<UserPreferenceBaseVo> listPreferencePaged(@Valid ListPreferenceQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<UserPreferenceBaseDto> dtoResult =
                userPreferenceBaseDtoQueryService.listPreferencePaged(qto);
        VSQueryResult<UserPreferenceBaseVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(
                userPreferenceBaseVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 根据用户ID获取账号列表 */
    @PublicInterface(id = "bdd8ed1a-7cc3-4d16-a3a8-8c1fabd63e15", version = "*************")
    @AutoGenerated(locked = false, uuid = "bdd8ed1a-7cc3-4d16-a3a8-8c1fabd63e15")
    @RequestMapping(
            value = {"/api/user/get-by-user-id"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<AccountVo> accountByUserId(String userId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<AccountDto> rpcResult = accountDtoService.getByUserId(userId);
        List<AccountVo> result = accountVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 新增用户 */
    @PublicInterface(id = "d7367e68-1ac2-4bf2-acb4-57de613b0107", version = "*************")
    @AutoGenerated(locked = false, uuid = "d7367e68-1ac2-4bf2-acb4-57de613b0107")
    @RequestMapping(
            value = {"/api/user/create-user"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String createUser(@Valid CreateUserBto createUserBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = userInfoBOService.createUser(createUserBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        if (!createUserBto.getStaffId().isEmpty() && createUserBto.getStaffId() != null) {
            CreateStaffUserBto.StaffUserBto staffUserBto = new CreateStaffUserBto.StaffUserBto();
            staffUserBto.setUserId(result);
            staffUserBto.setStaffId(createUserBto.getStaffId());
            var resultStaff = staffBOService.createStaffUser(staffUserBto);
            result += "-" + resultStaff;
        }
        return result;
    }

    /** 新增收藏 */
    @PublicInterface(id = "de225b83-ef5d-47c0-8b19-b5b370083cb3", version = "1746608869455")
    @AutoGenerated(locked = false, uuid = "de225b83-ef5d-47c0-8b19-b5b370083cb3")
    @RequestMapping(
            value = {"/api/user/create-preference"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String createPreference(@Valid CreatePreferenceBto.UserPreferenceBto userPreferenceBto) {
        SearchPreferenceQto qto = new SearchPreferenceQto();
        qto.setUserIdIs(userPreferenceBto.getUserId());
        qto.setFavoriteIdIs(userPreferenceBto.getFavoriteId());
        qto.setFavoriteTypeIs(userPreferenceBto.getFavoriteType());
        var retSearch = userPreferenceBaseDtoQueryService.searchPreference(qto);
        if (retSearch.size() > 0) {
            return "该收藏已存在，不能重复添加";
        }
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = userInfoBOService.createPreference(userPreferenceBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 创建账号 */
    @PublicInterface(id = "f2f37c78-a759-49ff-ae2b-8255d228f3cb", version = "*************")
    @AutoGenerated(locked = false, uuid = "f2f37c78-a759-49ff-ae2b-8255d228f3cb")
    @RequestMapping(
            value = {"/api/user/create-account"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String createAccount(@Valid CreateAccountBto.AccountBto accountBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = userInfoBOService.createAccount(accountBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 创建会话 */
    @PublicInterface(id = "fc03988e-b3bc-484d-8b9e-cb64dca93947", version = "*************")
    @AutoGenerated(locked = false, uuid = "fc03988e-b3bc-484d-8b9e-cb64dca93947")
    @RequestMapping(
            value = {"/api/user/create-session"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String createSession(@Valid CreateSessionBto createSessionBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = userSessionBOService.createSession(createSessionBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }
}
