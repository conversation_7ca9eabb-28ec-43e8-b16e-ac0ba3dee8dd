package com.pulse.user.entrance.web.query.assembler;

import com.pulse.user.entrance.web.vo.UserGroupRelationBaseVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** UserGroupRelationBaseVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "dda315e9-c5a2-3716-8d5c-4e4dbf9b1f77")
public class UserGroupRelationBaseVoDataAssembler {

    /** 批量自定义组装UserGroupRelationBaseVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "aadf8e96-d966-3f19-b8a2-02aa4900c8f5")
    public void assembleDataCustomized(List<UserGroupRelationBaseVo> dataList) {
        // 自定义数据组装

    }

    /** 组装UserGroupRelationBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "d1c50c05-b618-33ee-a66d-d3842401874e")
    public void assembleData(Map<String, UserGroupRelationBaseVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }
}
