package com.pulse.user.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.user.entrance.web.query.assembler.AccountVoDataAssembler;
import com.pulse.user.entrance.web.query.assembler.AccountVoDataAssembler.AccountVoDataHolder;
import com.pulse.user.entrance.web.query.collector.AccountVoDataCollector;
import com.pulse.user.entrance.web.vo.AccountVo;
import com.pulse.user.entrance.web.vo.UserBaseVo;
import com.pulse.user.manager.dto.AccountDto;
import com.pulse.user.manager.dto.UserBaseDto;
import com.pulse.user.service.AccountBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到AccountVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "bd133fd3-119c-4699-b4d2-1f6d4497b4e6|VO|CONVERTER")
public class AccountVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private AccountBaseDtoService accountBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private AccountVoDataAssembler accountVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private AccountVoDataCollector accountVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private UserBaseVoConverter userBaseVoConverter;

    /** 把AccountDto转换成AccountVo */
    @AutoGenerated(locked = true, uuid = "104b1cf9-e3c9-3c05-8c89-67ab639d4f94")
    public AccountVo convertToAccountVo(AccountDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToAccountVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装AccountVo数据 */
    @AutoGenerated(locked = true, uuid = "624b338a-b24b-3783-acb5-ca8405887402")
    public AccountVo convertAndAssembleData(AccountDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装AccountVo列表数据 */
    @AutoGenerated(locked = true, uuid = "6269ae16-bfb2-3c08-8d46-c128c274b010")
    public List<AccountVo> convertAndAssembleDataList(List<AccountDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        AccountVoDataHolder dataHolder = new AccountVoDataHolder();
        dataHolder.setRootBaseDtoList(
                accountBaseDtoService.getByIds(
                        dtoList.stream().map(AccountDto::getId).collect(Collectors.toList())));
        Map<String, AccountVo> voMap =
                convertToAccountVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        accountVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        accountVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把AccountDto转换成AccountVo */
    @AutoGenerated(locked = false, uuid = "bd133fd3-119c-4699-b4d2-1f6d4497b4e6-converter-Map")
    public Map<AccountDto, AccountVo> convertToAccountVoMap(List<AccountDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<UserBaseDto, UserBaseVo> userMap =
                userBaseVoConverter.convertToUserBaseVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(AccountDto::getUser)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<AccountDto, AccountVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            AccountVo vo = new AccountVo();
                                            vo.setId(dto.getId());
                                            vo.setUser(
                                                    dto.getUser() == null
                                                            ? null
                                                            : userMap.get(dto.getUser()));
                                            vo.setAccessToken(dto.getAccessToken());
                                            vo.setAccessTokenValidPeriod(
                                                    dto.getAccessTokenValidPeriod());
                                            vo.setAccountName(dto.getAccountName());
                                            vo.setAuthenticationFeatureData(
                                                    dto.getAuthenticationFeatureData());
                                            vo.setAuthenticationFeatureDataSalt(
                                                    dto.getAuthenticationFeatureDataSalt());
                                            vo.setCellphone(dto.getCellphone());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setEncryptionPassword(dto.getEncryptionPassword());
                                            vo.setLastLoginFailureTime(
                                                    dto.getLastLoginFailureTime());
                                            vo.setLastLoginTime(dto.getLastLoginTime());
                                            vo.setLoginFailureCount(dto.getLoginFailureCount());
                                            vo.setLoginType(dto.getLoginType());
                                            vo.setPasswordSalt(dto.getPasswordSalt());
                                            vo.setRefreshToken(dto.getRefreshToken());
                                            vo.setStatus(dto.getStatus());
                                            vo.setThirdPartyAccountId(dto.getThirdPartyAccountId());
                                            vo.setThirdPartyPlatformType(
                                                    dto.getThirdPartyPlatformType());
                                            vo.setTokenType(dto.getTokenType());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把AccountDto转换成AccountVo */
    @AutoGenerated(locked = true, uuid = "bd133fd3-119c-4699-b4d2-1f6d4497b4e6-converter-list")
    public List<AccountVo> convertToAccountVoList(List<AccountDto> dtoList) {
        return new ArrayList<>(convertToAccountVoMap(dtoList).values());
    }
}
