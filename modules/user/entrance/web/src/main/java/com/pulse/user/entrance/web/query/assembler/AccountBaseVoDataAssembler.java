package com.pulse.user.entrance.web.query.assembler;

import com.pulse.user.entrance.web.vo.AccountBaseVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** AccountBaseVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "e2b409e8-7108-37f7-a7c7-11cacb32f663")
public class AccountBaseVoDataAssembler {

    /** 组装AccountBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "26ad4be8-94af-3717-ad31-3d6bfae4c07f")
    public void assembleData(Map<String, AccountBaseVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装AccountBaseVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "d7db807d-e678-33d5-a418-402820583fc4")
    public void assembleDataCustomized(List<AccountBaseVo> dataList) {
        // 自定义数据组装

    }
}
