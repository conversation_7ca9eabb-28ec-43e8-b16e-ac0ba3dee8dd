package com.pulse.user.entrance.web.query.assembler;

import com.pulse.user.entrance.web.vo.UserListAccountVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** UserListAccountVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "0fd4542c-3386-33a5-a46a-4756df1ede0f")
public class UserListAccountVoDataAssembler {

    /** 组装UserListAccountVo数据 */
    @AutoGenerated(locked = true, uuid = "06e8f0e7-439c-3b81-be23-e527cff7c13f")
    public void assembleData(Map<String, UserListAccountVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装UserListAccountVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "241d4ca3-222b-3bb3-b824-d7e39663dc3f")
    public void assembleDataCustomized(List<UserListAccountVo> dataList) {
        // 自定义数据组装

    }
}
