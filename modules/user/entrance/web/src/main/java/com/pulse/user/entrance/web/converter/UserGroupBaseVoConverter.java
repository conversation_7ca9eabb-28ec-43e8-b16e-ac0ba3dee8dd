package com.pulse.user.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.user.entrance.web.query.assembler.UserGroupBaseVoDataAssembler;
import com.pulse.user.entrance.web.vo.UserGroupBaseVo;
import com.pulse.user.manager.dto.UserGroupBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到UserGroupBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "65450335-a9d5-415e-b2c2-e0c1c90614b8|VO|CONVERTER")
public class UserGroupBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private UserGroupBaseVoDataAssembler userGroupBaseVoDataAssembler;

    /** 把UserGroupBaseDto转换成UserGroupBaseVo */
    @AutoGenerated(locked = true, uuid = "2c7b8338-5d77-37da-ae22-c94f50840372")
    public UserGroupBaseVo convertToUserGroupBaseVo(UserGroupBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToUserGroupBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把UserGroupBaseDto转换成UserGroupBaseVo */
    @AutoGenerated(locked = false, uuid = "65450335-a9d5-415e-b2c2-e0c1c90614b8-converter-Map")
    public Map<UserGroupBaseDto, UserGroupBaseVo> convertToUserGroupBaseVoMap(
            List<UserGroupBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<UserGroupBaseDto, UserGroupBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            UserGroupBaseVo vo = new UserGroupBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setName(dto.getName());
                                            vo.setStatus(dto.getStatus());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把UserGroupBaseDto转换成UserGroupBaseVo */
    @AutoGenerated(locked = true, uuid = "65450335-a9d5-415e-b2c2-e0c1c90614b8-converter-list")
    public List<UserGroupBaseVo> convertToUserGroupBaseVoList(List<UserGroupBaseDto> dtoList) {
        return new ArrayList<>(convertToUserGroupBaseVoMap(dtoList).values());
    }

    /** 使用默认方式组装UserGroupBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "8db06197-9d20-3711-93ca-bc4c5252b332")
    public UserGroupBaseVo convertAndAssembleData(UserGroupBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装UserGroupBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "bf5cec1a-93fc-3d51-813e-ac843cb2bcfe")
    public List<UserGroupBaseVo> convertAndAssembleDataList(List<UserGroupBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, UserGroupBaseVo> voMap =
                convertToUserGroupBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        userGroupBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
