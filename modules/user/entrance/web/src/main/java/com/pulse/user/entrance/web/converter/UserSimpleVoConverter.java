package com.pulse.user.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.manager.dto.StaffBaseDto;
import com.pulse.organization.manager.dto.StaffUserWithStaffDto;
import com.pulse.user.entrance.web.query.assembler.UserSimpleVoDataAssembler;
import com.pulse.user.entrance.web.query.assembler.UserSimpleVoDataAssembler.UserSimpleVoDataHolder;
import com.pulse.user.entrance.web.query.collector.UserSimpleVoDataCollector;
import com.pulse.user.entrance.web.vo.UserSimpleVo;
import com.pulse.user.entrance.web.vo.UserSimpleVo.StaffBaseVo;
import com.pulse.user.entrance.web.vo.UserSimpleVo.StaffUserWithStaffVo;
import com.pulse.user.manager.dto.UserSimpleDto;
import com.pulse.user.service.UserBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到UserSimpleVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "174b8fb2-5783-42f4-9986-af2c4f9b39ba|VO|CONVERTER")
public class UserSimpleVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private UserBaseDtoService userBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private UserSimpleVoDataAssembler userSimpleVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private UserSimpleVoDataCollector userSimpleVoDataCollector;

    /** 把UserSimpleDto转换成UserSimpleVo */
    @AutoGenerated(locked = false, uuid = "174b8fb2-5783-42f4-9986-af2c4f9b39ba-converter-Map")
    public Map<UserSimpleDto, UserSimpleVo> convertToUserSimpleVoMap(List<UserSimpleDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<StaffUserWithStaffDto, StaffUserWithStaffVo> staffUserWithStaffMap =
                convertToStaffUserWithStaffVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(UserSimpleDto::getStaffUserWithStaff)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<UserSimpleDto, UserSimpleVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            UserSimpleVo vo = new UserSimpleVo();
                                            vo.setId(dto.getId());
                                            vo.setGender(dto.getGender());
                                            vo.setNames(dto.getNames());
                                            vo.setStatus(dto.getStatus());
                                            vo.setUserName(dto.getUserName());
                                            vo.setStaffUserWithStaff(
                                                    dto.getStaffUserWithStaff() == null
                                                            ? null
                                                            : staffUserWithStaffMap.get(
                                                                    dto.getStaffUserWithStaff()));
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把UserSimpleDto转换成UserSimpleVo */
    @AutoGenerated(locked = true, uuid = "174b8fb2-5783-42f4-9986-af2c4f9b39ba-converter-list")
    public List<UserSimpleVo> convertToUserSimpleVoList(List<UserSimpleDto> dtoList) {
        return new ArrayList<>(convertToUserSimpleVoMap(dtoList).values());
    }

    /** 把StaffUserWithStaffDto转换成StaffUserWithStaffVo */
    @AutoGenerated(locked = false, uuid = "379d0fa1-aaf7-4741-8dd1-aa7b3466ced5-converter-Map")
    public Map<StaffUserWithStaffDto, UserSimpleVo.StaffUserWithStaffVo>
            convertToStaffUserWithStaffVoMap(List<StaffUserWithStaffDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<StaffBaseDto, StaffBaseVo> staffMap =
                convertToStaffBaseVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(StaffUserWithStaffDto::getStaff)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<StaffUserWithStaffDto, StaffUserWithStaffVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            StaffUserWithStaffVo vo = new StaffUserWithStaffVo();
                                            vo.setId(dto.getId());
                                            vo.setStaff(
                                                    dto.getStaff() == null
                                                            ? null
                                                            : staffMap.get(dto.getStaff()));
                                            vo.setUserId(dto.getUserId());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把StaffUserWithStaffDto转换成StaffUserWithStaffVo */
    @AutoGenerated(locked = true, uuid = "379d0fa1-aaf7-4741-8dd1-aa7b3466ced5-converter-list")
    public List<UserSimpleVo.StaffUserWithStaffVo> convertToStaffUserWithStaffVoList(
            List<StaffUserWithStaffDto> dtoList) {
        return new ArrayList<>(convertToStaffUserWithStaffVoMap(dtoList).values());
    }

    /** 把StaffBaseDto转换成StaffBaseVo */
    @AutoGenerated(locked = true, uuid = "385cfeef-c69f-3ae8-9c42-8a9428794ec3")
    public UserSimpleVo.StaffBaseVo convertToStaffBaseVo(StaffBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToStaffBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装UserSimpleVo列表数据 */
    @AutoGenerated(locked = true, uuid = "5378c6f9-5c0e-33f0-841f-367fc5afb0e6")
    public List<UserSimpleVo> convertAndAssembleDataList(List<UserSimpleDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        UserSimpleVoDataHolder dataHolder = new UserSimpleVoDataHolder();
        dataHolder.setRootBaseDtoList(
                userBaseDtoService.getByIds(
                        dtoList.stream().map(UserSimpleDto::getId).collect(Collectors.toList())));
        Map<String, UserSimpleVo> voMap =
                convertToUserSimpleVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        userSimpleVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        userSimpleVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把StaffUserWithStaffDto转换成StaffUserWithStaffVo */
    @AutoGenerated(locked = true, uuid = "5537be0a-1356-3867-a2aa-069a77522a14")
    public UserSimpleVo.StaffUserWithStaffVo convertToStaffUserWithStaffVo(
            StaffUserWithStaffDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToStaffUserWithStaffVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把StaffBaseDto转换成StaffBaseVo */
    @AutoGenerated(locked = false, uuid = "57bc55b4-02a1-4c7b-8229-b0109edc42f6-converter-Map")
    public Map<StaffBaseDto, UserSimpleVo.StaffBaseVo> convertToStaffBaseVoMap(
            List<StaffBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<StaffBaseDto, StaffBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            StaffBaseVo vo = new StaffBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setCertificateNumber(dto.getCertificateNumber());
                                            vo.setInputCode(dto.getInputCode());
                                            vo.setName(dto.getName());
                                            vo.setStaffNumber(dto.getStaffNumber());
                                            vo.setStatus(dto.getStatus());
                                            vo.setSortNumber(dto.getSortNumber());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把StaffBaseDto转换成StaffBaseVo */
    @AutoGenerated(locked = true, uuid = "57bc55b4-02a1-4c7b-8229-b0109edc42f6-converter-list")
    public List<UserSimpleVo.StaffBaseVo> convertToStaffBaseVoList(List<StaffBaseDto> dtoList) {
        return new ArrayList<>(convertToStaffBaseVoMap(dtoList).values());
    }

    /** 使用默认方式组装UserSimpleVo数据 */
    @AutoGenerated(locked = true, uuid = "65f6c037-7c40-3951-9828-97a985aee6e9")
    public UserSimpleVo convertAndAssembleData(UserSimpleDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把UserSimpleDto转换成UserSimpleVo */
    @AutoGenerated(locked = true, uuid = "d5c13e46-5327-3eb4-b6c7-b9000d13455c")
    public UserSimpleVo convertToUserSimpleVo(UserSimpleDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToUserSimpleVoList(List.of(dto)).stream().findAny().orElse(null);
    }
}
