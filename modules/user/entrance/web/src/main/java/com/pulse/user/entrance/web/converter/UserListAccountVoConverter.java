package com.pulse.user.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.user.entrance.web.query.assembler.UserListAccountVoDataAssembler;
import com.pulse.user.entrance.web.vo.AccountBaseVo;
import com.pulse.user.entrance.web.vo.UserListAccountVo;
import com.pulse.user.manager.dto.AccountBaseDto;
import com.pulse.user.manager.dto.UserListAccountDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到UserListAccountVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "89c84766-ce2a-4c9d-a507-c38cae71a259|VO|CONVERTER")
public class UserListAccountVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private AccountBaseVoConverter accountBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private UserListAccountVoDataAssembler userListAccountVoDataAssembler;

    /** 使用默认方式组装UserListAccountVo列表数据 */
    @AutoGenerated(locked = true, uuid = "407ff616-f0b0-3718-bce7-936794b4f886")
    public List<UserListAccountVo> convertAndAssembleDataList(List<UserListAccountDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, UserListAccountVo> voMap =
                convertToUserListAccountVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        userListAccountVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 使用默认方式组装UserListAccountVo数据 */
    @AutoGenerated(locked = true, uuid = "632841e7-aefe-3c49-8bda-1d5a8af6277e")
    public UserListAccountVo convertAndAssembleData(UserListAccountDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把UserListAccountDto转换成UserListAccountVo */
    @AutoGenerated(locked = false, uuid = "89c84766-ce2a-4c9d-a507-c38cae71a259-converter-Map")
    public Map<UserListAccountDto, UserListAccountVo> convertToUserListAccountVoMap(
            List<UserListAccountDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<AccountBaseDto, AccountBaseVo> listAccountMap =
                accountBaseVoConverter.convertToAccountBaseVoMap(
                        dtoList.stream()
                                .filter(dto -> CollectionUtil.isNotEmpty(dto.getListAccountList()))
                                .flatMap(dto -> dto.getListAccountList().stream())
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<UserListAccountDto, UserListAccountVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            UserListAccountVo vo = new UserListAccountVo();
                                            vo.setId(dto.getId());
                                            vo.setAddress(dto.getAddress());
                                            vo.setBirthDay(dto.getBirthDay());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setGender(dto.getGender());
                                            vo.setNames(dto.getNames());
                                            vo.setStatus(dto.getStatus());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setListAccount(
                                                    dto.getListAccountList() == null
                                                            ? null
                                                            : dto.getListAccountList().stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    listAccountMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            vo.setAge(dto.getAge());
                                            vo.setPhoneNumber(dto.getPhoneNumber());
                                            vo.setUserExpirationStart(dto.getUserExpirationStart());
                                            vo.setUserExpirationEnd(dto.getUserExpirationEnd());
                                            vo.setUserName(dto.getUserName());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把UserListAccountDto转换成UserListAccountVo */
    @AutoGenerated(locked = true, uuid = "89c84766-ce2a-4c9d-a507-c38cae71a259-converter-list")
    public List<UserListAccountVo> convertToUserListAccountVoList(
            List<UserListAccountDto> dtoList) {
        return new ArrayList<>(convertToUserListAccountVoMap(dtoList).values());
    }

    /** 把UserListAccountDto转换成UserListAccountVo */
    @AutoGenerated(locked = true, uuid = "d290966d-accb-3153-b190-4d7b5fe7a817")
    public UserListAccountVo convertToUserListAccountVo(UserListAccountDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToUserListAccountVoList(List.of(dto)).stream().findAny().orElse(null);
    }
}
