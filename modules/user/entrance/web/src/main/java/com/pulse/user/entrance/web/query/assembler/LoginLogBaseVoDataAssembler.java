package com.pulse.user.entrance.web.query.assembler;

import com.pulse.user.entrance.web.vo.LoginLogBaseVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** LoginLogBaseVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "1b804ab8-abe1-3fe6-a931-3d4aa72164bc")
public class LoginLogBaseVoDataAssembler {

    /** 批量自定义组装LoginLogBaseVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "4a3b0760-7a67-35e2-ac12-da33ea6d0256")
    public void assembleDataCustomized(List<LoginLogBaseVo> dataList) {
        // 自定义数据组装

    }

    /** 组装LoginLogBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "ae634340-4852-3b40-bc30-dc8090bf8487")
    public void assembleData(Map<String, LoginLogBaseVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }
}
