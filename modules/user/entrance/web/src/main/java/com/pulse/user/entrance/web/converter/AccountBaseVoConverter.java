package com.pulse.user.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.user.entrance.web.query.assembler.AccountBaseVoDataAssembler;
import com.pulse.user.entrance.web.vo.AccountBaseVo;
import com.pulse.user.manager.dto.AccountBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到AccountBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "9fbaf85d-c102-467c-a5f7-717e43efbf34|VO|CONVERTER")
public class AccountBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private AccountBaseVoDataAssembler accountBaseVoDataAssembler;

    /** 使用默认方式组装AccountBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "0935a886-11cb-3099-ae6e-216bae288db5")
    public List<AccountBaseVo> convertAndAssembleDataList(List<AccountBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, AccountBaseVo> voMap =
                convertToAccountBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        accountBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 使用默认方式组装AccountBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "255546f0-9773-39d4-9a71-6be6db5cc768")
    public AccountBaseVo convertAndAssembleData(AccountBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把AccountBaseDto转换成AccountBaseVo */
    @AutoGenerated(locked = false, uuid = "9fbaf85d-c102-467c-a5f7-717e43efbf34-converter-Map")
    public Map<AccountBaseDto, AccountBaseVo> convertToAccountBaseVoMap(
            List<AccountBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<AccountBaseDto, AccountBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            AccountBaseVo vo = new AccountBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setUserId(dto.getUserId());
                                            vo.setAccessToken(dto.getAccessToken());
                                            vo.setAccessTokenValidPeriod(
                                                    dto.getAccessTokenValidPeriod());
                                            vo.setAccountName(dto.getAccountName());
                                            vo.setAuthenticationFeatureData(
                                                    dto.getAuthenticationFeatureData());
                                            vo.setAuthenticationFeatureDataSalt(
                                                    dto.getAuthenticationFeatureDataSalt());
                                            vo.setCellphone(dto.getCellphone());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setEncryptionPassword(dto.getEncryptionPassword());
                                            vo.setLastLoginFailureTime(
                                                    dto.getLastLoginFailureTime());
                                            vo.setLastLoginTime(dto.getLastLoginTime());
                                            vo.setLoginFailureCount(dto.getLoginFailureCount());
                                            vo.setLoginType(dto.getLoginType());
                                            vo.setPasswordSalt(dto.getPasswordSalt());
                                            vo.setRefreshToken(dto.getRefreshToken());
                                            vo.setStatus(dto.getStatus());
                                            vo.setThirdPartyAccountId(dto.getThirdPartyAccountId());
                                            vo.setThirdPartyPlatformType(
                                                    dto.getThirdPartyPlatformType());
                                            vo.setTokenType(dto.getTokenType());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把AccountBaseDto转换成AccountBaseVo */
    @AutoGenerated(locked = true, uuid = "9fbaf85d-c102-467c-a5f7-717e43efbf34-converter-list")
    public List<AccountBaseVo> convertToAccountBaseVoList(List<AccountBaseDto> dtoList) {
        return new ArrayList<>(convertToAccountBaseVoMap(dtoList).values());
    }

    /** 把AccountBaseDto转换成AccountBaseVo */
    @AutoGenerated(locked = true, uuid = "eb68b907-ad9d-3d48-b4aa-0ee2a150c934")
    public AccountBaseVo convertToAccountBaseVo(AccountBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToAccountBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }
}
