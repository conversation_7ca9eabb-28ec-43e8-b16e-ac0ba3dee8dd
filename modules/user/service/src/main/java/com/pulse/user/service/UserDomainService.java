package com.pulse.user.service;

import com.vs.code.AutoGenerated;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/** 模块领域服务，负责模块的BoService的包装 */
@Service
@Slf4j
@AutoGenerated(locked = false, uuid = "11de64c6-24e2-4026-82a7-cb4fe1e0a1af")
public class UserDomainService {
    @AutoGenerated(locked = true)
    @Resource
    private LoginLogBOService loginLogBOService;

    @AutoGenerated(locked = true)
    @Resource
    private UserGroupBOService userGroupBOService;

    @AutoGenerated(locked = true)
    @Resource
    private UserInfoBOService userInfoBOService;

    @AutoGenerated(locked = true)
    @Resource
    private UserSessionBOService userSessionBOService;
}
