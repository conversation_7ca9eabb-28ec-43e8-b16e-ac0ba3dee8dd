package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.ExamTypeDocumentTemplateVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** ExamTypeDocumentTemplateVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "4f1905b9-de23-3637-9986-af71eb392cd4")
public class ExamTypeDocumentTemplateVoDataAssembler {

    /** 组装ExamTypeDocumentTemplateVo数据 */
    @AutoGenerated(locked = true, uuid = "48593317-c678-3204-b7fd-0de4963cd8bb")
    public void assembleData(Map<String, ExamTypeDocumentTemplateVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装ExamTypeDocumentTemplateVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "a15ead99-0b85-343b-8fcb-c22c7491aee2")
    public void assembleDataCustomized(List<ExamTypeDocumentTemplateVo> dataList) {
        // 自定义数据组装

    }
}
