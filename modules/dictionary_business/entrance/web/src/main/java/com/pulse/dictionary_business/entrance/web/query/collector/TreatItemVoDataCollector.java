package com.pulse.dictionary_business.entrance.web.query.collector;

import com.pulse.dictionary_business.entrance.web.converter.TreatClassDictionaryVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.TreatItemVoConverter;
import com.pulse.dictionary_business.entrance.web.query.assembler.TreatItemVoDataAssembler.TreatItemVoDataHolder;
import com.pulse.dictionary_business.entrance.web.vo.TreatClassDictionaryVo;
import com.pulse.dictionary_business.manager.dto.TreatClassDictionaryBaseDto;
import com.pulse.dictionary_business.manager.dto.TreatItemBaseDto;
import com.pulse.dictionary_business.service.TreatClassDictionaryBaseDtoService;
import com.pulse.dictionary_business.service.TreatItemBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装TreatItemVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "064ca34b-0d50-391f-82c1-bacc15ed4258")
public class TreatItemVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private TreatClassDictionaryBaseDtoService treatClassDictionaryBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TreatClassDictionaryVoConverter treatClassDictionaryVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TreatItemBaseDtoService treatItemBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TreatItemVoConverter treatItemVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TreatItemVoDataCollector treatItemVoDataCollector;

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "11d1dbb2-b723-392f-b967-2bd357a3deea")
    public void collectDataDefault(TreatItemVoDataHolder dataHolder) {
        treatItemVoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 获取TreatItemBaseDto数据填充TreatItemVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "34a1f0f3-0d4a-3add-b4a7-7d2e23a57637")
    public void collectDataWithDtoData(
            List<TreatItemBaseDto> dtoList, TreatItemVoDataHolder dataHolder) {

        fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "a255eb2e-f084-3a40-b913-7f558fb0f571")
    private void fillDataWhenNecessary(TreatItemVoDataHolder dataHolder) {
        List<TreatItemBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.treatClass == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(TreatItemBaseDto::getTreatClassId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<TreatClassDictionaryBaseDto> baseDtoList =
                    treatClassDictionaryBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(TreatClassDictionaryBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, TreatClassDictionaryBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            TreatClassDictionaryBaseDto::getId,
                                            Function.identity()));
            Map<TreatClassDictionaryBaseDto, TreatClassDictionaryVo> dtoVoMap =
                    treatClassDictionaryVoConverter.convertToTreatClassDictionaryVoMap(baseDtoList);
            Map<TreatClassDictionaryBaseDto, TreatClassDictionaryVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.treatClass =
                    rootDtoList.stream()
                            .map(TreatItemBaseDto::getTreatClassId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }
}
