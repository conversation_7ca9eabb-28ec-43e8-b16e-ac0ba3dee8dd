package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.ClinicItemChargeItemBaseVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** ClinicItemChargeItemBaseVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "fc01af8a-382e-3610-bf6c-6f05b58fbf78")
public class ClinicItemChargeItemBaseVoDataAssembler {

    /** 组装ClinicItemChargeItemBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "4dfdb473-7321-3ef3-bfb0-79b8633c97f2")
    public void assembleData(Map<String, ClinicItemChargeItemBaseVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装ClinicItemChargeItemBaseVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "8661f7e1-0e68-334a-bcc3-00ee472e8047")
    public void assembleDataCustomized(List<ClinicItemChargeItemBaseVo> dataList) {
        // 自定义数据组装

    }
}
