package com.pulse.dictionary_business.entrance.web.query.collector;

import com.pulse.dictionary_business.entrance.web.converter.CommonUseDiagnosisExtVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.DiagnosisDictionaryVoConverter;
import com.pulse.dictionary_business.entrance.web.query.assembler.CommonUseDiagnosisExtVoDataAssembler.CommonUseDiagnosisExtVoDataHolder;
import com.pulse.dictionary_business.entrance.web.vo.DiagnosisDictionaryVo;
import com.pulse.dictionary_business.manager.dto.CommonUseDiagnosisBaseDto;
import com.pulse.dictionary_business.manager.dto.CommonUseDiagnosisExtDto;
import com.pulse.dictionary_business.manager.dto.DiagnosisDictionaryBaseDto;
import com.pulse.dictionary_business.service.CommonUseDiagnosisBaseDtoService;
import com.pulse.dictionary_business.service.DiagnosisDictionaryBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装CommonUseDiagnosisExtVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "d220ff01-f291-3c0c-a91d-ce8ac9ff79fa")
public class CommonUseDiagnosisExtVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private CommonUseDiagnosisBaseDtoService commonUseDiagnosisBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private CommonUseDiagnosisExtVoConverter commonUseDiagnosisExtVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private CommonUseDiagnosisExtVoDataCollector commonUseDiagnosisExtVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private DiagnosisDictionaryBaseDtoService diagnosisDictionaryBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DiagnosisDictionaryVoConverter diagnosisDictionaryVoConverter;

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "13893998-39c4-3bbb-8822-867b75045a7e")
    public void collectDataDefault(CommonUseDiagnosisExtVoDataHolder dataHolder) {
        commonUseDiagnosisExtVoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "2b530f8d-d736-3222-8b92-e02762ec76ed")
    private void fillDataWhenNecessary(CommonUseDiagnosisExtVoDataHolder dataHolder) {
        List<CommonUseDiagnosisBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.diagnosis == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(CommonUseDiagnosisBaseDto::getDiagnosisId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DiagnosisDictionaryBaseDto> baseDtoList =
                    diagnosisDictionaryBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(DiagnosisDictionaryBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, DiagnosisDictionaryBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            DiagnosisDictionaryBaseDto::getId,
                                            Function.identity()));
            Map<DiagnosisDictionaryBaseDto, DiagnosisDictionaryVo> dtoVoMap =
                    diagnosisDictionaryVoConverter.convertToDiagnosisDictionaryVoMap(baseDtoList);
            Map<DiagnosisDictionaryBaseDto, DiagnosisDictionaryVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.diagnosis =
                    rootDtoList.stream()
                            .map(CommonUseDiagnosisBaseDto::getDiagnosisId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** 获取CommonUseDiagnosisExtDto数据填充CommonUseDiagnosisExtVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "660db4dc-3abb-3419-ab2c-fa53eab9a722")
    public void collectDataWithDtoData(
            List<CommonUseDiagnosisExtDto> dtoList, CommonUseDiagnosisExtVoDataHolder dataHolder) {
        List<DiagnosisDictionaryBaseDto> diagnosisList = new ArrayList<>();

        for (CommonUseDiagnosisExtDto rootDto : dtoList) {
            DiagnosisDictionaryBaseDto diagnosisDto = rootDto.getDiagnosis();
            if (diagnosisDto != null) {
                diagnosisList.add(diagnosisDto);
            }
        }

        // access diagnosis
        Map<DiagnosisDictionaryBaseDto, DiagnosisDictionaryVo> diagnosisVoMap =
                diagnosisDictionaryVoConverter.convertToDiagnosisDictionaryVoMap(diagnosisList);
        dataHolder.diagnosis =
                diagnosisList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> diagnosisVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }
}
