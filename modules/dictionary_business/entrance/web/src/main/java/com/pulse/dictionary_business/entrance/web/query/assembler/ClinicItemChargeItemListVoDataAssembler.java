package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.billing_public_config.manager.dto.ChargeItemBaseDto;
import com.pulse.dictionary_business.entrance.web.vo.ChargeItemClinicItemVo;
import com.pulse.dictionary_business.entrance.web.vo.ClinicItemChargeItemListVo;
import com.pulse.dictionary_business.manager.dto.ClinicItemChargeItemBaseDto;
import com.pulse.dictionary_business.service.ClinicItemChargeItemBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** ClinicItemChargeItemListVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "4e2adddc-0edf-35c5-ad19-7382cc198161")
public class ClinicItemChargeItemListVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemChargeItemBaseDtoService clinicItemChargeItemBaseDtoService;

    /** 组装ClinicItemChargeItemListVo数据 */
    @AutoGenerated(locked = true, uuid = "7b0f5d2d-c9d1-3c32-a5f0-49bff51d33ae")
    public void assembleData(
            Map<String, ClinicItemChargeItemListVo> voMap,
            ClinicItemChargeItemListVoDataAssembler.ClinicItemChargeItemListVoDataHolder
                    dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<ClinicItemChargeItemBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, Pair<ChargeItemBaseDto, ChargeItemClinicItemVo>> chargeItem =
                dataHolder.chargeItem.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getItemCode(),
                                        dto -> Pair.of(dto, dataHolder.chargeItem.get(dto)),
                                        (o1, o2) -> o1));

        for (ClinicItemChargeItemBaseDto baseDto : baseDtoList) {
            ClinicItemChargeItemListVo vo = voMap.get(baseDto.getId());
            vo.setChargeItem(
                    Optional.ofNullable(chargeItem.get(baseDto.getChargeItemId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装ClinicItemChargeItemListVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "cdc50e42-da94-3ef4-99d9-cae5f4e144cf")
    public void assembleDataCustomized(List<ClinicItemChargeItemListVo> dataList) {
        // 自定义数据组装

    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class ClinicItemChargeItemListVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<ClinicItemChargeItemBaseDto> rootBaseDtoList;

        /** 持有字段chargeItem的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<ChargeItemBaseDto, ChargeItemClinicItemVo> chargeItem;
    }
}
