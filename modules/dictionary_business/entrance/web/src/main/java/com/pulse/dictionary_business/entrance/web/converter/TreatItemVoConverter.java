package com.pulse.dictionary_business.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.entrance.web.query.assembler.TreatItemVoDataAssembler;
import com.pulse.dictionary_business.entrance.web.query.assembler.TreatItemVoDataAssembler.TreatItemVoDataHolder;
import com.pulse.dictionary_business.entrance.web.query.collector.TreatItemVoDataCollector;
import com.pulse.dictionary_business.entrance.web.vo.TreatItemVo;
import com.pulse.dictionary_business.manager.dto.TreatItemBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到TreatItemVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "055f8951-4fe5-4e06-b153-11df4a68e338|VO|CONVERTER")
public class TreatItemVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private TreatItemVoDataAssembler treatItemVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private TreatItemVoDataCollector treatItemVoDataCollector;

    /** 把TreatItemBaseDto转换成TreatItemVo */
    @AutoGenerated(locked = false, uuid = "055f8951-4fe5-4e06-b153-11df4a68e338-converter-Map")
    public Map<TreatItemBaseDto, TreatItemVo> convertToTreatItemVoMap(
            List<TreatItemBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<TreatItemBaseDto, TreatItemVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            TreatItemVo vo = new TreatItemVo();
                                            vo.setId(dto.getId());
                                            vo.setTreatWayId(dto.getTreatWayId());
                                            vo.setHighRiskFlag(dto.getHighRiskFlag());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把TreatItemBaseDto转换成TreatItemVo */
    @AutoGenerated(locked = true, uuid = "055f8951-4fe5-4e06-b153-11df4a68e338-converter-list")
    public List<TreatItemVo> convertToTreatItemVoList(List<TreatItemBaseDto> dtoList) {
        return new ArrayList<>(convertToTreatItemVoMap(dtoList).values());
    }

    /** 使用默认方式组装TreatItemVo列表数据 */
    @AutoGenerated(locked = true, uuid = "1640add9-39ac-3a5c-a410-4dad8f4ea69b")
    public List<TreatItemVo> convertAndAssembleDataList(List<TreatItemBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        TreatItemVoDataHolder dataHolder = new TreatItemVoDataHolder();
        dataHolder.setRootBaseDtoList(dtoList);
        Map<String, TreatItemVo> voMap =
                convertToTreatItemVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        treatItemVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        treatItemVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 使用默认方式组装TreatItemVo数据 */
    @AutoGenerated(locked = true, uuid = "70f65457-f341-3778-b070-11198aabe905")
    public TreatItemVo convertAndAssembleData(TreatItemBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把TreatItemBaseDto转换成TreatItemVo */
    @AutoGenerated(locked = true, uuid = "dd4b3c3f-c17e-3852-b677-2023e3a86585")
    public TreatItemVo convertToTreatItemVo(TreatItemBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToTreatItemVoList(List.of(dto)).stream().findAny().orElse(null);
    }
}
