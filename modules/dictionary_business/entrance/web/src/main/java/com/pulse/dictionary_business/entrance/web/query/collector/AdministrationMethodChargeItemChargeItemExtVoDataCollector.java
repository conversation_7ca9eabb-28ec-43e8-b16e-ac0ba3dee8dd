package com.pulse.dictionary_business.entrance.web.query.collector;

import com.pulse.billing_public_config.manager.dto.ChargeItemBaseDto;
import com.pulse.billing_public_config.manager.dto.ChargeItemClinicItemDto;
import com.pulse.dictionary_business.entrance.web.converter.AdministrationMethodChargeItemChargeItemExtVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.ChargeItemClinicItemVoConverter;
import com.pulse.dictionary_business.entrance.web.query.assembler.AdministrationMethodChargeItemChargeItemExtVoDataAssembler.AdministrationMethodChargeItemChargeItemExtVoDataHolder;
import com.pulse.dictionary_business.entrance.web.vo.ChargeItemClinicItemVo;
import com.pulse.dictionary_business.manager.dto.AdministrationMethodChargeItemBaseDto;
import com.pulse.dictionary_business.manager.dto.AdministrationMethodChargeItemChargeItemExtDto;
import com.pulse.dictionary_business.manager.facade.billing_public_config.ChargeItemBaseDtoServiceInDictionaryBusinessRpcAdapter;
import com.pulse.dictionary_business.manager.facade.billing_public_config.ChargeItemClinicItemDtoServiceInDictionaryBusinessRpcAdapter;
import com.pulse.dictionary_business.service.AdministrationMethodChargeItemBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装AdministrationMethodChargeItemChargeItemExtVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "05222cef-fe55-3f17-988a-58420e7c9976")
public class AdministrationMethodChargeItemChargeItemExtVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private AdministrationMethodChargeItemBaseDtoService
            administrationMethodChargeItemBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private AdministrationMethodChargeItemChargeItemExtVoConverter
            administrationMethodChargeItemChargeItemExtVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private AdministrationMethodChargeItemChargeItemExtVoDataCollector
            administrationMethodChargeItemChargeItemExtVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemBaseDtoServiceInDictionaryBusinessRpcAdapter
            chargeItemBaseDtoServiceInDictionaryBusinessRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemClinicItemDtoServiceInDictionaryBusinessRpcAdapter
            chargeItemClinicItemDtoServiceInDictionaryBusinessRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemClinicItemVoConverter chargeItemClinicItemVoConverter;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "3a0fd207-ab70-31fa-884f-e90a7151453b")
    private void fillDataWhenNecessary(
            AdministrationMethodChargeItemChargeItemExtVoDataHolder dataHolder) {
        List<AdministrationMethodChargeItemBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.chargeItem == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(AdministrationMethodChargeItemBaseDto::getChargeItemId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ChargeItemBaseDto> baseDtoList =
                    chargeItemBaseDtoServiceInDictionaryBusinessRpcAdapter
                            .getByItemCodes(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(ChargeItemBaseDto::getItemCode))
                            .collect(Collectors.toList());
            Map<String, ChargeItemBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            ChargeItemBaseDto::getItemCode, Function.identity()));
            Map<String, ChargeItemClinicItemDto> chargeItemClinicItemDtoMap =
                    chargeItemClinicItemDtoServiceInDictionaryBusinessRpcAdapter
                            .getByItemCodes(
                                    baseDtoList.stream()
                                            .map(ChargeItemBaseDto::getItemCode)
                                            .collect(Collectors.toList()))
                            .stream()
                            .collect(
                                    Collectors.toMap(
                                            ChargeItemClinicItemDto::getItemCode,
                                            Function.identity()));
            Map<ChargeItemClinicItemDto, ChargeItemClinicItemVo> dtoVoMap =
                    chargeItemClinicItemVoConverter.convertToChargeItemClinicItemVoMap(
                            new ArrayList<>(chargeItemClinicItemDtoMap.values()));
            Map<ChargeItemBaseDto, ChargeItemClinicItemVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .filter(
                                    baseDto ->
                                            chargeItemClinicItemDtoMap.containsKey(
                                                    baseDto.getItemCode()))
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    dtoVoMap.get(
                                                            chargeItemClinicItemDtoMap.get(
                                                                    baseDto.getItemCode())),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.chargeItem =
                    rootDtoList.stream()
                            .map(AdministrationMethodChargeItemBaseDto::getChargeItemId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /**
     * 获取AdministrationMethodChargeItemChargeItemExtDto数据填充AdministrationMethodChargeItemChargeItemExtVo，并根据扩展关系填充剩余数据
     */
    @AutoGenerated(locked = true, uuid = "565b83e7-faeb-3ffe-8534-9be2314b71dd")
    public void collectDataWithDtoData(
            List<AdministrationMethodChargeItemChargeItemExtDto> dtoList,
            AdministrationMethodChargeItemChargeItemExtVoDataHolder dataHolder) {
        Map<ChargeItemBaseDto, ChargeItemClinicItemDto> chargeItemBaseDtoDtoMap =
                new LinkedHashMap<>();

        for (AdministrationMethodChargeItemChargeItemExtDto rootDto : dtoList) {
            ChargeItemClinicItemDto chargeItemDto = rootDto.getChargeItem();
            if (chargeItemDto != null) {
                ChargeItemBaseDto chargeItemBaseDto =
                        chargeItemBaseDtoServiceInDictionaryBusinessRpcAdapter
                                .getByItemCodes(List.of(chargeItemDto.getItemCode()))
                                .stream()
                                .findAny()
                                .get();
                chargeItemBaseDtoDtoMap.put(chargeItemBaseDto, chargeItemDto);
            }
        }

        // access chargeItem
        Map<ChargeItemClinicItemDto, ChargeItemClinicItemVo> chargeItemVoMap =
                chargeItemClinicItemVoConverter.convertToChargeItemClinicItemVoMap(
                        new ArrayList<>(chargeItemBaseDtoDtoMap.values()));
        dataHolder.chargeItem =
                chargeItemBaseDtoDtoMap.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                chargeItemVoMap.get(
                                                        chargeItemBaseDtoDtoMap.get(baseDto)),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "7b114b5f-3739-3831-9b0d-40e7edba6587")
    public void collectDataDefault(
            AdministrationMethodChargeItemChargeItemExtVoDataHolder dataHolder) {
        administrationMethodChargeItemChargeItemExtVoDataCollector.fillDataWhenNecessary(
                dataHolder);
    }
}
