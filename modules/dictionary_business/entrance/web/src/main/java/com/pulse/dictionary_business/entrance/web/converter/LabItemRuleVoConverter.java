package com.pulse.dictionary_business.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.entrance.web.query.assembler.LabItemRuleVoDataAssembler;
import com.pulse.dictionary_business.entrance.web.vo.LabItemRuleVo;
import com.pulse.dictionary_business.manager.dto.LabItemRuleBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到LabItemRuleVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "4934e8d7-3393-46eb-a8af-66f625e3fafb|VO|CONVERTER")
public class LabItemRuleVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private LabItemRuleVoDataAssembler labItemRuleVoDataAssembler;

    /** 把LabItemRuleBaseDto转换成LabItemRuleVo */
    @AutoGenerated(locked = false, uuid = "4934e8d7-3393-46eb-a8af-66f625e3fafb-converter-Map")
    public Map<LabItemRuleBaseDto, LabItemRuleVo> convertToLabItemRuleVoMap(
            List<LabItemRuleBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<LabItemRuleBaseDto, LabItemRuleVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            LabItemRuleVo vo = new LabItemRuleVo();
                                            vo.setId(dto.getId());
                                            vo.setLabItemId(dto.getLabItemId());
                                            vo.setUrgentType(dto.getUrgentType());
                                            vo.setLimitAgeMax(dto.getLimitAgeMax());
                                            vo.setLimitAgeUnit(dto.getLimitAgeUnit());
                                            vo.setLimitAgeMin(dto.getLimitAgeMin());
                                            vo.setLimitGender(dto.getLimitGender());
                                            vo.setLabItemInstruction(dto.getLabItemInstruction());
                                            vo.setApplicableOrderDepartmentIdList(
                                                    dto.getApplicableOrderDepartmentIdList());
                                            vo.setApplicableOrderDoctorIdList(
                                                    dto.getApplicableOrderDoctorIdList());
                                            vo.setNotAllowedOrderDepartmentIdList(
                                                    dto.getNotAllowedOrderDepartmentIdList());
                                            vo.setNotAllowedOrderDoctorIdList(
                                                    dto.getNotAllowedOrderDoctorIdList());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把LabItemRuleBaseDto转换成LabItemRuleVo */
    @AutoGenerated(locked = true, uuid = "4934e8d7-3393-46eb-a8af-66f625e3fafb-converter-list")
    public List<LabItemRuleVo> convertToLabItemRuleVoList(List<LabItemRuleBaseDto> dtoList) {
        return new ArrayList<>(convertToLabItemRuleVoMap(dtoList).values());
    }

    /** 把LabItemRuleBaseDto转换成LabItemRuleVo */
    @AutoGenerated(locked = true, uuid = "baaec3d8-d149-35f4-b60d-b55705701127")
    public LabItemRuleVo convertToLabItemRuleVo(LabItemRuleBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToLabItemRuleVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装LabItemRuleVo数据 */
    @AutoGenerated(locked = true, uuid = "ea490de2-a53e-366b-a6a4-f0e517c553b4")
    public LabItemRuleVo convertAndAssembleData(LabItemRuleBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装LabItemRuleVo列表数据 */
    @AutoGenerated(locked = true, uuid = "fbaaa1ee-9426-3cc4-a280-b957c60ee1b7")
    public List<LabItemRuleVo> convertAndAssembleDataList(List<LabItemRuleBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, LabItemRuleVo> voMap =
                convertToLabItemRuleVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        labItemRuleVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
