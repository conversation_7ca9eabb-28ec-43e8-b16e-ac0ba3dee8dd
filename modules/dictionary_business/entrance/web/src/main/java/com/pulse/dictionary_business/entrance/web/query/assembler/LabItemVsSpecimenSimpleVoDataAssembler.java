package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.LabItemVsSpecimenSimpleVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** LabItemVsSpecimenSimpleVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "0ef60cfa-cf09-37a7-a433-341d9cf57112")
public class LabItemVsSpecimenSimpleVoDataAssembler {

    /** 组装LabItemVsSpecimenSimpleVo数据 */
    @AutoGenerated(locked = true, uuid = "1259b0ce-4a3d-396e-8706-862829b498c3")
    public void assembleData(Map<Long, LabItemVsSpecimenSimpleVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装LabItemVsSpecimenSimpleVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "209eda11-f408-33b6-aefc-e38c2006bf2e")
    public void assembleDataCustomized(List<LabItemVsSpecimenSimpleVo> dataList) {
        // 自定义数据组装

    }
}
