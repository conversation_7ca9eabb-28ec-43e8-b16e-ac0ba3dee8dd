package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.ClinicItemDictionaryBaseVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** ClinicItemDictionaryBaseVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "5fbef31b-0d32-39e8-aab2-a231c3251cdf")
public class ClinicItemDictionaryBaseVoDataAssembler {

    /** 组装ClinicItemDictionaryBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "76b70eae-1dc3-391c-8e76-08d3256e127f")
    public void assembleData(Map<String, ClinicItemDictionaryBaseVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装ClinicItemDictionaryBaseVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "ca60dfb8-5958-3663-b45d-de7439329119")
    public void assembleDataCustomized(List<ClinicItemDictionaryBaseVo> dataList) {
        // 自定义数据组装

    }
}
