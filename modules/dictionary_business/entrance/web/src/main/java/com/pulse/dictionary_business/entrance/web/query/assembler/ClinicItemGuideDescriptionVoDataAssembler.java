package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.ClinicItemGuideDescriptionVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** ClinicItemGuideDescriptionVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "30e3a729-ac32-33b2-8b31-48249c8aac8b")
public class ClinicItemGuideDescriptionVoDataAssembler {

    /** 组装ClinicItemGuideDescriptionVo数据 */
    @AutoGenerated(locked = true, uuid = "29b8e11c-067c-37ed-a44a-752b3d2ae4f4")
    public void assembleData(Map<String, ClinicItemGuideDescriptionVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装ClinicItemGuideDescriptionVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "a2d46407-6abe-32d3-890f-190850c645bc")
    public void assembleDataCustomized(List<ClinicItemGuideDescriptionVo> dataList) {
        // 自定义数据组装

    }
}
