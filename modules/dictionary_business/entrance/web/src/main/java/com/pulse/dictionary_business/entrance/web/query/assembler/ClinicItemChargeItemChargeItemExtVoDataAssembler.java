package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.billing_public_config.manager.dto.ChargeItemBaseDto;
import com.pulse.dictionary_business.entrance.web.vo.ChargeItemVo;
import com.pulse.dictionary_business.entrance.web.vo.ClinicItemChargeItemChargeItemExtVo;
import com.pulse.dictionary_business.manager.dto.ClinicItemChargeItemBaseDto;
import com.pulse.dictionary_business.service.ClinicItemChargeItemBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** ClinicItemChargeItemChargeItemExtVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "67cbd2d6-88b6-3c89-91d1-10188116be37")
public class ClinicItemChargeItemChargeItemExtVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemChargeItemBaseDtoService clinicItemChargeItemBaseDtoService;

    /** 批量自定义组装ClinicItemChargeItemChargeItemExtVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "2e7444c4-f7aa-338e-b02b-6316ddf8eb70")
    public void assembleDataCustomized(List<ClinicItemChargeItemChargeItemExtVo> dataList) {
        // 自定义数据组装

    }

    /** 组装ClinicItemChargeItemChargeItemExtVo数据 */
    @AutoGenerated(locked = true, uuid = "5720925d-767e-3186-836b-49c99be62309")
    public void assembleData(
            Map<String, ClinicItemChargeItemChargeItemExtVo> voMap,
            ClinicItemChargeItemChargeItemExtVoDataAssembler
                            .ClinicItemChargeItemChargeItemExtVoDataHolder
                    dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<ClinicItemChargeItemBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, Pair<ChargeItemBaseDto, ChargeItemVo>> chargeItem =
                dataHolder.chargeItem.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getItemCode(),
                                        dto -> Pair.of(dto, dataHolder.chargeItem.get(dto)),
                                        (o1, o2) -> o1));

        for (ClinicItemChargeItemBaseDto baseDto : baseDtoList) {
            ClinicItemChargeItemChargeItemExtVo vo = voMap.get(baseDto.getId());
            vo.setChargeItem(
                    Optional.ofNullable(chargeItem.get(baseDto.getChargeItemId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class ClinicItemChargeItemChargeItemExtVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<ClinicItemChargeItemBaseDto> rootBaseDtoList;

        /** 持有字段chargeItem的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<ChargeItemBaseDto, ChargeItemVo> chargeItem;
    }
}
