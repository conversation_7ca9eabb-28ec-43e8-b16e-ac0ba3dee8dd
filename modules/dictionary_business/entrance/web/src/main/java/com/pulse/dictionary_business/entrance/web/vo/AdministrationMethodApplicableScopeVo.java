package com.pulse.dictionary_business.entrance.web.vo;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "7314af60-4c77-4f29-903a-dec2e17e4997|VO|DEFINITION")
public class AdministrationMethodApplicableScopeVo {
    /** 给药方式id */
    @AutoGenerated(locked = true, uuid = "acfafb09-92b6-4974-be87-d1de655162b2")
    private String administrationMethodId;

    /** 适用对象类型 */
    @AutoGenerated(locked = true, uuid = "c21d9c63-ed97-4f2c-833a-214e183561ec")
    private String applicableObjectType;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "404b1a60-5f2a-4bb1-938c-6520c2432004")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "98b03a4e-ff12-4daf-a5e5-ff90db4cda2a")
    private String createdBy;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "2cfd6b98-e694-4b2b-b60e-56ab242a855e")
    private String id;

    /** 对象ID */
    @AutoGenerated(locked = true, uuid = "f7552584-ffa9-4ae3-90c9-f77bdf3456a7")
    private String objectId;

    /** 对象名称 */
    @AutoGenerated(locked = true, uuid = "0858d683-9d76-45f6-83b3-64805ad9ccd0")
    private String objectName;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "6720795c-1017-481d-805b-e0136f8bc2be")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "7df0e69d-7aea-4670-94c9-6697c5a3ce69")
    private String updatedBy;
}
