package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.ClinicItemDictionaryBaseVo;
import com.pulse.dictionary_business.entrance.web.vo.LabClassDictionaryVo;
import com.pulse.dictionary_business.entrance.web.vo.LabItemDetailVo;
import com.pulse.dictionary_business.entrance.web.vo.LabItemExtensionVo;
import com.pulse.dictionary_business.entrance.web.vo.LabItemPackageDetailVo;
import com.pulse.dictionary_business.entrance.web.vo.LabItemPerformDepartmentVo;
import com.pulse.dictionary_business.entrance.web.vo.LabItemVsSpecimenVo;
import com.pulse.dictionary_business.manager.dto.ClinicItemDictionaryBaseDto;
import com.pulse.dictionary_business.manager.dto.LabClassDictionaryBaseDto;
import com.pulse.dictionary_business.manager.dto.LabItemBaseDto;
import com.pulse.dictionary_business.manager.dto.LabItemExtensionBaseDto;
import com.pulse.dictionary_business.manager.dto.LabItemPackageDetailBaseDto;
import com.pulse.dictionary_business.manager.dto.LabItemPerformDepartmentBaseDto;
import com.pulse.dictionary_business.manager.dto.LabItemVsSpecimenBaseDto;
import com.pulse.dictionary_business.service.LabItemBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** LabItemDetailVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "736206ca-c8ce-304a-a3e3-3128e81627d5")
public class LabItemDetailVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private LabItemBaseDtoService labItemBaseDtoService;

    /** 组装LabItemDetailVo数据 */
    @AutoGenerated(locked = true, uuid = "c5b6083c-639d-3339-ae59-914d69645385")
    public void assembleData(
            Map<String, LabItemDetailVo> voMap,
            LabItemDetailVoDataAssembler.LabItemDetailVoDataHolder dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<LabItemBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, Pair<ClinicItemDictionaryBaseDto, ClinicItemDictionaryBaseVo>> clinicItem =
                dataHolder.clinicItem.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getClinicItemId(),
                                        dto -> Pair.of(dto, dataHolder.clinicItem.get(dto)),
                                        (o1, o2) -> o1));
        Map<String, List<Pair<LabItemExtensionBaseDto, LabItemExtensionVo>>> labItemExtensionList =
                dataHolder.labItemExtensionList.keySet().stream()
                        .collect(
                                Collectors.groupingBy(
                                        dto -> dto.getLabItemId(),
                                        Collectors.mapping(
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder.labItemExtensionList.get(
                                                                        dto)),
                                                Collectors.toCollection(ArrayList::new))));
        Map<String, List<Pair<LabItemPerformDepartmentBaseDto, LabItemPerformDepartmentVo>>>
                labItemPerformDepartmentList =
                        dataHolder.labItemPerformDepartmentList.keySet().stream()
                                .collect(
                                        Collectors.groupingBy(
                                                dto -> dto.getLabItemId(),
                                                Collectors.mapping(
                                                        dto ->
                                                                Pair.of(
                                                                        dto,
                                                                        dataHolder
                                                                                .labItemPerformDepartmentList
                                                                                .get(dto)),
                                                        Collectors.toCollection(ArrayList::new))));
        Map<String, Pair<LabClassDictionaryBaseDto, LabClassDictionaryVo>> labClass =
                dataHolder.labClass.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getLabClassCode(),
                                        dto -> Pair.of(dto, dataHolder.labClass.get(dto)),
                                        (o1, o2) -> o1));
        Map<String, List<Pair<LabItemVsSpecimenBaseDto, LabItemVsSpecimenVo>>>
                labItemVsSpecimenList =
                        dataHolder.labItemVsSpecimenList.keySet().stream()
                                .collect(
                                        Collectors.groupingBy(
                                                dto -> dto.getLabItemId(),
                                                Collectors.mapping(
                                                        dto ->
                                                                Pair.of(
                                                                        dto,
                                                                        dataHolder
                                                                                .labItemVsSpecimenList
                                                                                .get(dto)),
                                                        Collectors.toCollection(ArrayList::new))));
        Map<String, List<Pair<LabItemPackageDetailBaseDto, LabItemPackageDetailVo>>>
                labItemPackageDetailList =
                        dataHolder.labItemPackageDetailList.keySet().stream()
                                .collect(
                                        Collectors.groupingBy(
                                                dto -> dto.getPackageId(),
                                                Collectors.mapping(
                                                        dto ->
                                                                Pair.of(
                                                                        dto,
                                                                        dataHolder
                                                                                .labItemPackageDetailList
                                                                                .get(dto)),
                                                        Collectors.toCollection(ArrayList::new))));

        for (LabItemBaseDto baseDto : baseDtoList) {
            LabItemDetailVo vo = voMap.get(baseDto.getId());
            vo.setClinicItem(
                    Optional.ofNullable(clinicItem.get(baseDto.getClinicItemId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
            vo.setLabItemExtensionList(
                    Optional.ofNullable(labItemExtensionList.get(baseDto.getId()))
                            .map(
                                    tmp ->
                                            tmp.stream()
                                                    .map(pair -> pair.getRight())
                                                    .collect(Collectors.toList()))
                            .orElse(new ArrayList<>()));
            vo.setLabItemPerformDepartmentList(
                    Optional.ofNullable(labItemPerformDepartmentList.get(baseDto.getId()))
                            .map(
                                    tmp ->
                                            tmp.stream()
                                                    .map(pair -> pair.getRight())
                                                    .collect(Collectors.toList()))
                            .orElse(new ArrayList<>()));
            vo.setLabClass(
                    Optional.ofNullable(labClass.get(baseDto.getLabClassId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
            vo.setLabItemVsSpecimenList(
                    Optional.ofNullable(labItemVsSpecimenList.get(baseDto.getId()))
                            .map(
                                    tmp ->
                                            tmp.stream()
                                                    .map(pair -> pair.getRight())
                                                    .collect(Collectors.toList()))
                            .orElse(new ArrayList<>()));
            vo.setLabItemPackageDetailList(
                    Optional.ofNullable(labItemPackageDetailList.get(baseDto.getId()))
                            .map(
                                    tmp ->
                                            tmp.stream()
                                                    .map(pair -> pair.getRight())
                                                    .collect(Collectors.toList()))
                            .orElse(new ArrayList<>()));
        }

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装LabItemDetailVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "ec8fd7b1-e8ab-3835-9274-0775de048916")
    public void assembleDataCustomized(List<LabItemDetailVo> dataList) {
        // 自定义数据组装

    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class LabItemDetailVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<LabItemBaseDto> rootBaseDtoList;

        /** 持有字段clinicItem的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<ClinicItemDictionaryBaseDto, ClinicItemDictionaryBaseVo> clinicItem;

        /** 持有字段labItemExtensionList的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<LabItemExtensionBaseDto, LabItemExtensionVo> labItemExtensionList;

        /** 持有字段labItemPerformDepartmentList的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<LabItemPerformDepartmentBaseDto, LabItemPerformDepartmentVo>
                labItemPerformDepartmentList;

        /** 持有字段labClass的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<LabClassDictionaryBaseDto, LabClassDictionaryVo> labClass;

        /** 持有字段labItemVsSpecimenList的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<LabItemVsSpecimenBaseDto, LabItemVsSpecimenVo> labItemVsSpecimenList;

        /** 持有字段labItemPackageDetailList的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<LabItemPackageDetailBaseDto, LabItemPackageDetailVo> labItemPackageDetailList;
    }
}
