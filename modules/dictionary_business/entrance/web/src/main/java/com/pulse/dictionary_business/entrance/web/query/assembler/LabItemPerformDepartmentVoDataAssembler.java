package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.LabItemPerformDepartmentVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** LabItemPerformDepartmentVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "73af7771-94a8-34f7-a849-c5bbfd8e1ddb")
public class LabItemPerformDepartmentVoDataAssembler {

    /** 组装LabItemPerformDepartmentVo数据 */
    @AutoGenerated(locked = true, uuid = "3a5fc09b-4570-3208-8b89-9b41495896e5")
    public void assembleData(Map<Long, LabItemPerformDepartmentVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装LabItemPerformDepartmentVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "7757d00a-15ea-345e-8a5a-5ffb3684c910")
    public void assembleDataCustomized(List<LabItemPerformDepartmentVo> dataList) {
        // 自定义数据组装

    }
}
