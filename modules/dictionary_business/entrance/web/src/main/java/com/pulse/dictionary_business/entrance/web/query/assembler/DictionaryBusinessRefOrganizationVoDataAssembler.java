package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.DictionaryBusinessRefOrganizationVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** DictionaryBusinessRefOrganizationVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "a062c9c4-fd99-3423-80d1-5215d924d628")
public class DictionaryBusinessRefOrganizationVoDataAssembler {

    /** 批量自定义组装DictionaryBusinessRefOrganizationVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "02434d06-26f2-32a7-a427-d65c10e60188")
    public void assembleDataCustomized(List<DictionaryBusinessRefOrganizationVo> dataList) {
        // 自定义数据组装

    }

    /** 组装DictionaryBusinessRefOrganizationVo数据 */
    @AutoGenerated(locked = true, uuid = "1938d1aa-ab83-3a4d-b893-54f9a2f52305")
    public void assembleData(Map<String, DictionaryBusinessRefOrganizationVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }
}
