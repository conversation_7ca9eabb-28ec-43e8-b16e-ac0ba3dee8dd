package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.LabItemRuleVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** LabItemRuleVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "7d06f2fa-2268-38ad-be76-80dd8ffc0dbc")
public class LabItemRuleVoDataAssembler {

    /** 组装LabItemRuleVo数据 */
    @AutoGenerated(locked = true, uuid = "5f83d95f-4cc6-3c75-b8d0-80b6f4c63221")
    public void assembleData(Map<String, LabItemRuleVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装LabItemRuleVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "b559b893-a5d8-3001-b150-d6db5a3b81e8")
    public void assembleDataCustomized(List<LabItemRuleVo> dataList) {
        // 自定义数据组装

    }
}
