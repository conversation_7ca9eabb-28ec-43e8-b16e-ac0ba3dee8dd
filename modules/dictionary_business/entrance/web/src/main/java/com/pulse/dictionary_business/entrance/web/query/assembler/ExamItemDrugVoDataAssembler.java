package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.ExamItemDrugVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** ExamItemDrugVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "832dba24-2b9b-3fe7-8a05-d969b725e51e")
public class ExamItemDrugVoDataAssembler {

    /** 组装ExamItemDrugVo数据 */
    @AutoGenerated(locked = true, uuid = "05111cbb-b486-3dec-9163-a1c315c300f8")
    public void assembleData(Map<String, ExamItemDrugVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装ExamItemDrugVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "ff3b3591-19b3-3fff-9693-f39fd80e05b0")
    public void assembleDataCustomized(List<ExamItemDrugVo> dataList) {
        // 自定义数据组装

    }
}
