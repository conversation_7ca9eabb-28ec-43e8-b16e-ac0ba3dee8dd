package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.AdministrationMethodApplicableScopeVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** AdministrationMethodApplicableScopeVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "701ace48-dd7a-36aa-8dc3-db764fe38363")
public class AdministrationMethodApplicableScopeVoDataAssembler {

    /** 组装AdministrationMethodApplicableScopeVo数据 */
    @AutoGenerated(locked = true, uuid = "4cb78681-e38e-3c40-a414-4a1664b0d372")
    public void assembleData(Map<String, AdministrationMethodApplicableScopeVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装AdministrationMethodApplicableScopeVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "df63dc01-d300-3218-b4e3-6df1331a78e0")
    public void assembleDataCustomized(List<AdministrationMethodApplicableScopeVo> dataList) {
        // 自定义数据组装

    }
}
