package com.pulse.dictionary_business.entrance.web.vo;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "13f12d94-29e1-4670-83c4-cdc6bba37663|VO|DEFINITION")
public class AdministrationMethodChargeItemChargeItemExtVo {
    /** 给药方式id */
    @AutoGenerated(locked = true, uuid = "4ae794dd-8cda-45be-b456-908910894e22")
    private String administrationMethodId;

    /** 院区id列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "49a70084-cb5e-475a-8313-4989a8af8ded")
    private List<String> campusIdList;

    /** 收费项目id */
    @Valid
    @AutoGenerated(locked = true, uuid = "3cc68e7f-1751-44dd-9ccd-ce1c36644f78")
    private ChargeItemClinicItemVo chargeItem;

    /** 续住标志 */
    @AutoGenerated(locked = true, uuid = "31053d23-0f85-4a40-9a69-4dc08498856a")
    private Boolean continueFlag;

    /** 数量 */
    @AutoGenerated(locked = true, uuid = "1d74b2a4-6343-44b7-8044-66ee1ff10654")
    private Long count;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "494a660d-fec0-4652-b733-64b70b880ad7")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "d3c6e77e-8ae1-448c-9198-2b4a5d76c760")
    private String createdBy;

    /** 急诊计费方法 */
    @AutoGenerated(locked = true, uuid = "a82469c1-09ea-4bc3-b23c-6edb37fa1b25")
    private String erpBillingMethod;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "51bcc9d0-0b82-41db-a9c3-b8e6c1a6fa75")
    private String id;

    /** 住院计费方法 */
    @AutoGenerated(locked = true, uuid = "54586a0f-2cd1-4891-ba40-6448119593a1")
    private String inpBillingMethod;

    /** 门诊计费方式 */
    @AutoGenerated(locked = true, uuid = "df5a3a2c-a44a-44a2-846e-54fa6fd947a0")
    private String outpBillingMethod;

    /** 执行科室id */
    @AutoGenerated(locked = true, uuid = "5ff55d50-7f51-4c46-b78a-************")
    private String performDepartmentId;

    /** 留抢计费方法 */
    @AutoGenerated(locked = true, uuid = "18a652bf-7b88-4937-91e7-c50702f1cf22")
    private String stayRescueBillingMethod;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "36d40aaa-156f-49f1-8e9c-bba42d4dae73")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "f14b1304-6a5a-42b9-8ab1-78347a0197ca")
    private String updatedBy;

    /** 使用范围列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "f0db26c3-11f3-4a4d-bd68-9dc1085156cc")
    private List<String> useScopeList;
}
