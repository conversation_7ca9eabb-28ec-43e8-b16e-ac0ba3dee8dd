package com.pulse.dictionary_business.entrance.web.vo;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "aa588fde-9169-48fc-9647-8c73c1137a63|VO|DEFINITION")
public class ExamDeviceOrderLimitBaseVo {
    /** 院区id */
    @Valid
    @AutoGenerated(locked = true, uuid = "64e2f30c-b86c-47e3-b412-2f3a875bdbd3")
    private List<String> campusIdList;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "bbf5d44a-aee3-45bb-abed-131b01b62dcc")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "8280d9d9-d602-4480-8e0a-6480ad5c9173")
    private String createdBy;

    /** 启用标志 */
    @AutoGenerated(locked = true, uuid = "be12e824-bd9b-43d5-a6aa-07ab5e8b3417")
    private Boolean enableFlag;

    /** 增强数量 */
    @AutoGenerated(locked = true, uuid = "fd3b08b0-7b9d-4769-9145-8e80d17fc62c")
    private Long enhancedCount;

    /** 增强扫描互斥标志 */
    @AutoGenerated(locked = true, uuid = "80763ae6-29f5-4eac-aaa1-561425b2c05e")
    private Boolean enhancedScanningMutexFlag;

    /** 检查设备id */
    @AutoGenerated(locked = true, uuid = "80ac9da4-8895-47bc-b9b1-6121ae90080b")
    private String examDeviceId;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "4764e940-25ea-4601-94a7-252e6be4512b")
    private Long id;

    /** 部位累加天数 */
    @AutoGenerated(locked = true, uuid = "9af07fa9-a94b-416e-833d-21a39c8b79e5")
    private Long partCountCalculationDay;

    /** 部位最大数量 */
    @AutoGenerated(locked = true, uuid = "a09a8f9d-514c-45db-bcdf-c06b936a088c")
    private Long partMaxCount;

    /** 有效部位数量 */
    @AutoGenerated(locked = true, uuid = "67e999a6-67cb-4ba4-b468-c44862d55a19")
    private Long partValidCount;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "12084e71-3eb9-4337-a6c1-eab4670f1c04")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "f28fe151-129c-49d0-a8c1-4a7b17db181a")
    private Long updatedBy;

    /** 使用范围 */
    @Valid
    @AutoGenerated(locked = true, uuid = "2d5ccded-3e71-49dd-8663-7d36d2aa1f11")
    private List<String> useScope;
}
