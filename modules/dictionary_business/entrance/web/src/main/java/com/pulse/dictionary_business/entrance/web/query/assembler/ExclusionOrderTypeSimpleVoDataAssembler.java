package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.ExclusionOrderTypeSimpleVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** ExclusionOrderTypeSimpleVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "70a8992c-004d-3783-af6e-0794e7a724b3")
public class ExclusionOrderTypeSimpleVoDataAssembler {

    /** 组装ExclusionOrderTypeSimpleVo数据 */
    @AutoGenerated(locked = true, uuid = "4b3a3de3-b04e-356c-a693-897676ad5b26")
    public void assembleData(Map<String, ExclusionOrderTypeSimpleVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装ExclusionOrderTypeSimpleVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "e0543fc8-a6f5-34ec-b6ed-74911c4a8254")
    public void assembleDataCustomized(List<ExclusionOrderTypeSimpleVo> dataList) {
        // 自定义数据组装

    }
}
