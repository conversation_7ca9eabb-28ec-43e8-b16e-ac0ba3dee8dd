package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.HighRiskDiagnosisBaseVo;
import com.pulse.dictionary_business.entrance.web.vo.HighRiskDrugBaseVo;
import com.pulse.dictionary_business.entrance.web.vo.HighRiskFactorVo;
import com.pulse.dictionary_business.entrance.web.vo.HighRiskItemBaseVo;
import com.pulse.dictionary_business.manager.dto.HighRiskDiagnosisBaseDto;
import com.pulse.dictionary_business.manager.dto.HighRiskDrugBaseDto;
import com.pulse.dictionary_business.manager.dto.HighRiskFactorRuleBaseDto;
import com.pulse.dictionary_business.manager.dto.HighRiskItemBaseDto;
import com.pulse.dictionary_business.service.HighRiskFactorRuleBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** HighRiskFactorVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "4dbbd7a1-1f94-3355-8b2a-28a0e8c70124")
public class HighRiskFactorVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private HighRiskFactorRuleBaseDtoService highRiskFactorRuleBaseDtoService;

    /** 批量自定义组装HighRiskFactorVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "79d87ce4-1c0a-3752-9f87-3c6d81826956")
    public void assembleDataCustomized(List<HighRiskFactorVo> dataList) {
        // 自定义数据组装

    }

    /** 组装HighRiskFactorVo数据 */
    @AutoGenerated(locked = true, uuid = "d81d6315-c9cc-3b28-9011-e9d0f719dd7f")
    public void assembleData(
            Map<String, HighRiskFactorVo> voMap,
            HighRiskFactorVoDataAssembler.HighRiskFactorVoDataHolder dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<HighRiskFactorRuleBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, List<Pair<HighRiskDrugBaseDto, HighRiskDrugBaseVo>>> highRiskDrugList =
                dataHolder.highRiskDrugList.keySet().stream()
                        .collect(
                                Collectors.groupingBy(
                                        dto -> dto.getHighRiskFactorId(),
                                        Collectors.mapping(
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder.highRiskDrugList.get(
                                                                        dto)),
                                                Collectors.toCollection(ArrayList::new))));
        Map<String, List<Pair<HighRiskDiagnosisBaseDto, HighRiskDiagnosisBaseVo>>>
                highRiskDiagnosisList =
                        dataHolder.highRiskDiagnosisList.keySet().stream()
                                .collect(
                                        Collectors.groupingBy(
                                                dto -> dto.getHighRiskFactorId(),
                                                Collectors.mapping(
                                                        dto ->
                                                                Pair.of(
                                                                        dto,
                                                                        dataHolder
                                                                                .highRiskDiagnosisList
                                                                                .get(dto)),
                                                        Collectors.toCollection(ArrayList::new))));
        Map<String, List<Pair<HighRiskItemBaseDto, HighRiskItemBaseVo>>> highRiskItemList =
                dataHolder.highRiskItemList.keySet().stream()
                        .collect(
                                Collectors.groupingBy(
                                        dto -> dto.getHighRiskFactorId(),
                                        Collectors.mapping(
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder.highRiskItemList.get(
                                                                        dto)),
                                                Collectors.toCollection(ArrayList::new))));

        for (HighRiskFactorRuleBaseDto baseDto : baseDtoList) {
            HighRiskFactorVo vo = voMap.get(baseDto.getId());
            vo.setHighRiskDrugList(
                    Optional.ofNullable(highRiskDrugList.get(baseDto.getId()))
                            .map(
                                    tmp ->
                                            tmp.stream()
                                                    .map(pair -> pair.getRight())
                                                    .collect(Collectors.toList()))
                            .orElse(new ArrayList<>()));
            vo.setHighRiskDiagnosisList(
                    Optional.ofNullable(highRiskDiagnosisList.get(baseDto.getId()))
                            .map(
                                    tmp ->
                                            tmp.stream()
                                                    .map(pair -> pair.getRight())
                                                    .collect(Collectors.toList()))
                            .orElse(new ArrayList<>()));
            vo.setHighRiskItemList(
                    Optional.ofNullable(highRiskItemList.get(baseDto.getId()))
                            .map(
                                    tmp ->
                                            tmp.stream()
                                                    .map(pair -> pair.getRight())
                                                    .collect(Collectors.toList()))
                            .orElse(new ArrayList<>()));
        }

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class HighRiskFactorVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<HighRiskFactorRuleBaseDto> rootBaseDtoList;

        /** 持有字段highRiskDrugList的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<HighRiskDrugBaseDto, HighRiskDrugBaseVo> highRiskDrugList;

        /** 持有字段highRiskDiagnosisList的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<HighRiskDiagnosisBaseDto, HighRiskDiagnosisBaseVo> highRiskDiagnosisList;

        /** 持有字段highRiskItemList的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<HighRiskItemBaseDto, HighRiskItemBaseVo> highRiskItemList;
    }
}
