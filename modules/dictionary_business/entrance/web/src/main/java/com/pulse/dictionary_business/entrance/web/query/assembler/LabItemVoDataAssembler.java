package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.ClinicItemDictionarySimpleVo;
import com.pulse.dictionary_business.entrance.web.vo.LabClassDictionarySimpleVo;
import com.pulse.dictionary_business.entrance.web.vo.LabItemVo;
import com.pulse.dictionary_business.manager.dto.ClinicItemDictionaryBaseDto;
import com.pulse.dictionary_business.manager.dto.LabClassDictionaryBaseDto;
import com.pulse.dictionary_business.manager.dto.LabItemBaseDto;
import com.pulse.dictionary_business.service.LabItemBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** LabItemVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "bb70c078-2245-3d18-b0ef-a9d163000844")
public class LabItemVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private LabItemBaseDtoService labItemBaseDtoService;

    /** 组装LabItemVo数据 */
    @AutoGenerated(locked = true, uuid = "09980430-65bc-3268-b1fd-4455d05852ce")
    public void assembleData(
            Map<String, LabItemVo> voMap, LabItemVoDataAssembler.LabItemVoDataHolder dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<LabItemBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, Pair<ClinicItemDictionaryBaseDto, ClinicItemDictionarySimpleVo>> clinicItem =
                dataHolder.clinicItem.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getClinicItemId(),
                                        dto -> Pair.of(dto, dataHolder.clinicItem.get(dto)),
                                        (o1, o2) -> o1));
        Map<String, Pair<LabClassDictionaryBaseDto, LabClassDictionarySimpleVo>> labClass =
                dataHolder.labClass.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getLabClassCode(),
                                        dto -> Pair.of(dto, dataHolder.labClass.get(dto)),
                                        (o1, o2) -> o1));

        for (LabItemBaseDto baseDto : baseDtoList) {
            LabItemVo vo = voMap.get(baseDto.getId());
            vo.setClinicItem(
                    Optional.ofNullable(clinicItem.get(baseDto.getClinicItemId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
            vo.setLabClass(
                    Optional.ofNullable(labClass.get(baseDto.getLabClassId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装LabItemVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "1e444afa-defd-3909-93c8-e176d0bd5da5")
    public void assembleDataCustomized(List<LabItemVo> dataList) {
        // 自定义数据组装

    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class LabItemVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<LabItemBaseDto> rootBaseDtoList;

        /** 持有字段clinicItem的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<ClinicItemDictionaryBaseDto, ClinicItemDictionarySimpleVo> clinicItem;

        /** 持有字段labClass的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<LabClassDictionaryBaseDto, LabClassDictionarySimpleVo> labClass;
    }
}
