package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.ClinicItemDictionaryVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamItemListVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamTypeDictionarySimpleVo;
import com.pulse.dictionary_business.manager.dto.ClinicItemDictionaryBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamItemBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamTypeDictionaryBaseDto;
import com.pulse.dictionary_business.service.ExamItemBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** ExamItemListVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "46a72829-f847-3085-a6a7-3b770cfdd308")
public class ExamItemListVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private ExamItemBaseDtoService examItemBaseDtoService;

    /** 批量自定义组装ExamItemListVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "18033849-02e0-315a-acc2-62a20efa94d9")
    public void assembleDataCustomized(List<ExamItemListVo> dataList) {
        // 自定义数据组装

    }

    /** 组装ExamItemListVo数据 */
    @AutoGenerated(locked = true, uuid = "bbc7d3db-2af9-326b-844b-afbb77a64c56")
    public void assembleData(
            Map<String, ExamItemListVo> voMap,
            ExamItemListVoDataAssembler.ExamItemListVoDataHolder dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<ExamItemBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, Pair<ClinicItemDictionaryBaseDto, ClinicItemDictionaryVo>> clinicItem =
                dataHolder.clinicItem.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getClinicItemId(),
                                        dto -> Pair.of(dto, dataHolder.clinicItem.get(dto)),
                                        (o1, o2) -> o1));
        Map<String, Pair<ExamTypeDictionaryBaseDto, ExamTypeDictionarySimpleVo>> examType =
                dataHolder.examType.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getId(),
                                        dto -> Pair.of(dto, dataHolder.examType.get(dto)),
                                        (o1, o2) -> o1));

        for (ExamItemBaseDto baseDto : baseDtoList) {
            ExamItemListVo vo = voMap.get(baseDto.getId());
            vo.setClinicItem(
                    Optional.ofNullable(clinicItem.get(baseDto.getClinicItemId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
            vo.setExamType(
                    Optional.ofNullable(examType.get(baseDto.getExamTypeId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class ExamItemListVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<ExamItemBaseDto> rootBaseDtoList;

        /** 持有字段clinicItem的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<ClinicItemDictionaryBaseDto, ClinicItemDictionaryVo> clinicItem;

        /** 持有字段examType的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<ExamTypeDictionaryBaseDto, ExamTypeDictionarySimpleVo> examType;
    }
}
