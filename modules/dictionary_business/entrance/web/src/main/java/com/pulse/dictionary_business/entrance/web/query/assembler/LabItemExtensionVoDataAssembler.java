package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.LabItemExtensionVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** LabItemExtensionVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "f99b7cd9-6f6a-328e-bb58-6286e32ca8b6")
public class LabItemExtensionVoDataAssembler {

    /** 批量自定义组装LabItemExtensionVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "00f00dc8-9d72-3a5e-af3e-************")
    public void assembleDataCustomized(List<LabItemExtensionVo> dataList) {
        // 自定义数据组装

    }

    /** 组装LabItemExtensionVo数据 */
    @AutoGenerated(locked = true, uuid = "e8301ad7-61ed-30ba-a295-e1eb8f12adda")
    public void assembleData(Map<String, LabItemExtensionVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }
}
