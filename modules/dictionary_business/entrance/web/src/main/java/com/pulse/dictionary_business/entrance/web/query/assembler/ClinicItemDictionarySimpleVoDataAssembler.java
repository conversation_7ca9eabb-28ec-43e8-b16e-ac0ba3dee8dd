package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.ClinicItemDictionarySimpleVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** ClinicItemDictionarySimpleVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "5981ce19-9bda-373a-9a6a-5264938437d3")
public class ClinicItemDictionarySimpleVoDataAssembler {

    /** 批量自定义组装ClinicItemDictionarySimpleVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "34064498-36c1-3a29-86d7-df27869d76ca")
    public void assembleDataCustomized(List<ClinicItemDictionarySimpleVo> dataList) {
        // 自定义数据组装

    }

    /** 组装ClinicItemDictionarySimpleVo数据 */
    @AutoGenerated(locked = true, uuid = "4fd69388-fa62-3d43-b62a-cb9420101cd2")
    public void assembleData(Map<String, ClinicItemDictionarySimpleVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }
}
