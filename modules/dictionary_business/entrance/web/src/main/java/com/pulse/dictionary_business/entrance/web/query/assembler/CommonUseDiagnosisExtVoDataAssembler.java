package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.CommonUseDiagnosisExtVo;
import com.pulse.dictionary_business.entrance.web.vo.DiagnosisDictionaryVo;
import com.pulse.dictionary_business.manager.dto.CommonUseDiagnosisBaseDto;
import com.pulse.dictionary_business.manager.dto.DiagnosisDictionaryBaseDto;
import com.pulse.dictionary_business.service.CommonUseDiagnosisBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** CommonUseDiagnosisExtVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "075fa84f-2b3f-33bb-b2e5-550c65ab0f6c")
public class CommonUseDiagnosisExtVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private CommonUseDiagnosisBaseDtoService commonUseDiagnosisBaseDtoService;

    /** 批量自定义组装CommonUseDiagnosisExtVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "b965d56d-86b3-3990-a5bd-cdaf07615727")
    public void assembleDataCustomized(List<CommonUseDiagnosisExtVo> dataList) {
        // 自定义数据组装

    }

    /** 组装CommonUseDiagnosisExtVo数据 */
    @AutoGenerated(locked = true, uuid = "ec4ba31d-c734-38b0-a0ea-8c96f0f8f30e")
    public void assembleData(
            Map<String, CommonUseDiagnosisExtVo> voMap,
            CommonUseDiagnosisExtVoDataAssembler.CommonUseDiagnosisExtVoDataHolder dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<CommonUseDiagnosisBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, Pair<DiagnosisDictionaryBaseDto, DiagnosisDictionaryVo>> diagnosis =
                dataHolder.diagnosis.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getId(),
                                        dto -> Pair.of(dto, dataHolder.diagnosis.get(dto)),
                                        (o1, o2) -> o1));

        for (CommonUseDiagnosisBaseDto baseDto : baseDtoList) {
            CommonUseDiagnosisExtVo vo = voMap.get(baseDto.getId());
            vo.setDiagnosis(
                    Optional.ofNullable(diagnosis.get(baseDto.getDiagnosisId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class CommonUseDiagnosisExtVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<CommonUseDiagnosisBaseDto> rootBaseDtoList;

        /** 持有字段diagnosis的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DiagnosisDictionaryBaseDto, DiagnosisDictionaryVo> diagnosis;
    }
}
