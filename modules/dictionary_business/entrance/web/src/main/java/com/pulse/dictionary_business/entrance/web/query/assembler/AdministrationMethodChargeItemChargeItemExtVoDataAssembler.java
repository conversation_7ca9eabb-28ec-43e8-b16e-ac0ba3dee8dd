package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.billing_public_config.manager.dto.ChargeItemBaseDto;
import com.pulse.dictionary_business.entrance.web.vo.AdministrationMethodChargeItemChargeItemExtVo;
import com.pulse.dictionary_business.entrance.web.vo.ChargeItemClinicItemVo;
import com.pulse.dictionary_business.manager.dto.AdministrationMethodChargeItemBaseDto;
import com.pulse.dictionary_business.service.AdministrationMethodChargeItemBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** AdministrationMethodChargeItemChargeItemExtVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "330f96c3-11f7-39f0-b3fb-7b5461b77439")
public class AdministrationMethodChargeItemChargeItemExtVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private AdministrationMethodChargeItemBaseDtoService
            administrationMethodChargeItemBaseDtoService;

    /** 批量自定义组装AdministrationMethodChargeItemChargeItemExtVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "727b860e-32c1-349e-abad-5762485f5835")
    public void assembleDataCustomized(
            List<AdministrationMethodChargeItemChargeItemExtVo> dataList) {
        // 自定义数据组装

    }

    /** 组装AdministrationMethodChargeItemChargeItemExtVo数据 */
    @AutoGenerated(locked = true, uuid = "fe2deeb5-4d69-3f28-812a-4c3c06fefc8d")
    public void assembleData(
            Map<String, AdministrationMethodChargeItemChargeItemExtVo> voMap,
            AdministrationMethodChargeItemChargeItemExtVoDataAssembler
                            .AdministrationMethodChargeItemChargeItemExtVoDataHolder
                    dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<AdministrationMethodChargeItemBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, Pair<ChargeItemBaseDto, ChargeItemClinicItemVo>> chargeItem =
                dataHolder.chargeItem.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getItemCode(),
                                        dto -> Pair.of(dto, dataHolder.chargeItem.get(dto)),
                                        (o1, o2) -> o1));

        for (AdministrationMethodChargeItemBaseDto baseDto : baseDtoList) {
            AdministrationMethodChargeItemChargeItemExtVo vo = voMap.get(baseDto.getId());
            vo.setChargeItem(
                    Optional.ofNullable(chargeItem.get(baseDto.getChargeItemId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class AdministrationMethodChargeItemChargeItemExtVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<AdministrationMethodChargeItemBaseDto> rootBaseDtoList;

        /** 持有字段chargeItem的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<ChargeItemBaseDto, ChargeItemClinicItemVo> chargeItem;
    }
}
