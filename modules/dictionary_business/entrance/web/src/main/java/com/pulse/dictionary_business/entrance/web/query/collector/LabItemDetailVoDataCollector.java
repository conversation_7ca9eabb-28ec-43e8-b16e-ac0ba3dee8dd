package com.pulse.dictionary_business.entrance.web.query.collector;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.entrance.web.converter.ClinicItemDictionaryBaseVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.LabClassDictionaryVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.LabItemDetailVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.LabItemExtensionVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.LabItemPackageDetailVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.LabItemPerformDepartmentVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.LabItemVsSpecimenVoConverter;
import com.pulse.dictionary_business.entrance.web.query.assembler.LabItemDetailVoDataAssembler.LabItemDetailVoDataHolder;
import com.pulse.dictionary_business.entrance.web.vo.ClinicItemDictionaryBaseVo;
import com.pulse.dictionary_business.entrance.web.vo.LabClassDictionaryVo;
import com.pulse.dictionary_business.entrance.web.vo.LabItemExtensionVo;
import com.pulse.dictionary_business.entrance.web.vo.LabItemPackageDetailVo;
import com.pulse.dictionary_business.entrance.web.vo.LabItemPerformDepartmentVo;
import com.pulse.dictionary_business.entrance.web.vo.LabItemVsSpecimenVo;
import com.pulse.dictionary_business.manager.dto.ClinicItemDictionaryBaseDto;
import com.pulse.dictionary_business.manager.dto.LabClassDictionaryBaseDto;
import com.pulse.dictionary_business.manager.dto.LabItemBaseDto;
import com.pulse.dictionary_business.manager.dto.LabItemDetailDto;
import com.pulse.dictionary_business.manager.dto.LabItemExtensionBaseDto;
import com.pulse.dictionary_business.manager.dto.LabItemPackageDetailBaseDto;
import com.pulse.dictionary_business.manager.dto.LabItemPerformDepartmentBaseDto;
import com.pulse.dictionary_business.manager.dto.LabItemVsSpecimenBaseDto;
import com.pulse.dictionary_business.service.ClinicItemDictionaryBaseDtoService;
import com.pulse.dictionary_business.service.LabClassDictionaryBaseDtoService;
import com.pulse.dictionary_business.service.LabItemBaseDtoService;
import com.pulse.dictionary_business.service.LabItemExtensionBaseDtoService;
import com.pulse.dictionary_business.service.LabItemPackageDetailBaseDtoService;
import com.pulse.dictionary_business.service.LabItemPerformDepartmentBaseDtoService;
import com.pulse.dictionary_business.service.LabItemVsSpecimenBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装LabItemDetailVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "36b5b244-4f1e-3379-aabf-7556f410ef55")
public class LabItemDetailVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemDictionaryBaseDtoService clinicItemDictionaryBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemDictionaryBaseVoConverter clinicItemDictionaryBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private LabClassDictionaryBaseDtoService labClassDictionaryBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private LabClassDictionaryVoConverter labClassDictionaryVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private LabItemBaseDtoService labItemBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private LabItemDetailVoConverter labItemDetailVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private LabItemDetailVoDataCollector labItemDetailVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private LabItemExtensionBaseDtoService labItemExtensionBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private LabItemExtensionVoConverter labItemExtensionVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private LabItemPackageDetailBaseDtoService labItemPackageDetailBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private LabItemPackageDetailVoConverter labItemPackageDetailVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private LabItemPerformDepartmentBaseDtoService labItemPerformDepartmentBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private LabItemPerformDepartmentVoConverter labItemPerformDepartmentVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private LabItemVsSpecimenBaseDtoService labItemVsSpecimenBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private LabItemVsSpecimenVoConverter labItemVsSpecimenVoConverter;

    /** 获取LabItemDetailDto数据填充LabItemDetailVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "13cc7c77-db32-39ad-aaf2-0e6662013635")
    public void collectDataWithDtoData(
            List<LabItemDetailDto> dtoList, LabItemDetailVoDataHolder dataHolder) {
        List<ClinicItemDictionaryBaseDto> clinicItemList = new ArrayList<>();
        List<LabItemExtensionBaseDto> labItemExtensionListList = new ArrayList<>();
        List<LabItemPerformDepartmentBaseDto> labItemPerformDepartmentListList = new ArrayList<>();
        List<LabClassDictionaryBaseDto> labClassList = new ArrayList<>();
        List<LabItemVsSpecimenBaseDto> labItemVsSpecimenListList = new ArrayList<>();
        List<LabItemPackageDetailBaseDto> labItemPackageDetailListList = new ArrayList<>();

        for (LabItemDetailDto rootDto : dtoList) {
            ClinicItemDictionaryBaseDto clinicItemDto = rootDto.getClinicItem();
            if (clinicItemDto != null) {
                clinicItemList.add(clinicItemDto);
            }
            if (CollectionUtil.isNotEmpty(rootDto.getLabItemExtensionList())) {
                for (LabItemExtensionBaseDto labItemExtensionListDto :
                        rootDto.getLabItemExtensionList()) {
                    labItemExtensionListList.add(labItemExtensionListDto);
                }
            }
            if (CollectionUtil.isNotEmpty(rootDto.getLabItemPerformDepartmentList())) {
                for (LabItemPerformDepartmentBaseDto labItemPerformDepartmentListDto :
                        rootDto.getLabItemPerformDepartmentList()) {
                    labItemPerformDepartmentListList.add(labItemPerformDepartmentListDto);
                }
            }
            LabClassDictionaryBaseDto labClassDto = rootDto.getLabClass();
            if (labClassDto != null) {
                labClassList.add(labClassDto);
            }
            if (CollectionUtil.isNotEmpty(rootDto.getLabItemVsSpecimenList())) {
                for (LabItemVsSpecimenBaseDto labItemVsSpecimenListDto :
                        rootDto.getLabItemVsSpecimenList()) {
                    labItemVsSpecimenListList.add(labItemVsSpecimenListDto);
                }
            }
            if (CollectionUtil.isNotEmpty(rootDto.getLabItemDetailList())) {
                for (LabItemPackageDetailBaseDto labItemPackageDetailListDto :
                        rootDto.getLabItemDetailList()) {
                    labItemPackageDetailListList.add(labItemPackageDetailListDto);
                }
            }
        }

        // access clinicItem
        Map<ClinicItemDictionaryBaseDto, ClinicItemDictionaryBaseVo> clinicItemVoMap =
                clinicItemDictionaryBaseVoConverter.convertToClinicItemDictionaryBaseVoMap(
                        clinicItemList);
        dataHolder.clinicItem =
                clinicItemList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> clinicItemVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access labItemExtensionList
        Map<LabItemExtensionBaseDto, LabItemExtensionVo> labItemExtensionListVoMap =
                labItemExtensionVoConverter.convertToLabItemExtensionVoMap(
                        labItemExtensionListList);
        dataHolder.labItemExtensionList =
                labItemExtensionListList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> labItemExtensionListVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access labItemPerformDepartmentList
        Map<LabItemPerformDepartmentBaseDto, LabItemPerformDepartmentVo>
                labItemPerformDepartmentListVoMap =
                        labItemPerformDepartmentVoConverter.convertToLabItemPerformDepartmentVoMap(
                                labItemPerformDepartmentListList);
        dataHolder.labItemPerformDepartmentList =
                labItemPerformDepartmentListList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> labItemPerformDepartmentListVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access labClass
        Map<LabClassDictionaryBaseDto, LabClassDictionaryVo> labClassVoMap =
                labClassDictionaryVoConverter.convertToLabClassDictionaryVoMap(labClassList);
        dataHolder.labClass =
                labClassList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> labClassVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access labItemVsSpecimenList
        Map<LabItemVsSpecimenBaseDto, LabItemVsSpecimenVo> labItemVsSpecimenListVoMap =
                labItemVsSpecimenVoConverter.convertToLabItemVsSpecimenVoMap(
                        labItemVsSpecimenListList);
        dataHolder.labItemVsSpecimenList =
                labItemVsSpecimenListList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> labItemVsSpecimenListVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access labItemPackageDetailList
        Map<LabItemPackageDetailBaseDto, LabItemPackageDetailVo> labItemPackageDetailListVoMap =
                labItemPackageDetailVoConverter.convertToLabItemPackageDetailVoMap(
                        labItemPackageDetailListList);
        dataHolder.labItemPackageDetailList =
                labItemPackageDetailListList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> labItemPackageDetailListVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "382a333f-b522-3b47-a8bb-54bec16d6c09")
    public void collectDataDefault(LabItemDetailVoDataHolder dataHolder) {
        labItemDetailVoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "468a96ee-57f2-3a0b-becb-a159245f3c39")
    private void fillDataWhenNecessary(LabItemDetailVoDataHolder dataHolder) {
        List<LabItemBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.clinicItem == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(LabItemBaseDto::getClinicItemId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ClinicItemDictionaryBaseDto> baseDtoList =
                    clinicItemDictionaryBaseDtoService
                            .getByClinicItemIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(
                                    Comparator.comparing(
                                            ClinicItemDictionaryBaseDto::getClinicItemId))
                            .collect(Collectors.toList());
            Map<String, ClinicItemDictionaryBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            ClinicItemDictionaryBaseDto::getClinicItemId,
                                            Function.identity()));
            Map<ClinicItemDictionaryBaseDto, ClinicItemDictionaryBaseVo> dtoVoMap =
                    clinicItemDictionaryBaseVoConverter.convertToClinicItemDictionaryBaseVoMap(
                            baseDtoList);
            Map<ClinicItemDictionaryBaseDto, ClinicItemDictionaryBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.clinicItem =
                    rootDtoList.stream()
                            .map(LabItemBaseDto::getClinicItemId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.labItemExtensionList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(LabItemBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<LabItemExtensionBaseDto> baseDtoList =
                    labItemExtensionBaseDtoService.getByLabItemIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(LabItemExtensionBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<LabItemExtensionBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(LabItemExtensionBaseDto::getLabItemId));
            Map<LabItemExtensionBaseDto, LabItemExtensionVo> dtoVoMap =
                    labItemExtensionVoConverter.convertToLabItemExtensionVoMap(baseDtoList);
            Map<LabItemExtensionBaseDto, LabItemExtensionVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.labItemExtensionList =
                    rootDtoList.stream()
                            .map(LabItemBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.labItemPerformDepartmentList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(LabItemBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<LabItemPerformDepartmentBaseDto> baseDtoList =
                    labItemPerformDepartmentBaseDtoService
                            .getByLabItemIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(LabItemPerformDepartmentBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<LabItemPerformDepartmentBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            LabItemPerformDepartmentBaseDto::getLabItemId));
            Map<LabItemPerformDepartmentBaseDto, LabItemPerformDepartmentVo> dtoVoMap =
                    labItemPerformDepartmentVoConverter.convertToLabItemPerformDepartmentVoMap(
                            baseDtoList);
            Map<LabItemPerformDepartmentBaseDto, LabItemPerformDepartmentVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.labItemPerformDepartmentList =
                    rootDtoList.stream()
                            .map(LabItemBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.labClass == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(LabItemBaseDto::getLabClassId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<LabClassDictionaryBaseDto> baseDtoList =
                    labClassDictionaryBaseDtoService
                            .getByLabClassCodes(new ArrayList<>(ids))
                            .stream()
                            .sorted(
                                    Comparator.comparing(
                                            LabClassDictionaryBaseDto::getLabClassCode))
                            .collect(Collectors.toList());
            Map<String, LabClassDictionaryBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            LabClassDictionaryBaseDto::getLabClassCode,
                                            Function.identity()));
            Map<LabClassDictionaryBaseDto, LabClassDictionaryVo> dtoVoMap =
                    labClassDictionaryVoConverter.convertToLabClassDictionaryVoMap(baseDtoList);
            Map<LabClassDictionaryBaseDto, LabClassDictionaryVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.labClass =
                    rootDtoList.stream()
                            .map(LabItemBaseDto::getLabClassId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.labItemVsSpecimenList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(LabItemBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<LabItemVsSpecimenBaseDto> baseDtoList =
                    labItemVsSpecimenBaseDtoService.getByLabItemIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(LabItemVsSpecimenBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<LabItemVsSpecimenBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(LabItemVsSpecimenBaseDto::getLabItemId));
            Map<LabItemVsSpecimenBaseDto, LabItemVsSpecimenVo> dtoVoMap =
                    labItemVsSpecimenVoConverter.convertToLabItemVsSpecimenVoMap(baseDtoList);
            Map<LabItemVsSpecimenBaseDto, LabItemVsSpecimenVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.labItemVsSpecimenList =
                    rootDtoList.stream()
                            .map(LabItemBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.labItemPackageDetailList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(LabItemBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<LabItemPackageDetailBaseDto> baseDtoList =
                    labItemPackageDetailBaseDtoService
                            .getByPackageIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(LabItemPackageDetailBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<LabItemPackageDetailBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            LabItemPackageDetailBaseDto::getPackageId));
            Map<LabItemPackageDetailBaseDto, LabItemPackageDetailVo> dtoVoMap =
                    labItemPackageDetailVoConverter.convertToLabItemPackageDetailVoMap(baseDtoList);
            Map<LabItemPackageDetailBaseDto, LabItemPackageDetailVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.labItemPackageDetailList =
                    rootDtoList.stream()
                            .map(LabItemBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }
}
