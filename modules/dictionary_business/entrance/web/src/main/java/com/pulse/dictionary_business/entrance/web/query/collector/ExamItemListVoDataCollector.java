package com.pulse.dictionary_business.entrance.web.query.collector;

import com.pulse.dictionary_business.entrance.web.converter.ClinicItemDictionaryVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.ExamItemListVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.ExamTypeDictionarySimpleVoConverter;
import com.pulse.dictionary_business.entrance.web.query.assembler.ExamItemListVoDataAssembler.ExamItemListVoDataHolder;
import com.pulse.dictionary_business.entrance.web.vo.ClinicItemDictionaryVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamTypeDictionarySimpleVo;
import com.pulse.dictionary_business.manager.dto.ClinicItemDictionaryBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamItemBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamItemListDto;
import com.pulse.dictionary_business.manager.dto.ExamTypeDictionaryBaseDto;
import com.pulse.dictionary_business.service.ClinicItemDictionaryBaseDtoService;
import com.pulse.dictionary_business.service.ExamItemBaseDtoService;
import com.pulse.dictionary_business.service.ExamTypeDictionaryBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装ExamItemListVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "29e1f54a-fb83-3719-97e9-9789465a0d53")
public class ExamItemListVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemDictionaryBaseDtoService clinicItemDictionaryBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemDictionaryVoConverter clinicItemDictionaryVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ExamItemBaseDtoService examItemBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ExamItemListVoConverter examItemListVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ExamItemListVoDataCollector examItemListVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeDictionaryBaseDtoService examTypeDictionaryBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeDictionarySimpleVoConverter examTypeDictionarySimpleVoConverter;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "1de869f0-9224-3208-8bb7-b4ce986391ef")
    private void fillDataWhenNecessary(ExamItemListVoDataHolder dataHolder) {
        List<ExamItemBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.clinicItem == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(ExamItemBaseDto::getClinicItemId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ClinicItemDictionaryBaseDto> baseDtoList =
                    clinicItemDictionaryBaseDtoService
                            .getByClinicItemIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(
                                    Comparator.comparing(
                                            ClinicItemDictionaryBaseDto::getClinicItemId))
                            .collect(Collectors.toList());
            Map<String, ClinicItemDictionaryBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            ClinicItemDictionaryBaseDto::getClinicItemId,
                                            Function.identity()));
            Map<ClinicItemDictionaryBaseDto, ClinicItemDictionaryVo> dtoVoMap =
                    clinicItemDictionaryVoConverter.convertToClinicItemDictionaryVoMap(baseDtoList);
            Map<ClinicItemDictionaryBaseDto, ClinicItemDictionaryVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.clinicItem =
                    rootDtoList.stream()
                            .map(ExamItemBaseDto::getClinicItemId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.examType == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(ExamItemBaseDto::getExamTypeId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ExamTypeDictionaryBaseDto> baseDtoList =
                    examTypeDictionaryBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(ExamTypeDictionaryBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, ExamTypeDictionaryBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            ExamTypeDictionaryBaseDto::getId, Function.identity()));
            Map<ExamTypeDictionaryBaseDto, ExamTypeDictionarySimpleVo> dtoVoMap =
                    examTypeDictionarySimpleVoConverter.convertToExamTypeDictionarySimpleVoMap(
                            baseDtoList);
            Map<ExamTypeDictionaryBaseDto, ExamTypeDictionarySimpleVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.examType =
                    rootDtoList.stream()
                            .map(ExamItemBaseDto::getExamTypeId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "20db54fb-5016-3e3c-83e1-6f4a48392b77")
    public void collectDataDefault(ExamItemListVoDataHolder dataHolder) {
        examItemListVoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 获取ExamItemListDto数据填充ExamItemListVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "bb355101-7e49-3bc7-aaa8-a92d9029934a")
    public void collectDataWithDtoData(
            List<ExamItemListDto> dtoList, ExamItemListVoDataHolder dataHolder) {
        List<ClinicItemDictionaryBaseDto> clinicItemList = new ArrayList<>();
        List<ExamTypeDictionaryBaseDto> examTypeList = new ArrayList<>();

        for (ExamItemListDto rootDto : dtoList) {
            ClinicItemDictionaryBaseDto clinicItemDto = rootDto.getClinicItem();
            if (clinicItemDto != null) {
                clinicItemList.add(clinicItemDto);
            }
            ExamTypeDictionaryBaseDto examTypeDto = rootDto.getExamType();
            if (examTypeDto != null) {
                examTypeList.add(examTypeDto);
            }
        }

        // access clinicItem
        Map<ClinicItemDictionaryBaseDto, ClinicItemDictionaryVo> clinicItemVoMap =
                clinicItemDictionaryVoConverter.convertToClinicItemDictionaryVoMap(clinicItemList);
        dataHolder.clinicItem =
                clinicItemList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> clinicItemVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access examType
        Map<ExamTypeDictionaryBaseDto, ExamTypeDictionarySimpleVo> examTypeVoMap =
                examTypeDictionarySimpleVoConverter.convertToExamTypeDictionarySimpleVoMap(
                        examTypeList);
        dataHolder.examType =
                examTypeList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> examTypeVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }
}
