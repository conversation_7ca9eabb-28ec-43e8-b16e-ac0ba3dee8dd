package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.ClinicItemDictionaryBaseVo;
import com.pulse.dictionary_business.entrance.web.vo.ExclusionOrderTypeItemDetailClinicItemExtVo;
import com.pulse.dictionary_business.manager.dto.ClinicItemDictionaryBaseDto;
import com.pulse.dictionary_business.manager.dto.ExclusionOrderTypeItemDetailBaseDto;
import com.pulse.dictionary_business.service.ExclusionOrderTypeItemDetailBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** ExclusionOrderTypeItemDetailClinicItemExtVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "580ce557-f3f9-3876-8fd0-b3ef856f4340")
public class ExclusionOrderTypeItemDetailClinicItemExtVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private ExclusionOrderTypeItemDetailBaseDtoService exclusionOrderTypeItemDetailBaseDtoService;

    /** 组装ExclusionOrderTypeItemDetailClinicItemExtVo数据 */
    @AutoGenerated(locked = true, uuid = "3e3a81b4-1a4c-35a0-8cbb-e7d145dba9ab")
    public void assembleData(
            Map<String, ExclusionOrderTypeItemDetailClinicItemExtVo> voMap,
            ExclusionOrderTypeItemDetailClinicItemExtVoDataAssembler
                            .ExclusionOrderTypeItemDetailClinicItemExtVoDataHolder
                    dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<ExclusionOrderTypeItemDetailBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, Pair<ClinicItemDictionaryBaseDto, ClinicItemDictionaryBaseVo>> clinicItem =
                dataHolder.clinicItem.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getClinicItemId(),
                                        dto -> Pair.of(dto, dataHolder.clinicItem.get(dto)),
                                        (o1, o2) -> o1));

        for (ExclusionOrderTypeItemDetailBaseDto baseDto : baseDtoList) {
            ExclusionOrderTypeItemDetailClinicItemExtVo vo = voMap.get(baseDto.getId());
            vo.setClinicItem(
                    Optional.ofNullable(clinicItem.get(baseDto.getClinicItemId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装ExclusionOrderTypeItemDetailClinicItemExtVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "8c81b0d5-e6a5-3765-a39c-350ba5c0c037")
    public void assembleDataCustomized(List<ExclusionOrderTypeItemDetailClinicItemExtVo> dataList) {
        // 自定义数据组装

    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class ExclusionOrderTypeItemDetailClinicItemExtVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<ExclusionOrderTypeItemDetailBaseDto> rootBaseDtoList;

        /** 持有字段clinicItem的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<ClinicItemDictionaryBaseDto, ClinicItemDictionaryBaseVo> clinicItem;
    }
}
