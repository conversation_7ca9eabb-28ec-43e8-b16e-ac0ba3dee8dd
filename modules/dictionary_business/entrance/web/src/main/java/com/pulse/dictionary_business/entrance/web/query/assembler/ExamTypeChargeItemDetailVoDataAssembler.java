package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.billing_public_config.manager.dto.ChargeItemBaseDto;
import com.pulse.dictionary_business.entrance.web.vo.ChargeItemVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamTypeChargeItemDetailVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamTypeDictionaryVo;
import com.pulse.dictionary_business.manager.dto.ExamTypeChargeItemBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamTypeDictionaryBaseDto;
import com.pulse.dictionary_business.service.ExamTypeChargeItemBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** ExamTypeChargeItemDetailVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "2a902638-35ef-3b72-9091-07a1cfe69ef9")
public class ExamTypeChargeItemDetailVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeChargeItemBaseDtoService examTypeChargeItemBaseDtoService;

    /** 批量自定义组装ExamTypeChargeItemDetailVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "4c933e06-bb50-3a8e-a0c5-144375bf3cbe")
    public void assembleDataCustomized(List<ExamTypeChargeItemDetailVo> dataList) {
        // 自定义数据组装

    }

    /** 组装ExamTypeChargeItemDetailVo数据 */
    @AutoGenerated(locked = true, uuid = "a6c4b03d-6c03-36ab-b77e-624bf0ae04b9")
    public void assembleData(
            Map<String, ExamTypeChargeItemDetailVo> voMap,
            ExamTypeChargeItemDetailVoDataAssembler.ExamTypeChargeItemDetailVoDataHolder
                    dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<ExamTypeChargeItemBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, Pair<ChargeItemBaseDto, ChargeItemVo>> chargeItem =
                dataHolder.chargeItem.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getItemCode(),
                                        dto -> Pair.of(dto, dataHolder.chargeItem.get(dto)),
                                        (o1, o2) -> o1));
        Map<String, Pair<ExamTypeDictionaryBaseDto, ExamTypeDictionaryVo>> examType =
                dataHolder.examType.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getId(),
                                        dto -> Pair.of(dto, dataHolder.examType.get(dto)),
                                        (o1, o2) -> o1));

        for (ExamTypeChargeItemBaseDto baseDto : baseDtoList) {
            ExamTypeChargeItemDetailVo vo = voMap.get(baseDto.getId());
            vo.setChargeItem(
                    Optional.ofNullable(chargeItem.get(baseDto.getChargeItemId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
            vo.setExamType(
                    Optional.ofNullable(examType.get(baseDto.getExamTypeId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class ExamTypeChargeItemDetailVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<ExamTypeChargeItemBaseDto> rootBaseDtoList;

        /** 持有字段chargeItem的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<ChargeItemBaseDto, ChargeItemVo> chargeItem;

        /** 持有字段examType的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<ExamTypeDictionaryBaseDto, ExamTypeDictionaryVo> examType;
    }
}
