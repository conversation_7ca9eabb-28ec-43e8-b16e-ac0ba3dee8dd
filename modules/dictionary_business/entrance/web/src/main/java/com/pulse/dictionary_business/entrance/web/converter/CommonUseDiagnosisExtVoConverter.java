package com.pulse.dictionary_business.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.entrance.web.query.assembler.CommonUseDiagnosisExtVoDataAssembler;
import com.pulse.dictionary_business.entrance.web.query.assembler.CommonUseDiagnosisExtVoDataAssembler.CommonUseDiagnosisExtVoDataHolder;
import com.pulse.dictionary_business.entrance.web.query.collector.CommonUseDiagnosisExtVoDataCollector;
import com.pulse.dictionary_business.entrance.web.vo.CommonUseDiagnosisExtVo;
import com.pulse.dictionary_business.entrance.web.vo.DiagnosisDictionaryVo;
import com.pulse.dictionary_business.manager.dto.CommonUseDiagnosisExtDto;
import com.pulse.dictionary_business.manager.dto.DiagnosisDictionaryBaseDto;
import com.pulse.dictionary_business.service.CommonUseDiagnosisBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到CommonUseDiagnosisExtVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "54010d84-84fd-4609-aa5e-62fc47cb95d9|VO|CONVERTER")
public class CommonUseDiagnosisExtVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private CommonUseDiagnosisBaseDtoService commonUseDiagnosisBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private CommonUseDiagnosisExtVoDataAssembler commonUseDiagnosisExtVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private CommonUseDiagnosisExtVoDataCollector commonUseDiagnosisExtVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private DiagnosisDictionaryVoConverter diagnosisDictionaryVoConverter;

    /** 把CommonUseDiagnosisExtDto转换成CommonUseDiagnosisExtVo */
    @AutoGenerated(locked = false, uuid = "54010d84-84fd-4609-aa5e-62fc47cb95d9-converter-Map")
    public Map<CommonUseDiagnosisExtDto, CommonUseDiagnosisExtVo>
            convertToCommonUseDiagnosisExtVoMap(List<CommonUseDiagnosisExtDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<DiagnosisDictionaryBaseDto, DiagnosisDictionaryVo> diagnosisMap =
                diagnosisDictionaryVoConverter.convertToDiagnosisDictionaryVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(CommonUseDiagnosisExtDto::getDiagnosis)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<CommonUseDiagnosisExtDto, CommonUseDiagnosisExtVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            CommonUseDiagnosisExtVo vo =
                                                    new CommonUseDiagnosisExtVo();
                                            vo.setId(dto.getId());
                                            vo.setCommonDiagnosisUseScope(
                                                    dto.getCommonDiagnosisUseScope());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setDiagnosis(
                                                    dto.getDiagnosis() == null
                                                            ? null
                                                            : diagnosisMap.get(dto.getDiagnosis()));
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setUserId(dto.getUserId());
                                            vo.setOrganizationId(dto.getOrganizationId());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把CommonUseDiagnosisExtDto转换成CommonUseDiagnosisExtVo */
    @AutoGenerated(locked = true, uuid = "54010d84-84fd-4609-aa5e-62fc47cb95d9-converter-list")
    public List<CommonUseDiagnosisExtVo> convertToCommonUseDiagnosisExtVoList(
            List<CommonUseDiagnosisExtDto> dtoList) {
        return new ArrayList<>(convertToCommonUseDiagnosisExtVoMap(dtoList).values());
    }

    /** 把CommonUseDiagnosisExtDto转换成CommonUseDiagnosisExtVo */
    @AutoGenerated(locked = true, uuid = "5fa6dd78-79fa-31af-8fce-a04c2b96c05e")
    public CommonUseDiagnosisExtVo convertToCommonUseDiagnosisExtVo(CommonUseDiagnosisExtDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToCommonUseDiagnosisExtVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装CommonUseDiagnosisExtVo数据 */
    @AutoGenerated(locked = true, uuid = "6bafa79a-9f29-3165-804b-a003a3448f55")
    public CommonUseDiagnosisExtVo convertAndAssembleData(CommonUseDiagnosisExtDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装CommonUseDiagnosisExtVo列表数据 */
    @AutoGenerated(locked = true, uuid = "71fb482f-7dda-392f-aa97-fd5ede633bdb")
    public List<CommonUseDiagnosisExtVo> convertAndAssembleDataList(
            List<CommonUseDiagnosisExtDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        CommonUseDiagnosisExtVoDataHolder dataHolder = new CommonUseDiagnosisExtVoDataHolder();
        dataHolder.setRootBaseDtoList(
                commonUseDiagnosisBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(CommonUseDiagnosisExtDto::getId)
                                .collect(Collectors.toList())));
        Map<String, CommonUseDiagnosisExtVo> voMap =
                convertToCommonUseDiagnosisExtVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        commonUseDiagnosisExtVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        commonUseDiagnosisExtVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
