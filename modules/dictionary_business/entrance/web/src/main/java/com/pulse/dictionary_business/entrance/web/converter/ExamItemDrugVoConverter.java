package com.pulse.dictionary_business.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.entrance.web.query.assembler.ExamItemDrugVoDataAssembler;
import com.pulse.dictionary_business.entrance.web.vo.ExamItemDrugVo;
import com.pulse.dictionary_business.manager.dto.ExamItemDrugBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到ExamItemDrugVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "197d71de-58c2-4ef7-9337-51f298b03ec3|VO|CONVERTER")
public class ExamItemDrugVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private ExamItemDrugVoDataAssembler examItemDrugVoDataAssembler;

    /** 把ExamItemDrugBaseDto转换成ExamItemDrugVo */
    @AutoGenerated(locked = false, uuid = "197d71de-58c2-4ef7-9337-51f298b03ec3-converter-Map")
    public Map<ExamItemDrugBaseDto, ExamItemDrugVo> convertToExamItemDrugVoMap(
            List<ExamItemDrugBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<ExamItemDrugBaseDto, ExamItemDrugVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            ExamItemDrugVo vo = new ExamItemDrugVo();
                                            vo.setId(dto.getId());
                                            vo.setExamItemId(dto.getExamItemId());
                                            vo.setPriceItemId(dto.getPriceItemId());
                                            vo.setPriceItemType(dto.getPriceItemType());
                                            vo.setCount(dto.getCount());
                                            vo.setCampusIdList(dto.getCampusIdList());
                                            vo.setAdministration(dto.getAdministration());
                                            vo.setDepartmentDrugFlag(dto.getDepartmentDrugFlag());
                                            vo.setUseScope(dto.getUseScopeList());
                                            vo.setContrastAgentType(dto.getContrastAgentType());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把ExamItemDrugBaseDto转换成ExamItemDrugVo */
    @AutoGenerated(locked = true, uuid = "197d71de-58c2-4ef7-9337-51f298b03ec3-converter-list")
    public List<ExamItemDrugVo> convertToExamItemDrugVoList(List<ExamItemDrugBaseDto> dtoList) {
        return new ArrayList<>(convertToExamItemDrugVoMap(dtoList).values());
    }

    /** 把ExamItemDrugBaseDto转换成ExamItemDrugVo */
    @AutoGenerated(locked = true, uuid = "8477093b-7e55-3032-b2a5-89a43c029903")
    public ExamItemDrugVo convertToExamItemDrugVo(ExamItemDrugBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToExamItemDrugVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装ExamItemDrugVo数据 */
    @AutoGenerated(locked = true, uuid = "9be21468-8197-32b1-a373-b5bb73615d7a")
    public ExamItemDrugVo convertAndAssembleData(ExamItemDrugBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装ExamItemDrugVo列表数据 */
    @AutoGenerated(locked = true, uuid = "fa1c3f80-9531-3dfb-820f-ffeebe00989c")
    public List<ExamItemDrugVo> convertAndAssembleDataList(List<ExamItemDrugBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, ExamItemDrugVo> voMap =
                convertToExamItemDrugVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        examItemDrugVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
