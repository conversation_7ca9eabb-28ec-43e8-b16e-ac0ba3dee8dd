package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.ChargeItemClinicItemVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** ChargeItemClinicItemVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "c43b0ded-551a-3c7f-9619-09ec2f5abffd")
public class ChargeItemClinicItemVoDataAssembler {

    /** 批量自定义组装ChargeItemClinicItemVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "8c29d2b4-e05d-3333-808a-4891846942ef")
    public void assembleDataCustomized(List<ChargeItemClinicItemVo> dataList) {
        // 自定义数据组装

    }

    /** 组装ChargeItemClinicItemVo数据 */
    @AutoGenerated(locked = true, uuid = "9f578a8e-214e-3ccf-8729-6290ed1faa38")
    public void assembleData(Map<String, ChargeItemClinicItemVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }
}
