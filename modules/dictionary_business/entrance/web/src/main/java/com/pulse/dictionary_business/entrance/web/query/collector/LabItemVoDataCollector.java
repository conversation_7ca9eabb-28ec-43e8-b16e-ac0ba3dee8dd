package com.pulse.dictionary_business.entrance.web.query.collector;

import com.pulse.dictionary_business.entrance.web.converter.ClinicItemDictionarySimpleVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.LabClassDictionarySimpleVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.LabItemVoConverter;
import com.pulse.dictionary_business.entrance.web.query.assembler.LabItemVoDataAssembler.LabItemVoDataHolder;
import com.pulse.dictionary_business.entrance.web.vo.ClinicItemDictionarySimpleVo;
import com.pulse.dictionary_business.entrance.web.vo.LabClassDictionarySimpleVo;
import com.pulse.dictionary_business.manager.dto.ClinicItemDictionaryBaseDto;
import com.pulse.dictionary_business.manager.dto.LabClassDictionaryBaseDto;
import com.pulse.dictionary_business.manager.dto.LabItemBaseDto;
import com.pulse.dictionary_business.manager.dto.LabItemDto;
import com.pulse.dictionary_business.service.ClinicItemDictionaryBaseDtoService;
import com.pulse.dictionary_business.service.LabClassDictionaryBaseDtoService;
import com.pulse.dictionary_business.service.LabItemBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装LabItemVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "8c068cfc-89fd-3b0a-9a39-8300aced842d")
public class LabItemVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemDictionaryBaseDtoService clinicItemDictionaryBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemDictionarySimpleVoConverter clinicItemDictionarySimpleVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private LabClassDictionaryBaseDtoService labClassDictionaryBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private LabClassDictionarySimpleVoConverter labClassDictionarySimpleVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private LabItemBaseDtoService labItemBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private LabItemVoConverter labItemVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private LabItemVoDataCollector labItemVoDataCollector;

    /** 获取LabItemDto数据填充LabItemVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "3dc923e3-84ec-37ca-b1a2-6825d31e6561")
    public void collectDataWithDtoData(List<LabItemDto> dtoList, LabItemVoDataHolder dataHolder) {
        List<ClinicItemDictionaryBaseDto> clinicItemList = new ArrayList<>();
        List<LabClassDictionaryBaseDto> labClassList = new ArrayList<>();

        for (LabItemDto rootDto : dtoList) {
            ClinicItemDictionaryBaseDto clinicItemDto = rootDto.getClinicItem();
            if (clinicItemDto != null) {
                clinicItemList.add(clinicItemDto);
            }
            LabClassDictionaryBaseDto labClassDto = rootDto.getLabClass();
            if (labClassDto != null) {
                labClassList.add(labClassDto);
            }
        }

        // access clinicItem
        Map<ClinicItemDictionaryBaseDto, ClinicItemDictionarySimpleVo> clinicItemVoMap =
                clinicItemDictionarySimpleVoConverter.convertToClinicItemDictionarySimpleVoMap(
                        clinicItemList);
        dataHolder.clinicItem =
                clinicItemList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> clinicItemVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access labClass
        Map<LabClassDictionaryBaseDto, LabClassDictionarySimpleVo> labClassVoMap =
                labClassDictionarySimpleVoConverter.convertToLabClassDictionarySimpleVoMap(
                        labClassList);
        dataHolder.labClass =
                labClassList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> labClassVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "b9f658f1-0fc0-3d42-8857-81ec282db34b")
    private void fillDataWhenNecessary(LabItemVoDataHolder dataHolder) {
        List<LabItemBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.clinicItem == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(LabItemBaseDto::getClinicItemId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ClinicItemDictionaryBaseDto> baseDtoList =
                    clinicItemDictionaryBaseDtoService
                            .getByClinicItemIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(
                                    Comparator.comparing(
                                            ClinicItemDictionaryBaseDto::getClinicItemId))
                            .collect(Collectors.toList());
            Map<String, ClinicItemDictionaryBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            ClinicItemDictionaryBaseDto::getClinicItemId,
                                            Function.identity()));
            Map<ClinicItemDictionaryBaseDto, ClinicItemDictionarySimpleVo> dtoVoMap =
                    clinicItemDictionarySimpleVoConverter.convertToClinicItemDictionarySimpleVoMap(
                            baseDtoList);
            Map<ClinicItemDictionaryBaseDto, ClinicItemDictionarySimpleVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.clinicItem =
                    rootDtoList.stream()
                            .map(LabItemBaseDto::getClinicItemId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.labClass == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(LabItemBaseDto::getLabClassId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<LabClassDictionaryBaseDto> baseDtoList =
                    labClassDictionaryBaseDtoService
                            .getByLabClassCodes(new ArrayList<>(ids))
                            .stream()
                            .sorted(
                                    Comparator.comparing(
                                            LabClassDictionaryBaseDto::getLabClassCode))
                            .collect(Collectors.toList());
            Map<String, LabClassDictionaryBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            LabClassDictionaryBaseDto::getLabClassCode,
                                            Function.identity()));
            Map<LabClassDictionaryBaseDto, LabClassDictionarySimpleVo> dtoVoMap =
                    labClassDictionarySimpleVoConverter.convertToLabClassDictionarySimpleVoMap(
                            baseDtoList);
            Map<LabClassDictionaryBaseDto, LabClassDictionarySimpleVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.labClass =
                    rootDtoList.stream()
                            .map(LabItemBaseDto::getLabClassId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "fe800eff-6190-3381-9af5-b4ef30df3bcb")
    public void collectDataDefault(LabItemVoDataHolder dataHolder) {
        labItemVoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
