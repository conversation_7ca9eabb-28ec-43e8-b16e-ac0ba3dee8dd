package com.pulse.dictionary_business.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.entrance.web.query.assembler.ExamItemRuleDetailVoDataAssembler;
import com.pulse.dictionary_business.entrance.web.query.assembler.ExamItemRuleDetailVoDataAssembler.ExamItemRuleDetailVoDataHolder;
import com.pulse.dictionary_business.entrance.web.query.collector.ExamItemRuleDetailVoDataCollector;
import com.pulse.dictionary_business.entrance.web.vo.ClinicItemDictionaryBaseVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamItemDocumentTemplateVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamItemRuleDetailVo;
import com.pulse.dictionary_business.manager.dto.ClinicItemDictionaryBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamItemDocumentTemplateBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamItemRuleDetailDto;
import com.pulse.dictionary_business.service.ExamItemBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到ExamItemRuleDetailVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "763215f0-a0c7-4322-853f-538cc54de12f|VO|CONVERTER")
public class ExamItemRuleDetailVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemDictionaryBaseVoConverter clinicItemDictionaryBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ExamItemBaseDtoService examItemBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ExamItemDocumentTemplateVoConverter examItemDocumentTemplateVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ExamItemRuleDetailVoDataAssembler examItemRuleDetailVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private ExamItemRuleDetailVoDataCollector examItemRuleDetailVoDataCollector;

    /** 使用默认方式组装ExamItemRuleDetailVo数据 */
    @AutoGenerated(locked = true, uuid = "00973f64-ba36-3da7-9043-b652005b9426")
    public ExamItemRuleDetailVo convertAndAssembleData(ExamItemRuleDetailDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把ExamItemRuleDetailDto转换成ExamItemRuleDetailVo */
    @AutoGenerated(locked = false, uuid = "763215f0-a0c7-4322-853f-538cc54de12f-converter-Map")
    public Map<ExamItemRuleDetailDto, ExamItemRuleDetailVo> convertToExamItemRuleDetailVoMap(
            List<ExamItemRuleDetailDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<ClinicItemDictionaryBaseDto, ClinicItemDictionaryBaseVo> clinicItemMap =
                clinicItemDictionaryBaseVoConverter.convertToClinicItemDictionaryBaseVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(ExamItemRuleDetailDto::getClinicItem)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<ExamItemDocumentTemplateBaseDto, ExamItemDocumentTemplateVo>
                examItemDocumentTemplateMap =
                        examItemDocumentTemplateVoConverter.convertToExamItemDocumentTemplateVoMap(
                                dtoList.stream()
                                        .filter(Objects::nonNull)
                                        .map(ExamItemRuleDetailDto::getExamItemDocumentTemplate)
                                        .filter(Objects::nonNull)
                                        .collect(Collectors.toList()));
        Map<ExamItemRuleDetailDto, ExamItemRuleDetailVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            ExamItemRuleDetailVo vo = new ExamItemRuleDetailVo();
                                            vo.setId(dto.getId());
                                            vo.setExamCatalogId(dto.getExamCatalogId());
                                            vo.setClinicItem(
                                                    dto.getClinicItem() == null
                                                            ? null
                                                            : clinicItemMap.get(
                                                                    dto.getClinicItem()));
                                            vo.setEmptyStomachFlag(dto.getEmptyStomachFlag());
                                            vo.setBedsideFlag(dto.getBedsideFlag());
                                            vo.setExamDirection(dto.getExamDirectionList());
                                            vo.setPartNumber(dto.getPartNumber());
                                            vo.setUclaFlag(dto.getUclaFlag());
                                            vo.setLimbPositionCode(dto.getLimbPositionCodeList());
                                            vo.setAppointType(dto.getAppointType());
                                            vo.setIodineContrastFlag(dto.getIodineContrastFlag());
                                            vo.setGadoliniumContrastFlag(
                                                    dto.getGadoliniumContrastFlag());
                                            vo.setInternetEnableFlag(dto.getInternetEnableFlag());
                                            vo.setSelfServiceOrderFlag(
                                                    dto.getSelfServiceOrderFlag());
                                            vo.setExamTypeId(dto.getExamTypeId());
                                            vo.setUrgentType(dto.getUrgentType());
                                            vo.setUnitWeight(dto.getUnitWeight());
                                            vo.setPromptContent(dto.getPromptContent());
                                            vo.setNotAllowedDepartmentIdList(
                                                    dto.getNotAllowedDepartmentIdList());
                                            vo.setAllowedStaffIdList(dto.getAllowedStaffIdList());
                                            vo.setExamItemDocumentTemplate(
                                                    dto.getExamItemDocumentTemplate() == null
                                                            ? null
                                                            : examItemDocumentTemplateMap.get(
                                                                    dto
                                                                            .getExamItemDocumentTemplate()));
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把ExamItemRuleDetailDto转换成ExamItemRuleDetailVo */
    @AutoGenerated(locked = true, uuid = "763215f0-a0c7-4322-853f-538cc54de12f-converter-list")
    public List<ExamItemRuleDetailVo> convertToExamItemRuleDetailVoList(
            List<ExamItemRuleDetailDto> dtoList) {
        return new ArrayList<>(convertToExamItemRuleDetailVoMap(dtoList).values());
    }

    /** 把ExamItemRuleDetailDto转换成ExamItemRuleDetailVo */
    @AutoGenerated(locked = true, uuid = "82643edd-c1fa-3495-89a0-acedaaef91f3")
    public ExamItemRuleDetailVo convertToExamItemRuleDetailVo(ExamItemRuleDetailDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToExamItemRuleDetailVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装ExamItemRuleDetailVo列表数据 */
    @AutoGenerated(locked = true, uuid = "c5419c12-475c-388a-9d74-c31c9dee8910")
    public List<ExamItemRuleDetailVo> convertAndAssembleDataList(
            List<ExamItemRuleDetailDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        ExamItemRuleDetailVoDataHolder dataHolder = new ExamItemRuleDetailVoDataHolder();
        dataHolder.setRootBaseDtoList(
                examItemBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(ExamItemRuleDetailDto::getId)
                                .collect(Collectors.toList())));
        Map<String, ExamItemRuleDetailVo> voMap =
                convertToExamItemRuleDetailVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        examItemRuleDetailVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        examItemRuleDetailVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
