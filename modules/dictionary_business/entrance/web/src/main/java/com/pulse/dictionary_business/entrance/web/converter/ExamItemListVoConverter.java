package com.pulse.dictionary_business.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.entrance.web.query.assembler.ExamItemListVoDataAssembler;
import com.pulse.dictionary_business.entrance.web.query.assembler.ExamItemListVoDataAssembler.ExamItemListVoDataHolder;
import com.pulse.dictionary_business.entrance.web.query.collector.ExamItemListVoDataCollector;
import com.pulse.dictionary_business.entrance.web.vo.ClinicItemDictionaryVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamItemListVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamTypeDictionarySimpleVo;
import com.pulse.dictionary_business.manager.dto.ClinicItemDictionaryBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamItemListDto;
import com.pulse.dictionary_business.manager.dto.ExamTypeDictionaryBaseDto;
import com.pulse.dictionary_business.service.ExamItemBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到ExamItemListVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "678db8f3-2134-4e46-9bbc-cb3ad91a301e|VO|CONVERTER")
public class ExamItemListVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemDictionaryVoConverter clinicItemDictionaryVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ExamItemBaseDtoService examItemBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ExamItemListVoDataAssembler examItemListVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private ExamItemListVoDataCollector examItemListVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeDictionarySimpleVoConverter examTypeDictionarySimpleVoConverter;

    /** 使用默认方式组装ExamItemListVo列表数据 */
    @AutoGenerated(locked = true, uuid = "06048424-56ba-3105-9705-bc658fd57a1b")
    public List<ExamItemListVo> convertAndAssembleDataList(List<ExamItemListDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        ExamItemListVoDataHolder dataHolder = new ExamItemListVoDataHolder();
        dataHolder.setRootBaseDtoList(
                examItemBaseDtoService.getByIds(
                        dtoList.stream().map(ExamItemListDto::getId).collect(Collectors.toList())));
        Map<String, ExamItemListVo> voMap =
                convertToExamItemListVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        examItemListVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        examItemListVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把ExamItemListDto转换成ExamItemListVo */
    @AutoGenerated(locked = true, uuid = "1a47661f-1ec1-3def-9a66-6762493a90bd")
    public ExamItemListVo convertToExamItemListVo(ExamItemListDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToExamItemListVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把ExamItemListDto转换成ExamItemListVo */
    @AutoGenerated(locked = false, uuid = "678db8f3-2134-4e46-9bbc-cb3ad91a301e-converter-Map")
    public Map<ExamItemListDto, ExamItemListVo> convertToExamItemListVoMap(
            List<ExamItemListDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<ClinicItemDictionaryBaseDto, ClinicItemDictionaryVo> clinicItemMap =
                clinicItemDictionaryVoConverter.convertToClinicItemDictionaryVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(ExamItemListDto::getClinicItem)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<ExamTypeDictionaryBaseDto, ExamTypeDictionarySimpleVo> examTypeMap =
                examTypeDictionarySimpleVoConverter.convertToExamTypeDictionarySimpleVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(ExamItemListDto::getExamType)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<ExamItemListDto, ExamItemListVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            ExamItemListVo vo = new ExamItemListVo();
                                            vo.setId(dto.getId());
                                            vo.setExamCatalogId(dto.getExamCatalogId());
                                            vo.setClinicItem(
                                                    dto.getClinicItem() == null
                                                            ? null
                                                            : clinicItemMap.get(
                                                                    dto.getClinicItem()));
                                            vo.setSortNumber(dto.getSortNumber());
                                            vo.setPartNumber(dto.getPartNumber());
                                            vo.setExamType(
                                                    dto.getExamType() == null
                                                            ? null
                                                            : examTypeMap.get(dto.getExamType()));
                                            vo.setEnhancedScanningFlag(
                                                    dto.getEnhancedScanningFlag());
                                            vo.setPathologyType(dto.getPathologyType());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把ExamItemListDto转换成ExamItemListVo */
    @AutoGenerated(locked = true, uuid = "678db8f3-2134-4e46-9bbc-cb3ad91a301e-converter-list")
    public List<ExamItemListVo> convertToExamItemListVoList(List<ExamItemListDto> dtoList) {
        return new ArrayList<>(convertToExamItemListVoMap(dtoList).values());
    }

    /** 使用默认方式组装ExamItemListVo数据 */
    @AutoGenerated(locked = true, uuid = "b4ccf858-3bd3-3859-8cf4-233cd6050716")
    public ExamItemListVo convertAndAssembleData(ExamItemListDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }
}
