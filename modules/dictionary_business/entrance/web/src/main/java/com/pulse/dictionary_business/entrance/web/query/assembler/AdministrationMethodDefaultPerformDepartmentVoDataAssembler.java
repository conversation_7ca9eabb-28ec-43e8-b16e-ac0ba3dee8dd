package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.AdministrationMethodDefaultPerformDepartmentVo;
import com.pulse.dictionary_business.entrance.web.vo.DictionaryBusinessRefOrganizationVo;
import com.pulse.dictionary_business.manager.dto.AdministrationMethodDefaultPerformDepartmentBaseDto;
import com.pulse.dictionary_business.service.AdministrationMethodDefaultPerformDepartmentBaseDtoService;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** AdministrationMethodDefaultPerformDepartmentVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "89001afe-6bdb-3994-afac-0c5aa3717c13")
public class AdministrationMethodDefaultPerformDepartmentVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private AdministrationMethodDefaultPerformDepartmentBaseDtoService
            administrationMethodDefaultPerformDepartmentBaseDtoService;

    /** 批量自定义组装AdministrationMethodDefaultPerformDepartmentVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "42087c86-7ca0-3a3e-b0cb-73503ba5e8a0")
    public void assembleDataCustomized(
            List<AdministrationMethodDefaultPerformDepartmentVo> dataList) {
        // 自定义数据组装

    }

    /** 组装AdministrationMethodDefaultPerformDepartmentVo数据 */
    @AutoGenerated(locked = true, uuid = "ff66cd75-996e-3012-a30c-4b83b89effd2")
    public void assembleData(
            Map<String, AdministrationMethodDefaultPerformDepartmentVo> voMap,
            AdministrationMethodDefaultPerformDepartmentVoDataAssembler
                            .AdministrationMethodDefaultPerformDepartmentVoDataHolder
                    dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<AdministrationMethodDefaultPerformDepartmentBaseDto> baseDtoList =
                dataHolder.getRootBaseDtoList();

        Map<String, Pair<OrganizationBaseDto, DictionaryBusinessRefOrganizationVo>>
                performDepartment =
                        dataHolder.performDepartment.keySet().stream()
                                .collect(
                                        Collectors.toMap(
                                                dto -> dto.getId(),
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder.performDepartment.get(
                                                                        dto)),
                                                (o1, o2) -> o1));

        for (AdministrationMethodDefaultPerformDepartmentBaseDto baseDto : baseDtoList) {
            AdministrationMethodDefaultPerformDepartmentVo vo = voMap.get(baseDto.getId());
            vo.setPerformDepartment(
                    Optional.ofNullable(performDepartment.get(baseDto.getPerformDepartmentId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class AdministrationMethodDefaultPerformDepartmentVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<AdministrationMethodDefaultPerformDepartmentBaseDto> rootBaseDtoList;

        /** 持有字段performDepartment的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<OrganizationBaseDto, DictionaryBusinessRefOrganizationVo> performDepartment;
    }
}
