package com.pulse.dictionary_business.entrance.web.query.collector;

import com.pulse.billing_public_config.manager.dto.ChargeItemBaseDto;
import com.pulse.billing_public_config.manager.dto.ChargeItemDto;
import com.pulse.dictionary_business.entrance.web.converter.ChargeItemVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.ExamDeviceChargeItemVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.ExamTypeDeviceVoConverter;
import com.pulse.dictionary_business.entrance.web.query.assembler.ExamDeviceChargeItemVoDataAssembler.ExamDeviceChargeItemVoDataHolder;
import com.pulse.dictionary_business.entrance.web.vo.ChargeItemVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamTypeDeviceVo;
import com.pulse.dictionary_business.manager.dto.ExamDeviceBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamDeviceChargeItemBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamDeviceChargeItemDto;
import com.pulse.dictionary_business.manager.facade.billing_public_config.ChargeItemBaseDtoServiceInDictionaryBusinessRpcAdapter;
import com.pulse.dictionary_business.manager.facade.billing_public_config.ChargeItemDtoServiceInDictionaryBusinessRpcAdapter;
import com.pulse.dictionary_business.service.ExamDeviceBaseDtoService;
import com.pulse.dictionary_business.service.ExamDeviceChargeItemBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装ExamDeviceChargeItemVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "5f4243a6-909b-3775-8067-f32f817c072b")
public class ExamDeviceChargeItemVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemBaseDtoServiceInDictionaryBusinessRpcAdapter
            chargeItemBaseDtoServiceInDictionaryBusinessRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemDtoServiceInDictionaryBusinessRpcAdapter
            chargeItemDtoServiceInDictionaryBusinessRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemVoConverter chargeItemVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ExamDeviceBaseDtoService examDeviceBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ExamDeviceChargeItemBaseDtoService examDeviceChargeItemBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ExamDeviceChargeItemVoConverter examDeviceChargeItemVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ExamDeviceChargeItemVoDataCollector examDeviceChargeItemVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeDeviceVoConverter examTypeDeviceVoConverter;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "*************-3b56-9185-66ab82c0c2d5")
    private void fillDataWhenNecessary(ExamDeviceChargeItemVoDataHolder dataHolder) {
        List<ExamDeviceChargeItemBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.chargeItem == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(ExamDeviceChargeItemBaseDto::getChargeItemId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ChargeItemBaseDto> baseDtoList =
                    chargeItemBaseDtoServiceInDictionaryBusinessRpcAdapter
                            .getByItemCodes(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(ChargeItemBaseDto::getItemCode))
                            .collect(Collectors.toList());
            Map<String, ChargeItemBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            ChargeItemBaseDto::getItemCode, Function.identity()));
            Map<String, ChargeItemDto> chargeItemDtoMap =
                    chargeItemDtoServiceInDictionaryBusinessRpcAdapter
                            .getByItemCodes(
                                    baseDtoList.stream()
                                            .map(ChargeItemBaseDto::getItemCode)
                                            .collect(Collectors.toList()))
                            .stream()
                            .collect(
                                    Collectors.toMap(
                                            ChargeItemDto::getItemCode, Function.identity()));
            Map<ChargeItemDto, ChargeItemVo> dtoVoMap =
                    chargeItemVoConverter.convertToChargeItemVoMap(
                            new ArrayList<>(chargeItemDtoMap.values()));
            Map<ChargeItemBaseDto, ChargeItemVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .filter(baseDto -> chargeItemDtoMap.containsKey(baseDto.getItemCode()))
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    dtoVoMap.get(
                                                            chargeItemDtoMap.get(
                                                                    baseDto.getItemCode())),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.chargeItem =
                    rootDtoList.stream()
                            .map(ExamDeviceChargeItemBaseDto::getChargeItemId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.examDevice == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(ExamDeviceChargeItemBaseDto::getExamDeviceId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ExamDeviceBaseDto> baseDtoList =
                    examDeviceBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(ExamDeviceBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, ExamDeviceBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            ExamDeviceBaseDto::getId, Function.identity()));
            Map<ExamDeviceBaseDto, ExamTypeDeviceVo> dtoVoMap =
                    examTypeDeviceVoConverter.convertToExamTypeDeviceVoMap(baseDtoList);
            Map<ExamDeviceBaseDto, ExamTypeDeviceVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.examDevice =
                    rootDtoList.stream()
                            .map(ExamDeviceChargeItemBaseDto::getExamDeviceId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** 获取ExamDeviceChargeItemDto数据填充ExamDeviceChargeItemVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "b23035d5-49bf-301d-bdc1-e6916a304605")
    public void collectDataWithDtoData(
            List<ExamDeviceChargeItemDto> dtoList, ExamDeviceChargeItemVoDataHolder dataHolder) {
        Map<ChargeItemBaseDto, ChargeItemDto> chargeItemBaseDtoDtoMap = new LinkedHashMap<>();
        List<ExamDeviceBaseDto> examDeviceList = new ArrayList<>();

        for (ExamDeviceChargeItemDto rootDto : dtoList) {
            ChargeItemDto chargeItemDto = rootDto.getChargeItem();
            if (chargeItemDto != null) {
                ChargeItemBaseDto chargeItemBaseDto =
                        chargeItemBaseDtoServiceInDictionaryBusinessRpcAdapter
                                .getByItemCodes(List.of(chargeItemDto.getItemCode()))
                                .stream()
                                .findAny()
                                .get();
                chargeItemBaseDtoDtoMap.put(chargeItemBaseDto, chargeItemDto);
            }
            ExamDeviceBaseDto examDeviceDto = rootDto.getExamDevice();
            if (examDeviceDto != null) {
                examDeviceList.add(examDeviceDto);
            }
        }

        // access chargeItem
        Map<ChargeItemDto, ChargeItemVo> chargeItemVoMap =
                chargeItemVoConverter.convertToChargeItemVoMap(
                        new ArrayList<>(chargeItemBaseDtoDtoMap.values()));
        dataHolder.chargeItem =
                chargeItemBaseDtoDtoMap.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                chargeItemVoMap.get(
                                                        chargeItemBaseDtoDtoMap.get(baseDto)),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access examDevice
        Map<ExamDeviceBaseDto, ExamTypeDeviceVo> examDeviceVoMap =
                examTypeDeviceVoConverter.convertToExamTypeDeviceVoMap(examDeviceList);
        dataHolder.examDevice =
                examDeviceList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> examDeviceVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "bee45065-**************-89a880f6e451")
    public void collectDataDefault(ExamDeviceChargeItemVoDataHolder dataHolder) {
        examDeviceChargeItemVoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
