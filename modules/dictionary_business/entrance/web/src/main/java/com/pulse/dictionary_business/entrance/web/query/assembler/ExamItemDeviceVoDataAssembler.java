package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.ExamItemDeviceVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** ExamItemDeviceVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "169a6fc1-cf1e-32ef-af9a-1aa5cdfedcf4")
public class ExamItemDeviceVoDataAssembler {

    /** 组装ExamItemDeviceVo数据 */
    @AutoGenerated(locked = true, uuid = "a810b046-4b42-3e62-b03b-78670d68e365")
    public void assembleData(Map<String, ExamItemDeviceVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装ExamItemDeviceVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "f40cc166-3d0b-377d-b374-a46e48f12998")
    public void assembleDataCustomized(List<ExamItemDeviceVo> dataList) {
        // 自定义数据组装

    }
}
