package com.pulse.dictionary_business.entrance.web.query.collector;

import com.pulse.billing_public_config.manager.dto.ChargeItemBaseDto;
import com.pulse.billing_public_config.manager.dto.ChargeItemClinicItemDto;
import com.pulse.dictionary_business.entrance.web.converter.ChargeItemClinicItemVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.ClinicItemChargeItemListVoConverter;
import com.pulse.dictionary_business.entrance.web.query.assembler.ClinicItemChargeItemListVoDataAssembler.ClinicItemChargeItemListVoDataHolder;
import com.pulse.dictionary_business.entrance.web.vo.ChargeItemClinicItemVo;
import com.pulse.dictionary_business.manager.dto.ClinicItemChargeItemBaseDto;
import com.pulse.dictionary_business.manager.dto.ClinicItemChargeItemListDto;
import com.pulse.dictionary_business.manager.facade.billing_public_config.ChargeItemBaseDtoServiceInDictionaryBusinessRpcAdapter;
import com.pulse.dictionary_business.manager.facade.billing_public_config.ChargeItemClinicItemDtoServiceInDictionaryBusinessRpcAdapter;
import com.pulse.dictionary_business.service.ClinicItemChargeItemBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装ClinicItemChargeItemListVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "c6d5b4d9-99eb-30ed-bf62-874a022f49d5")
public class ClinicItemChargeItemListVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemBaseDtoServiceInDictionaryBusinessRpcAdapter
            chargeItemBaseDtoServiceInDictionaryBusinessRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemClinicItemDtoServiceInDictionaryBusinessRpcAdapter
            chargeItemClinicItemDtoServiceInDictionaryBusinessRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemClinicItemVoConverter chargeItemClinicItemVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemChargeItemBaseDtoService clinicItemChargeItemBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemChargeItemListVoConverter clinicItemChargeItemListVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ClinicItemChargeItemListVoDataCollector clinicItemChargeItemListVoDataCollector;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "33be9a97-9863-361b-b70f-24ad311be39e")
    private void fillDataWhenNecessary(ClinicItemChargeItemListVoDataHolder dataHolder) {
        List<ClinicItemChargeItemBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.chargeItem == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(ClinicItemChargeItemBaseDto::getChargeItemId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ChargeItemBaseDto> baseDtoList =
                    chargeItemBaseDtoServiceInDictionaryBusinessRpcAdapter
                            .getByItemCodes(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(ChargeItemBaseDto::getItemCode))
                            .collect(Collectors.toList());
            Map<String, ChargeItemBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            ChargeItemBaseDto::getItemCode, Function.identity()));
            Map<String, ChargeItemClinicItemDto> chargeItemClinicItemDtoMap =
                    chargeItemClinicItemDtoServiceInDictionaryBusinessRpcAdapter
                            .getByItemCodes(
                                    baseDtoList.stream()
                                            .map(ChargeItemBaseDto::getItemCode)
                                            .collect(Collectors.toList()))
                            .stream()
                            .collect(
                                    Collectors.toMap(
                                            ChargeItemClinicItemDto::getItemCode,
                                            Function.identity()));
            Map<ChargeItemClinicItemDto, ChargeItemClinicItemVo> dtoVoMap =
                    chargeItemClinicItemVoConverter.convertToChargeItemClinicItemVoMap(
                            new ArrayList<>(chargeItemClinicItemDtoMap.values()));
            Map<ChargeItemBaseDto, ChargeItemClinicItemVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .filter(
                                    baseDto ->
                                            chargeItemClinicItemDtoMap.containsKey(
                                                    baseDto.getItemCode()))
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    dtoVoMap.get(
                                                            chargeItemClinicItemDtoMap.get(
                                                                    baseDto.getItemCode())),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.chargeItem =
                    rootDtoList.stream()
                            .map(ClinicItemChargeItemBaseDto::getChargeItemId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "6868f660-85f4-31b7-a0d8-d63d64162ce4")
    public void collectDataDefault(ClinicItemChargeItemListVoDataHolder dataHolder) {
        clinicItemChargeItemListVoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 获取ClinicItemChargeItemListDto数据填充ClinicItemChargeItemListVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "b7ff6f6e-3d13-30f2-9962-46c745181a2e")
    public void collectDataWithDtoData(
            List<ClinicItemChargeItemListDto> dtoList,
            ClinicItemChargeItemListVoDataHolder dataHolder) {
        Map<ChargeItemBaseDto, ChargeItemClinicItemDto> chargeItemBaseDtoDtoMap =
                new LinkedHashMap<>();

        for (ClinicItemChargeItemListDto rootDto : dtoList) {
            ChargeItemClinicItemDto chargeItemDto = rootDto.getChargeItem();
            if (chargeItemDto != null) {
                ChargeItemBaseDto chargeItemBaseDto =
                        chargeItemBaseDtoServiceInDictionaryBusinessRpcAdapter
                                .getByItemCodes(List.of(chargeItemDto.getItemCode()))
                                .stream()
                                .findAny()
                                .get();
                chargeItemBaseDtoDtoMap.put(chargeItemBaseDto, chargeItemDto);
            }
        }

        // access chargeItem
        Map<ChargeItemClinicItemDto, ChargeItemClinicItemVo> chargeItemVoMap =
                chargeItemClinicItemVoConverter.convertToChargeItemClinicItemVoMap(
                        new ArrayList<>(chargeItemBaseDtoDtoMap.values()));
        dataHolder.chargeItem =
                chargeItemBaseDtoDtoMap.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                chargeItemVoMap.get(
                                                        chargeItemBaseDtoDtoMap.get(baseDto)),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }
}
