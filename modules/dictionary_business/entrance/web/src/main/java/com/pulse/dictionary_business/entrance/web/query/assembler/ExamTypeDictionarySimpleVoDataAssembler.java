package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.ExamTypeDictionarySimpleVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** ExamTypeDictionarySimpleVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "15d17eff-9792-3b7e-a01e-8eb0b5bd6018")
public class ExamTypeDictionarySimpleVoDataAssembler {

    /** 组装ExamTypeDictionarySimpleVo数据 */
    @AutoGenerated(locked = true, uuid = "568de917-67a2-3095-8343-28f5a11ea3e3")
    public void assembleData(Map<String, ExamTypeDictionarySimpleVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装ExamTypeDictionarySimpleVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "a4dbfb7a-7131-3a1a-a92f-73f609cd53dd")
    public void assembleDataCustomized(List<ExamTypeDictionarySimpleVo> dataList) {
        // 自定义数据组装

    }
}
