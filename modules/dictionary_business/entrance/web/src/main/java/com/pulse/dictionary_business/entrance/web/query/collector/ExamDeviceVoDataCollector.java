package com.pulse.dictionary_business.entrance.web.query.collector;

import com.pulse.dictionary_business.entrance.web.converter.ExamDeviceVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.ExamTypeDictionaryVoConverter;
import com.pulse.dictionary_business.entrance.web.query.assembler.ExamDeviceVoDataAssembler.ExamDeviceVoDataHolder;
import com.pulse.dictionary_business.entrance.web.vo.ExamTypeDictionaryVo;
import com.pulse.dictionary_business.manager.dto.ExamDeviceBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamDeviceDto;
import com.pulse.dictionary_business.manager.dto.ExamTypeDictionaryBaseDto;
import com.pulse.dictionary_business.service.ExamDeviceBaseDtoService;
import com.pulse.dictionary_business.service.ExamTypeDictionaryBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装ExamDeviceVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "0b2b3095-a733-3ca2-8823-055d2b817227")
public class ExamDeviceVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private ExamDeviceBaseDtoService examDeviceBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ExamDeviceVoConverter examDeviceVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ExamDeviceVoDataCollector examDeviceVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeDictionaryBaseDtoService examTypeDictionaryBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeDictionaryVoConverter examTypeDictionaryVoConverter;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "4a38fdbb-0bd4-3454-a29c-4160b05cbcd4")
    private void fillDataWhenNecessary(ExamDeviceVoDataHolder dataHolder) {
        List<ExamDeviceBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.examType == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(ExamDeviceBaseDto::getExamTypeId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ExamTypeDictionaryBaseDto> baseDtoList =
                    examTypeDictionaryBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(ExamTypeDictionaryBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, ExamTypeDictionaryBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            ExamTypeDictionaryBaseDto::getId, Function.identity()));
            Map<ExamTypeDictionaryBaseDto, ExamTypeDictionaryVo> dtoVoMap =
                    examTypeDictionaryVoConverter.convertToExamTypeDictionaryVoMap(baseDtoList);
            Map<ExamTypeDictionaryBaseDto, ExamTypeDictionaryVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.examType =
                    rootDtoList.stream()
                            .map(ExamDeviceBaseDto::getExamTypeId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** 获取ExamDeviceDto数据填充ExamDeviceVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "7f059d61-c642-3d7d-a0fb-635392ed8d4e")
    public void collectDataWithDtoData(
            List<ExamDeviceDto> dtoList, ExamDeviceVoDataHolder dataHolder) {
        List<ExamTypeDictionaryBaseDto> examTypeList = new ArrayList<>();

        for (ExamDeviceDto rootDto : dtoList) {
            ExamTypeDictionaryBaseDto examTypeDto = rootDto.getExamType();
            if (examTypeDto != null) {
                examTypeList.add(examTypeDto);
            }
        }

        // access examType
        Map<ExamTypeDictionaryBaseDto, ExamTypeDictionaryVo> examTypeVoMap =
                examTypeDictionaryVoConverter.convertToExamTypeDictionaryVoMap(examTypeList);
        dataHolder.examType =
                examTypeList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> examTypeVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "dffe93a8-d19a-3173-8414-a590af3ec231")
    public void collectDataDefault(ExamDeviceVoDataHolder dataHolder) {
        examDeviceVoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
