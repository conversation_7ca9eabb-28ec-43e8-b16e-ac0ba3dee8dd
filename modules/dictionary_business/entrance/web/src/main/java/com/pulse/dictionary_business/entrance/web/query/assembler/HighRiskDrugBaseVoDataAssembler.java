package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.HighRiskDrugBaseVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** HighRiskDrugBaseVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "9799a6d5-9140-3bb5-b55b-94ba300681f3")
public class HighRiskDrugBaseVoDataAssembler {

    /** 组装HighRiskDrugBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "22b35aa2-0253-35f4-ab5a-9e5d6de9ed51")
    public void assembleData(Map<String, HighRiskDrugBaseVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装HighRiskDrugBaseVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "c6474358-15bd-32cb-94eb-750ec71151b7")
    public void assembleDataCustomized(List<HighRiskDrugBaseVo> dataList) {
        // 自定义数据组装

    }
}
