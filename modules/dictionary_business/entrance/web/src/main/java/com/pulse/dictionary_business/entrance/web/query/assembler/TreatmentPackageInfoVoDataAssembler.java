package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.TreatmentPackageInfoVo;
import com.pulse.dictionary_business.entrance.web.vo.TreatmentPackageItemVo;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageInfoDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageItemDto;
import com.pulse.dictionary_business.service.TreatmentPackageInfoDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** TreatmentPackageInfoVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "002825d7-d34b-3cb9-8249-58010ff1811c")
public class TreatmentPackageInfoVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageInfoDtoService treatmentPackageInfoDtoService;

    /** 批量自定义组装TreatmentPackageInfoVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "672257b5-b1c4-30f4-b76a-fb72f9afa58d")
    public void assembleDataCustomized(List<TreatmentPackageInfoVo> dataList) {
        // 自定义数据组装

    }

    /** 组装TreatmentPackageInfoVo数据 */
    @AutoGenerated(locked = true, uuid = "a68f75dd-f8cc-32c1-8497-19328b889013")
    public void assembleData(
            Map<String, TreatmentPackageInfoVo> voMap,
            TreatmentPackageInfoVoDataAssembler.TreatmentPackageInfoVoDataHolder dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<TreatmentPackageInfoDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, List<Pair<TreatmentPackageItemDto, TreatmentPackageItemVo>>>
                treatmentPackageItemList =
                        dataHolder.treatmentPackageItemList.keySet().stream()
                                .collect(
                                        Collectors.groupingBy(
                                                dto -> dto.getTreatmentPackageId(),
                                                Collectors.mapping(
                                                        dto ->
                                                                Pair.of(
                                                                        dto,
                                                                        dataHolder
                                                                                .treatmentPackageItemList
                                                                                .get(dto)),
                                                        Collectors.toCollection(ArrayList::new))));

        for (TreatmentPackageInfoDto baseDto : baseDtoList) {
            TreatmentPackageInfoVo vo = voMap.get(baseDto.getId());
            vo.setTreatmentPackageItemList(
                    Optional.ofNullable(treatmentPackageItemList.get(baseDto.getId()))
                            .map(
                                    tmp ->
                                            tmp.stream()
                                                    .map(pair -> pair.getRight())
                                                    .collect(Collectors.toList()))
                            .orElse(new ArrayList<>()));
        }

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class TreatmentPackageInfoVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<TreatmentPackageInfoDto> rootBaseDtoList;

        /** 持有字段treatmentPackageItemList的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<TreatmentPackageItemDto, TreatmentPackageItemVo> treatmentPackageItemList;
    }
}
