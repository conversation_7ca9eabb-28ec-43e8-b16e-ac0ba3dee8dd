package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.ClinicItemDictionaryBaseVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamItemDocumentTemplateVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamItemRuleDetailVo;
import com.pulse.dictionary_business.manager.dto.ClinicItemDictionaryBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamItemBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamItemDocumentTemplateBaseDto;
import com.pulse.dictionary_business.service.ExamItemBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** ExamItemRuleDetailVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "811b12a1-eee9-3194-8d93-40e9c14ab488")
public class ExamItemRuleDetailVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private ExamItemBaseDtoService examItemBaseDtoService;

    /** 批量自定义组装ExamItemRuleDetailVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "5d0e2107-c4ac-3e45-b825-65308e149c94")
    public void assembleDataCustomized(List<ExamItemRuleDetailVo> dataList) {
        // 自定义数据组装

    }

    /** 组装ExamItemRuleDetailVo数据 */
    @AutoGenerated(locked = true, uuid = "c9488d77-668c-3b65-8a5a-f367d6964001")
    public void assembleData(
            Map<String, ExamItemRuleDetailVo> voMap,
            ExamItemRuleDetailVoDataAssembler.ExamItemRuleDetailVoDataHolder dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<ExamItemBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, Pair<ClinicItemDictionaryBaseDto, ClinicItemDictionaryBaseVo>> clinicItem =
                dataHolder.clinicItem.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getClinicItemId(),
                                        dto -> Pair.of(dto, dataHolder.clinicItem.get(dto)),
                                        (o1, o2) -> o1));
        Map<String, Pair<ExamItemDocumentTemplateBaseDto, ExamItemDocumentTemplateVo>>
                examItemDocumentTemplate =
                        dataHolder.examItemDocumentTemplate.keySet().stream()
                                .collect(
                                        Collectors.toMap(
                                                dto -> dto.getExamItemId(),
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder.examItemDocumentTemplate
                                                                        .get(dto)),
                                                (o1, o2) -> o1));

        for (ExamItemBaseDto baseDto : baseDtoList) {
            ExamItemRuleDetailVo vo = voMap.get(baseDto.getId());
            vo.setClinicItem(
                    Optional.ofNullable(clinicItem.get(baseDto.getClinicItemId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
            vo.setExamItemDocumentTemplate(
                    Optional.ofNullable(examItemDocumentTemplate.get(baseDto.getId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class ExamItemRuleDetailVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<ExamItemBaseDto> rootBaseDtoList;

        /** 持有字段clinicItem的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<ClinicItemDictionaryBaseDto, ClinicItemDictionaryBaseVo> clinicItem;

        /** 持有字段examItemDocumentTemplate的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<ExamItemDocumentTemplateBaseDto, ExamItemDocumentTemplateVo>
                examItemDocumentTemplate;
    }
}
