package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.OperationDictionaryVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** OperationDictionaryVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "12a0eed9-0acb-3ed3-a7e6-aebd15434c1f")
public class OperationDictionaryVoDataAssembler {

    /** 组装OperationDictionaryVo数据 */
    @AutoGenerated(locked = true, uuid = "45288c71-9c58-37fe-846a-5551b29a0dbb")
    public void assembleData(Map<String, OperationDictionaryVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装OperationDictionaryVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "6d80e323-f103-38ac-9637-6f3436bc82ba")
    public void assembleDataCustomized(List<OperationDictionaryVo> dataList) {
        // 自定义数据组装

    }
}
