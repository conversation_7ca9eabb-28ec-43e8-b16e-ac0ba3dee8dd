package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.HighRiskItemBaseVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** HighRiskItemBaseVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "27743f05-b13d-34d9-82a7-f123a63a9515")
public class HighRiskItemBaseVoDataAssembler {

    /** 组装HighRiskItemBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "30974d5d-4fe4-3de5-8738-e0e76e2b55d9")
    public void assembleData(Map<String, HighRiskItemBaseVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装HighRiskItemBaseVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "a3ca526d-a413-33a7-872b-12908db2b922")
    public void assembleDataCustomized(List<HighRiskItemBaseVo> dataList) {
        // 自定义数据组装

    }
}
