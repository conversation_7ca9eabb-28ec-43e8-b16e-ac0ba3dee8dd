package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.LabClassDictionarySimpleVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** LabClassDictionarySimpleVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "f387688a-7131-3d24-82fa-ddbed0618436")
public class LabClassDictionarySimpleVoDataAssembler {

    /** 批量自定义组装LabClassDictionarySimpleVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "76eb91b2-81c4-34b9-a791-c965cc0424fc")
    public void assembleDataCustomized(List<LabClassDictionarySimpleVo> dataList) {
        // 自定义数据组装

    }

    /** 组装LabClassDictionarySimpleVo数据 */
    @AutoGenerated(locked = true, uuid = "b48b60fa-7562-3059-af78-24ea5cd090a7")
    public void assembleData(Map<String, LabClassDictionarySimpleVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }
}
