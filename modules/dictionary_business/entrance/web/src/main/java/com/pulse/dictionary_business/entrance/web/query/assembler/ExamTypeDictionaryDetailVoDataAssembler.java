package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.ExamTypeDeviceVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamTypeDictionaryDetailVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamTypeDocumentTemplateVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamTypeOrderLimitVo;
import com.pulse.dictionary_business.manager.dto.ExamDeviceBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamTypeDictionaryBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamTypeDocumentTemplateBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamTypeOrderLimitBaseDto;
import com.pulse.dictionary_business.service.ExamTypeDictionaryBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** ExamTypeDictionaryDetailVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "2bdf7523-e203-3da9-991c-da90f9ea5a63")
public class ExamTypeDictionaryDetailVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private ExamTypeDictionaryBaseDtoService examTypeDictionaryBaseDtoService;

    /** 组装ExamTypeDictionaryDetailVo数据 */
    @AutoGenerated(locked = true, uuid = "44dd0669-a7d1-3c62-8f91-e2b873602f0f")
    public void assembleData(
            Map<String, ExamTypeDictionaryDetailVo> voMap,
            ExamTypeDictionaryDetailVoDataAssembler.ExamTypeDictionaryDetailVoDataHolder
                    dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<ExamTypeDictionaryBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, List<Pair<ExamTypeOrderLimitBaseDto, ExamTypeOrderLimitVo>>>
                examTypeOrderLimitList =
                        dataHolder.examTypeOrderLimitList.keySet().stream()
                                .collect(
                                        Collectors.groupingBy(
                                                dto -> dto.getExamTypeId(),
                                                Collectors.mapping(
                                                        dto ->
                                                                Pair.of(
                                                                        dto,
                                                                        dataHolder
                                                                                .examTypeOrderLimitList
                                                                                .get(dto)),
                                                        Collectors.toCollection(ArrayList::new))));
        Map<String, Pair<ExamTypeDocumentTemplateBaseDto, ExamTypeDocumentTemplateVo>>
                examTypeDocumentTemplate =
                        dataHolder.examTypeDocumentTemplate.keySet().stream()
                                .collect(
                                        Collectors.toMap(
                                                dto -> dto.getExamTypeId(),
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder.examTypeDocumentTemplate
                                                                        .get(dto)),
                                                (o1, o2) -> o1));
        Map<String, List<Pair<ExamDeviceBaseDto, ExamTypeDeviceVo>>> examTypeDeviceList =
                dataHolder.examTypeDeviceList.keySet().stream()
                        .collect(
                                Collectors.groupingBy(
                                        dto -> dto.getExamTypeId(),
                                        Collectors.mapping(
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder.examTypeDeviceList.get(
                                                                        dto)),
                                                Collectors.toCollection(ArrayList::new))));

        for (ExamTypeDictionaryBaseDto baseDto : baseDtoList) {
            ExamTypeDictionaryDetailVo vo = voMap.get(baseDto.getId());
            vo.setExamTypeOrderLimitList(
                    Optional.ofNullable(examTypeOrderLimitList.get(baseDto.getId()))
                            .map(
                                    tmp ->
                                            tmp.stream()
                                                    .map(pair -> pair.getRight())
                                                    .collect(Collectors.toList()))
                            .orElse(new ArrayList<>()));
            vo.setExamTypeDocumentTemplate(
                    Optional.ofNullable(examTypeDocumentTemplate.get(baseDto.getId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
            vo.setExamTypeDeviceList(
                    Optional.ofNullable(examTypeDeviceList.get(baseDto.getId()))
                            .map(
                                    tmp ->
                                            tmp.stream()
                                                    .map(pair -> pair.getRight())
                                                    .collect(Collectors.toList()))
                            .orElse(new ArrayList<>()));
        }

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装ExamTypeDictionaryDetailVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "aabd9af7-ca19-340e-94ba-59c5651974d4")
    public void assembleDataCustomized(List<ExamTypeDictionaryDetailVo> dataList) {
        // 自定义数据组装

    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class ExamTypeDictionaryDetailVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<ExamTypeDictionaryBaseDto> rootBaseDtoList;

        /** 持有字段examTypeOrderLimitList的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<ExamTypeOrderLimitBaseDto, ExamTypeOrderLimitVo> examTypeOrderLimitList;

        /** 持有字段examTypeDocumentTemplate的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<ExamTypeDocumentTemplateBaseDto, ExamTypeDocumentTemplateVo>
                examTypeDocumentTemplate;

        /** 持有字段examTypeDeviceList的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<ExamDeviceBaseDto, ExamTypeDeviceVo> examTypeDeviceList;
    }
}
