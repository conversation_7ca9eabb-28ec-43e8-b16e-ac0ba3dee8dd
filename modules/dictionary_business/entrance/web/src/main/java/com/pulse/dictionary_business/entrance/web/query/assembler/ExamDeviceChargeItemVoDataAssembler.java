package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.billing_public_config.manager.dto.ChargeItemBaseDto;
import com.pulse.dictionary_business.entrance.web.vo.ChargeItemVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamDeviceChargeItemVo;
import com.pulse.dictionary_business.entrance.web.vo.ExamTypeDeviceVo;
import com.pulse.dictionary_business.manager.dto.ExamDeviceBaseDto;
import com.pulse.dictionary_business.manager.dto.ExamDeviceChargeItemBaseDto;
import com.pulse.dictionary_business.service.ExamDeviceChargeItemBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** ExamDeviceChargeItemVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "0eb3f837-52b6-3901-82d3-0825c9652815")
public class ExamDeviceChargeItemVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private ExamDeviceChargeItemBaseDtoService examDeviceChargeItemBaseDtoService;

    /** 批量自定义组装ExamDeviceChargeItemVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "351c3362-f7b8-38ab-9f4f-49f03a4e61e3")
    public void assembleDataCustomized(List<ExamDeviceChargeItemVo> dataList) {
        // 自定义数据组装

    }

    /** 组装ExamDeviceChargeItemVo数据 */
    @AutoGenerated(locked = true, uuid = "4374741e-f380-3c66-ab1f-1141e562572e")
    public void assembleData(
            Map<String, ExamDeviceChargeItemVo> voMap,
            ExamDeviceChargeItemVoDataAssembler.ExamDeviceChargeItemVoDataHolder dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<ExamDeviceChargeItemBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, Pair<ChargeItemBaseDto, ChargeItemVo>> chargeItem =
                dataHolder.chargeItem.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getItemCode(),
                                        dto -> Pair.of(dto, dataHolder.chargeItem.get(dto)),
                                        (o1, o2) -> o1));
        Map<String, Pair<ExamDeviceBaseDto, ExamTypeDeviceVo>> examDevice =
                dataHolder.examDevice.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getId(),
                                        dto -> Pair.of(dto, dataHolder.examDevice.get(dto)),
                                        (o1, o2) -> o1));

        for (ExamDeviceChargeItemBaseDto baseDto : baseDtoList) {
            ExamDeviceChargeItemVo vo = voMap.get(baseDto.getId());
            vo.setChargeItem(
                    Optional.ofNullable(chargeItem.get(baseDto.getChargeItemId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
            vo.setExamDevice(
                    Optional.ofNullable(examDevice.get(baseDto.getExamDeviceId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class ExamDeviceChargeItemVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<ExamDeviceChargeItemBaseDto> rootBaseDtoList;

        /** 持有字段chargeItem的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<ChargeItemBaseDto, ChargeItemVo> chargeItem;

        /** 持有字段examDevice的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<ExamDeviceBaseDto, ExamTypeDeviceVo> examDevice;
    }
}
