package com.pulse.dictionary_business.entrance.web.vo;

import com.pulse.dictionary_business.common.enums.AgeUnitEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "4934e8d7-3393-46eb-a8af-66f625e3fafb|VO|DEFINITION")
public class LabItemRuleVo {
    /** 适用开单科室列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "1d693c1d-1492-4274-9066-e6d9318d6f8f")
    private List<String> applicableOrderDepartmentIdList;

    /** 适用开单医生列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "d89b7db0-e23a-483a-96b6-386e5bdca65e")
    private List<String> applicableOrderDoctorIdList;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "a00c30c5-f4d4-496c-ba52-b5b048e989c7")
    private Date createdAt;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "4c95010e-8ba1-41ba-9843-c7192afee314")
    private String id;

    /** 检验项目ID */
    @AutoGenerated(locked = true, uuid = "c4bc4232-3f35-4561-a901-5e164b72d980")
    private String labItemId;

    /** 检验项目说明 */
    @AutoGenerated(locked = true, uuid = "891b0cc3-d908-4177-8716-b95db8e96923")
    private String labItemInstruction;

    /** 限制年龄上限 */
    @AutoGenerated(locked = true, uuid = "89ed9cd0-19fc-499b-8526-5172aa4b31c6")
    private Long limitAgeMax;

    /** 限制年龄下限 */
    @AutoGenerated(locked = true, uuid = "1b7ec22b-6fb9-4901-a164-c07582783781")
    private Long limitAgeMin;

    /** 年龄限制单位 */
    @AutoGenerated(locked = true, uuid = "41cf2972-0d20-48f2-a927-1d23842d7f8e")
    private AgeUnitEnum limitAgeUnit;

    /** 性别限制 */
    @AutoGenerated(locked = true, uuid = "d690e4f0-c124-4280-91a4-533ab34adfa0")
    private String limitGender;

    /** 不允许的开单部门ID列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "1481aa61-757c-4abd-9588-49bd3b19f996")
    private List<String> notAllowedOrderDepartmentIdList;

    /** 不允许的开单医生ID列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "ac4706d1-fc1b-4c14-bfc9-170c710d35dc")
    private List<String> notAllowedOrderDoctorIdList;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "*************-4647-8687-1541248b7690")
    private Date updatedAt;

    /** 加急类型 */
    @AutoGenerated(locked = true, uuid = "04762e9f-c907-479e-9e6e-a91b4d7cdc8f")
    private String urgentType;
}
