package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.LabItemVsSpecimenVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** LabItemVsSpecimenVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "6df497c9-b153-3b6c-baef-ced8b742cde7")
public class LabItemVsSpecimenVoDataAssembler {

    /** 批量自定义组装LabItemVsSpecimenVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "0dacfc7b-f0ee-3271-8b86-a8fa5f48035b")
    public void assembleDataCustomized(List<LabItemVsSpecimenVo> dataList) {
        // 自定义数据组装

    }

    /** 组装LabItemVsSpecimenVo数据 */
    @AutoGenerated(locked = true, uuid = "eaa4707a-c459-3bf6-9f8c-92de69050f45")
    public void assembleData(Map<Long, LabItemVsSpecimenVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }
}
