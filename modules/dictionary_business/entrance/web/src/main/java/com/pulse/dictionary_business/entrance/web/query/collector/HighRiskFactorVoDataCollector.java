package com.pulse.dictionary_business.entrance.web.query.collector;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.entrance.web.converter.HighRiskDiagnosisBaseVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.HighRiskDrugBaseVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.HighRiskFactorVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.HighRiskItemBaseVoConverter;
import com.pulse.dictionary_business.entrance.web.query.assembler.HighRiskFactorVoDataAssembler.HighRiskFactorVoDataHolder;
import com.pulse.dictionary_business.entrance.web.vo.HighRiskDiagnosisBaseVo;
import com.pulse.dictionary_business.entrance.web.vo.HighRiskDrugBaseVo;
import com.pulse.dictionary_business.entrance.web.vo.HighRiskItemBaseVo;
import com.pulse.dictionary_business.manager.dto.HighRiskDiagnosisBaseDto;
import com.pulse.dictionary_business.manager.dto.HighRiskDrugBaseDto;
import com.pulse.dictionary_business.manager.dto.HighRiskFactorDto;
import com.pulse.dictionary_business.manager.dto.HighRiskFactorRuleBaseDto;
import com.pulse.dictionary_business.manager.dto.HighRiskItemBaseDto;
import com.pulse.dictionary_business.service.HighRiskDiagnosisBaseDtoService;
import com.pulse.dictionary_business.service.HighRiskDrugBaseDtoService;
import com.pulse.dictionary_business.service.HighRiskFactorRuleBaseDtoService;
import com.pulse.dictionary_business.service.HighRiskItemBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装HighRiskFactorVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "18e321fc-ced7-3abf-9586-812a27ed1a12")
public class HighRiskFactorVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private HighRiskDiagnosisBaseDtoService highRiskDiagnosisBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private HighRiskDiagnosisBaseVoConverter highRiskDiagnosisBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private HighRiskDrugBaseDtoService highRiskDrugBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private HighRiskDrugBaseVoConverter highRiskDrugBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private HighRiskFactorRuleBaseDtoService highRiskFactorRuleBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private HighRiskFactorVoConverter highRiskFactorVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private HighRiskFactorVoDataCollector highRiskFactorVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private HighRiskItemBaseDtoService highRiskItemBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private HighRiskItemBaseVoConverter highRiskItemBaseVoConverter;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "7ce49680-d80c-3606-900e-e40097617908")
    private void fillDataWhenNecessary(HighRiskFactorVoDataHolder dataHolder) {
        List<HighRiskFactorRuleBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.highRiskDrugList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(HighRiskFactorRuleBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<HighRiskDrugBaseDto> baseDtoList =
                    highRiskDrugBaseDtoService.getByHighRiskFactorIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(HighRiskDrugBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<HighRiskDrugBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            HighRiskDrugBaseDto::getHighRiskFactorId));
            Map<HighRiskDrugBaseDto, HighRiskDrugBaseVo> dtoVoMap =
                    highRiskDrugBaseVoConverter.convertToHighRiskDrugBaseVoMap(baseDtoList);
            Map<HighRiskDrugBaseDto, HighRiskDrugBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.highRiskDrugList =
                    rootDtoList.stream()
                            .map(HighRiskFactorRuleBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.highRiskDiagnosisList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(HighRiskFactorRuleBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<HighRiskDiagnosisBaseDto> baseDtoList =
                    highRiskDiagnosisBaseDtoService
                            .getByHighRiskFactorIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(HighRiskDiagnosisBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<HighRiskDiagnosisBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            HighRiskDiagnosisBaseDto::getHighRiskFactorId));
            Map<HighRiskDiagnosisBaseDto, HighRiskDiagnosisBaseVo> dtoVoMap =
                    highRiskDiagnosisBaseVoConverter.convertToHighRiskDiagnosisBaseVoMap(
                            baseDtoList);
            Map<HighRiskDiagnosisBaseDto, HighRiskDiagnosisBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.highRiskDiagnosisList =
                    rootDtoList.stream()
                            .map(HighRiskFactorRuleBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.highRiskItemList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(HighRiskFactorRuleBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<HighRiskItemBaseDto> baseDtoList =
                    highRiskItemBaseDtoService.getByHighRiskFactorIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(HighRiskItemBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<HighRiskItemBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            HighRiskItemBaseDto::getHighRiskFactorId));
            Map<HighRiskItemBaseDto, HighRiskItemBaseVo> dtoVoMap =
                    highRiskItemBaseVoConverter.convertToHighRiskItemBaseVoMap(baseDtoList);
            Map<HighRiskItemBaseDto, HighRiskItemBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.highRiskItemList =
                    rootDtoList.stream()
                            .map(HighRiskFactorRuleBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** 获取HighRiskFactorDto数据填充HighRiskFactorVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "7ed1d710-38b7-32d7-bea3-915c12afc403")
    public void collectDataWithDtoData(
            List<HighRiskFactorDto> dtoList, HighRiskFactorVoDataHolder dataHolder) {
        List<HighRiskDrugBaseDto> highRiskDrugListList = new ArrayList<>();
        List<HighRiskDiagnosisBaseDto> highRiskDiagnosisListList = new ArrayList<>();
        List<HighRiskItemBaseDto> highRiskItemListList = new ArrayList<>();

        for (HighRiskFactorDto rootDto : dtoList) {
            if (CollectionUtil.isNotEmpty(rootDto.getHighRiskDrugList())) {
                for (HighRiskDrugBaseDto highRiskDrugListDto : rootDto.getHighRiskDrugList()) {
                    highRiskDrugListList.add(highRiskDrugListDto);
                }
            }
            if (CollectionUtil.isNotEmpty(rootDto.getHighRiskDiagnosisList())) {
                for (HighRiskDiagnosisBaseDto highRiskDiagnosisListDto :
                        rootDto.getHighRiskDiagnosisList()) {
                    highRiskDiagnosisListList.add(highRiskDiagnosisListDto);
                }
            }
            if (CollectionUtil.isNotEmpty(rootDto.getHighRiskItemList())) {
                for (HighRiskItemBaseDto highRiskItemListDto : rootDto.getHighRiskItemList()) {
                    highRiskItemListList.add(highRiskItemListDto);
                }
            }
        }

        // access highRiskDrugList
        Map<HighRiskDrugBaseDto, HighRiskDrugBaseVo> highRiskDrugListVoMap =
                highRiskDrugBaseVoConverter.convertToHighRiskDrugBaseVoMap(highRiskDrugListList);
        dataHolder.highRiskDrugList =
                highRiskDrugListList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> highRiskDrugListVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access highRiskDiagnosisList
        Map<HighRiskDiagnosisBaseDto, HighRiskDiagnosisBaseVo> highRiskDiagnosisListVoMap =
                highRiskDiagnosisBaseVoConverter.convertToHighRiskDiagnosisBaseVoMap(
                        highRiskDiagnosisListList);
        dataHolder.highRiskDiagnosisList =
                highRiskDiagnosisListList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> highRiskDiagnosisListVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access highRiskItemList
        Map<HighRiskItemBaseDto, HighRiskItemBaseVo> highRiskItemListVoMap =
                highRiskItemBaseVoConverter.convertToHighRiskItemBaseVoMap(highRiskItemListList);
        dataHolder.highRiskItemList =
                highRiskItemListList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> highRiskItemListVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "cf574572-3d94-3e57-a88d-0754a09eefed")
    public void collectDataDefault(HighRiskFactorVoDataHolder dataHolder) {
        highRiskFactorVoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
