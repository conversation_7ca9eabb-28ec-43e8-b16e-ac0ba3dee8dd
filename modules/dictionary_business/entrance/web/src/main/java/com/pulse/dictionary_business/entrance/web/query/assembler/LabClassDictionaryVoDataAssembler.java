package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.LabClassDictionaryVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** LabClassDictionaryVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "d3a51d22-e25b-3f46-951f-44491cccf94e")
public class LabClassDictionaryVoDataAssembler {

    /** 组装LabClassDictionaryVo数据 */
    @AutoGenerated(locked = true, uuid = "4488ac6a-97f2-369d-b0da-204c22aa6226")
    public void assembleData(Map<String, LabClassDictionaryVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装LabClassDictionaryVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "d793ef29-4c52-3eeb-9d86-5bd6f35a0106")
    public void assembleDataCustomized(List<LabClassDictionaryVo> dataList) {
        // 自定义数据组装

    }
}
