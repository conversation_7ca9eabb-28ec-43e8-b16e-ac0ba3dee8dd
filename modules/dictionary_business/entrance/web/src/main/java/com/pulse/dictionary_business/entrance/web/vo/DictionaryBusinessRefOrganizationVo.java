package com.pulse.dictionary_business.entrance.web.vo;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.organization.common.enums.OrganizationTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "3de3a192-c21b-4094-b991-dcfb264ac158|VO|DEFINITION")
public class DictionaryBusinessRefOrganizationVo {
    /** 别名 */
    @AutoGenerated(locked = true, uuid = "71f36b57-1258-4cfb-9665-cedab3f4bf52")
    private String alias;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "2fe9b1c0-f1fc-467c-844f-7982add06225")
    private String id;

    /** 输入代码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "dd21bf33-bf9b-48ab-89e6-3e0c8e0864e7")
    private InputCodeEo inputCode;

    /** 组织名称 */
    @AutoGenerated(locked = true, uuid = "2fdfb414-f1d2-47dc-a8be-afda90015b1a")
    private String name;

    /** 组织类型 */
    @AutoGenerated(locked = true, uuid = "71fc6e98-f9a4-4523-8f45-584b7636ff10")
    private OrganizationTypeEnum type;
}
