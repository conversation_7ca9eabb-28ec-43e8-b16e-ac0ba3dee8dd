package com.pulse.dictionary_business.entrance.web.query.collector;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.entrance.web.converter.TreatmentPackageInfoVoConverter;
import com.pulse.dictionary_business.entrance.web.converter.TreatmentPackageItemVoConverter;
import com.pulse.dictionary_business.entrance.web.query.assembler.TreatmentPackageInfoVoDataAssembler.TreatmentPackageInfoVoDataHolder;
import com.pulse.dictionary_business.entrance.web.vo.TreatmentPackageItemVo;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageInfoDto;
import com.pulse.dictionary_business.manager.dto.TreatmentPackageItemDto;
import com.pulse.dictionary_business.service.TreatmentPackageInfoDtoService;
import com.pulse.dictionary_business.service.TreatmentPackageItemDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装TreatmentPackageInfoVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "a78cc186-e90f-3cb2-a263-65061811684f")
public class TreatmentPackageInfoVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageInfoDtoService treatmentPackageInfoDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageInfoVoConverter treatmentPackageInfoVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageInfoVoDataCollector treatmentPackageInfoVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageItemDtoService treatmentPackageItemDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TreatmentPackageItemVoConverter treatmentPackageItemVoConverter;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "80df9ce9-1e42-32c7-b22c-dd7fed0c3c70")
    private void fillDataWhenNecessary(TreatmentPackageInfoVoDataHolder dataHolder) {
        List<TreatmentPackageInfoDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.treatmentPackageItemList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(TreatmentPackageInfoDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<TreatmentPackageItemDto> baseDtoList =
                    treatmentPackageItemDtoService
                            .getByTreatmentPackageIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(TreatmentPackageItemDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<TreatmentPackageItemDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            TreatmentPackageItemDto::getTreatmentPackageId));
            Map<TreatmentPackageItemDto, TreatmentPackageItemVo> dtoVoMap =
                    treatmentPackageItemVoConverter.convertToTreatmentPackageItemVoMap(baseDtoList);
            Map<TreatmentPackageItemDto, TreatmentPackageItemVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.treatmentPackageItemList =
                    rootDtoList.stream()
                            .map(TreatmentPackageInfoDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** 获取TreatmentPackageInfoDto数据填充TreatmentPackageInfoVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "8a2471fd-f3ef-35f1-a26c-b66ad69a7a53")
    public void collectDataWithDtoData(
            List<TreatmentPackageInfoDto> dtoList, TreatmentPackageInfoVoDataHolder dataHolder) {
        List<TreatmentPackageItemDto> treatmentPackageItemListList = new ArrayList<>();

        for (TreatmentPackageInfoDto rootDto : dtoList) {
            if (CollectionUtil.isNotEmpty(rootDto.getTreatmentPackageItemList())) {
                for (TreatmentPackageItemDto treatmentPackageItemListDto :
                        rootDto.getTreatmentPackageItemList()) {
                    treatmentPackageItemListList.add(treatmentPackageItemListDto);
                }
            }
        }

        // access treatmentPackageItemList
        Map<TreatmentPackageItemDto, TreatmentPackageItemVo> treatmentPackageItemListVoMap =
                treatmentPackageItemVoConverter.convertToTreatmentPackageItemVoMap(
                        treatmentPackageItemListList);
        dataHolder.treatmentPackageItemList =
                treatmentPackageItemListList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> treatmentPackageItemListVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "ebce6cb2-00f5-34f6-a09d-76afc33744d7")
    public void collectDataDefault(TreatmentPackageInfoVoDataHolder dataHolder) {
        treatmentPackageInfoVoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
