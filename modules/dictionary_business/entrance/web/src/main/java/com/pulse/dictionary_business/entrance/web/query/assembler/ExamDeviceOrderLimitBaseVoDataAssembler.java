package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.ExamDeviceOrderLimitBaseVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** ExamDeviceOrderLimitBaseVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "09aa4ace-087e-374a-b3c7-e621648761bc")
public class ExamDeviceOrderLimitBaseVoDataAssembler {

    /** 批量自定义组装ExamDeviceOrderLimitBaseVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "4cf48d9c-0501-3b13-a9cd-a37416cb6bfe")
    public void assembleDataCustomized(List<ExamDeviceOrderLimitBaseVo> dataList) {
        // 自定义数据组装

    }

    /** 组装ExamDeviceOrderLimitBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "fcdc7b2a-445d-3957-8032-075a890ae4e9")
    public void assembleData(Map<Long, ExamDeviceOrderLimitBaseVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }
}
