package com.pulse.dictionary_business.entrance.web.query.assembler;

import com.pulse.dictionary_business.entrance.web.vo.ExamTypeOrderLimitVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** ExamTypeOrderLimitVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "68e5de36-103b-3c19-8516-a8ed4967d204")
public class ExamTypeOrderLimitVoDataAssembler {

    /** 组装ExamTypeOrderLimitVo数据 */
    @AutoGenerated(locked = true, uuid = "564e93f3-e767-3ae3-b889-6a5bcc668364")
    public void assembleData(Map<String, ExamTypeOrderLimitVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装ExamTypeOrderLimitVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "653f5aab-4436-34eb-bf40-34c119c70cbe")
    public void assembleDataCustomized(List<ExamTypeOrderLimitVo> dataList) {
        // 自定义数据组装

    }
}
