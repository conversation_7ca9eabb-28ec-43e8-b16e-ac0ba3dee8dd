package com.pulse.dictionary_business.persist.mapper;

import com.pulse.dictionary_business.persist.dos.TreatItem;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "54fd6a63-50de-4bca-9673-e9fb190500fd|ENTITY|IDAO")
public interface TreatItemDao {

    @AutoGenerated(locked = true, uuid = "18dbbcf0-e225-3657-83d6-cc3520109612")
    TreatItem getById(String id);

    @AutoGenerated(locked = true, uuid = "5f1cc742-5f7d-3a49-8d9a-575f38204870")
    List<TreatItem> getByTreatClassId(String treatClassId);

    @AutoGenerated(locked = true, uuid = "f30d1a4d-54f8-3a40-839f-8e08f741b999")
    List<TreatItem> getByTreatClassIds(List<String> treatClassId);

    @AutoGenerated(locked = true, uuid = "f6b51a9e-d835-32ad-a80a-d3534ef4c45e")
    List<TreatItem> getByIds(List<String> id);
}
