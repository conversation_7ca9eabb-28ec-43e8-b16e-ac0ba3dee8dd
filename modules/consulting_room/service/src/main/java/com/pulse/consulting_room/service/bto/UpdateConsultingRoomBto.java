package com.pulse.consulting_room.service.bto;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;

import javax.validation.Valid;

/**
 * <b>[源自]</b> ConsultingRoom
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "549bc7b9-ac70-45ef-9947-ed82931e9b51|BTO|DEFINITION")
public class UpdateConsultingRoomBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 诊室区域 */
    @AutoGenerated(locked = true, uuid = "40883d02-5ccc-47bd-ac9c-48e8b368e89c")
    private String area;

    /** 诊室区域英文名称 */
    @AutoGenerated(locked = true, uuid = "fe91e4fd-f966-493e-b4b5-5511d45c0f0f")
    private String areaEnglishName;

    /** 院区组织id */
    @AutoGenerated(locked = true, uuid = "3b95c1cd-83c8-4ca2-99e3-281e33510134")
    private String campusOrganizationId;

    /** 诊室门牌号 */
    @AutoGenerated(locked = true, uuid = "c39d654a-3dec-41bb-aebb-7903c6780b85")
    private String clinicRoomNumber;

    /** 诊室代码 */
    @AutoGenerated(locked = true, uuid = "02909520-e7d1-4c9a-9137-ef4fa4791d21")
    private String code;

    /** 诊室名称(患者) */
    @AutoGenerated(locked = true, uuid = "addd8774-ee16-443e-87a0-972290f3969d")
    private String displayName;

    /** 楼层单元 */
    @AutoGenerated(locked = true, uuid = "951c0681-1eba-47e4-9ae9-b5707c0679e2")
    private String floorUnit;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "637609fe-e430-416c-a45d-fd3c4e24865c")
    private String id;

    /** 输入代码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "01d2f8f4-08ee-45ed-92ca-932f61ab163b")
    private InputCodeEo inputCode;

    /** 作废标志 */
    @AutoGenerated(locked = true, uuid = "cad12042-fdae-4b27-8101-abfd182cb1ed")
    private Boolean invalidFlag;

    /** 作废原因 */
    @AutoGenerated(locked = true, uuid = "cfed279d-fdb1-44c8-9e2e-a31cf514075f")
    private String invalidReason;

    /** IP地址 */
    @AutoGenerated(locked = true, uuid = "f9144c4c-ba2d-40ff-88a2-3f6d68112ad1")
    private String ip;

    /** 诊室名称（排班） */
    @AutoGenerated(locked = true, uuid = "3493bfaf-8454-47f5-b026-e34b4b164dd6")
    private String name;

    /** 性质 */
    @AutoGenerated(locked = true, uuid = "55c2aa17-17bc-4fc6-b398-259e81c04fcf")
    private String property;

    /** 简码 */
    @AutoGenerated(locked = true, uuid = "627ce814-d09a-4d43-9773-1b4b5b1d7234")
    private String shortCode;

    /** 修改者id */
    @AutoGenerated(locked = true, uuid = "711b13c2-9106-4fe4-b8b1-c76ba4178288")
    private String updatedBy;

    @AutoGenerated(locked = true)
    public void setArea(String area) {
        this.__$validPropertySet.add("area");
        this.area = area;
    }

    @AutoGenerated(locked = true)
    public void setAreaEnglishName(String areaEnglishName) {
        this.__$validPropertySet.add("areaEnglishName");
        this.areaEnglishName = areaEnglishName;
    }

    @AutoGenerated(locked = true)
    public void setCampusOrganizationId(String campusOrganizationId) {
        this.__$validPropertySet.add("campusOrganizationId");
        this.campusOrganizationId = campusOrganizationId;
    }

    @AutoGenerated(locked = true)
    public void setClinicRoomNumber(String clinicRoomNumber) {
        this.__$validPropertySet.add("clinicRoomNumber");
        this.clinicRoomNumber = clinicRoomNumber;
    }

    @AutoGenerated(locked = true)
    public void setCode(String code) {
        this.__$validPropertySet.add("code");
        this.code = code;
    }

    @AutoGenerated(locked = true)
    public void setDisplayName(String displayName) {
        this.__$validPropertySet.add("displayName");
        this.displayName = displayName;
    }

    @AutoGenerated(locked = true)
    public void setFloorUnit(String floorUnit) {
        this.__$validPropertySet.add("floorUnit");
        this.floorUnit = floorUnit;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setInputCode(InputCodeEo inputCode) {
        this.__$validPropertySet.add("inputCode");
        this.inputCode = inputCode;
    }

    @AutoGenerated(locked = true)
    public void setInvalidFlag(Boolean invalidFlag) {
        this.__$validPropertySet.add("invalidFlag");
        this.invalidFlag = invalidFlag;
    }

    @AutoGenerated(locked = true)
    public void setInvalidReason(String invalidReason) {
        this.__$validPropertySet.add("invalidReason");
        this.invalidReason = invalidReason;
    }

    @AutoGenerated(locked = true)
    public void setIp(String ip) {
        this.__$validPropertySet.add("ip");
        this.ip = ip;
    }

    @AutoGenerated(locked = true)
    public void setName(String name) {
        this.__$validPropertySet.add("name");
        this.name = name;
    }

    @AutoGenerated(locked = true)
    public void setProperty(String property) {
        this.__$validPropertySet.add("property");
        this.property = property;
    }

    @AutoGenerated(locked = true)
    public void setShortCode(String shortCode) {
        this.__$validPropertySet.add("shortCode");
        this.shortCode = shortCode;
    }

    @AutoGenerated(locked = true)
    public void setUpdatedBy(String updatedBy) {
        this.__$validPropertySet.add("updatedBy");
        this.updatedBy = updatedBy;
    }
}
