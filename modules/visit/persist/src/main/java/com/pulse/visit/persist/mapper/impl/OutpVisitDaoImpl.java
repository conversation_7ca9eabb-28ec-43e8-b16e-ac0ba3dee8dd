package com.pulse.visit.persist.mapper.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pulse.visit.persist.dos.OutpVisit;
import com.pulse.visit.persist.mapper.OutpVisitDao;
import com.pulse.visit.persist.mapper.mybatis.OutpVisitMapper;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "63f788e4-786b-47af-89b1-93a43687a49e|ENTITY|DAO")
public class OutpVisitDaoImpl implements OutpVisitDao {
    @AutoGenerated(locked = true)
    @Resource
    private OutpVisitMapper outpVisitMapper;

    @AutoGenerated(locked = true, uuid = "22c395b2-121e-3134-8fee-90ceb8c16271")
    @Override
    public List<OutpVisit> getByOutpRegisterId(String outpRegisterId) {
        QueryWrapper<OutpVisit> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("outp_register_id", outpRegisterId).orderByAsc("id");
        return outpVisitMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "3ded476e-0ddd-3b1c-a463-cfb920e46bf7")
    @Override
    public List<OutpVisit> getByPatientIds(List<String> patientId) {
        QueryWrapper<OutpVisit> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("patient_id", patientId).orderByAsc("id");
        return outpVisitMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "5f728db2-0174-348b-b1db-fd0688aad279")
    @Override
    public OutpVisit getById(String id) {
        QueryWrapper<OutpVisit> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        return outpVisitMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "880c2671-3494-3fbc-abb1-b85f72bb19b3")
    @Override
    public List<OutpVisit> getByPatientId(String patientId) {
        QueryWrapper<OutpVisit> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("patient_id", patientId).orderByAsc("id");
        return outpVisitMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "9d8dd267-026d-3034-a8f2-29e6f0dc2918")
    @Override
    public List<OutpVisit> getByOutpRegisterIds(List<String> outpRegisterId) {
        QueryWrapper<OutpVisit> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("outp_register_id", outpRegisterId).orderByAsc("id");
        return outpVisitMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "d1386f1f-eab1-31b2-a91a-4b3672f34438")
    @Override
    public List<OutpVisit> getByIds(List<String> id) {
        QueryWrapper<OutpVisit> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", id).orderByAsc("id");
        return outpVisitMapper.selectList(queryWrapper);
    }
}
