package com.pulse.visit.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.pulse.visit.common.enums.VisitStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "outp_visit", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "63f788e4-786b-47af-89b1-93a43687a49e|ENTITY|DEFINITION")
public class OutpVisit {
    @AutoGenerated(locked = true, uuid = "aba2cb71-4caf-462f-b101-96e71903ddd3")
    @TableField(value = "cost_categories")
    private String costCategories;

    @AutoGenerated(locked = true, uuid = "e7877f48-daf1-49c5-bf43-ca4d6d0b6e4d")
    @TableField(value = "cost_nature")
    private String costNature;

    @AutoGenerated(locked = true, uuid = "4c96aa10-eae0-5eb9-bf75-aa128b1b0c69")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "dc38e8ba-93fb-4029-b1c7-dea5bffbfd41")
    @TableField(value = "first_visit_flag")
    private Boolean firstVisitFlag;

    @AutoGenerated(locked = true, uuid = "ba189da1-ac32-4c78-8640-4a3ba0b9af33")
    @TableField(value = "gcp_project_id")
    private String gcpProjectId;

    @AutoGenerated(locked = true, uuid = "22d26855-401a-439c-b2b6-c829d502f295")
    @TableId(value = "id")
    private String id;

    @AutoGenerated(locked = true, uuid = "ebdffeee-5d73-4049-8211-d7b74b71f10e")
    @TableField(value = "identity_type")
    private String identityType;

    /** 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "65dd32cb-3b0c-49f4-893a-8027c43f39fc")
    @TableField(value = "lock_version")
    private Long lockVersion;

    @AutoGenerated(locked = true, uuid = "7d968629-23bb-49e5-8625-1098ac9d4b8c")
    @TableField(value = "outp_register_id")
    private String outpRegisterId;

    @AutoGenerated(locked = true, uuid = "fa4df6c2-24df-45d4-ad0a-dd791372f80c")
    @TableField(value = "patient_destination")
    private String patientDestination;

    @AutoGenerated(locked = true, uuid = "89488a4b-5873-4342-8e29-0208cec2501b")
    @TableField(value = "patient_id")
    private String patientId;

    @AutoGenerated(locked = true, uuid = "fa66e35c-8273-43d2-8db9-21ad75d7dde3")
    @TableField(value = "patient_name")
    private String patientName;

    @AutoGenerated(locked = true, uuid = "53f916e0-0feb-4f80-8b40-479e6f6f40fb")
    @TableField(value = "triage_serial_number")
    private Long triageSerialNumber;

    @AutoGenerated(locked = true, uuid = "0b3d2e4d-dc4c-4766-afae-0bff50caf4a5")
    @TableField(value = "triage_status")
    private String triageStatus;

    @AutoGenerated(locked = true, uuid = "fff33cad-f533-4148-b261-321ae279807c")
    @TableField(value = "triage_time")
    private Date triageTime;

    @AutoGenerated(locked = true, uuid = "cc9d6308-5ab1-5fc9-ae97-2496c693577e")
    @TableField(value = "updated_at")
    private Date updatedAt;

    @AutoGenerated(locked = true, uuid = "0d06d696-51f7-4c42-a52a-606d9521fb5d")
    @TableField(value = "visit_app_id")
    private String visitAppId;

    @AutoGenerated(locked = true, uuid = "dc4e219a-7b71-48aa-8f59-f7d2aa4e2c81")
    @TableField(value = "visit_card_id")
    private String visitCardId;

    @AutoGenerated(locked = true, uuid = "a99c3573-f2a0-442e-aa15-680bd9291915")
    @TableField(value = "visit_date")
    private Date visitDate;

    @AutoGenerated(locked = true, uuid = "434ae06e-5781-40be-852c-fe2c288d9d2e")
    @TableField(value = "visit_department_id")
    private String visitDepartmentId;

    @AutoGenerated(locked = true, uuid = "9e303589-326c-4fcb-87eb-393313bbd072")
    @TableField(value = "visit_doctor_id")
    private String visitDoctorId;

    @AutoGenerated(locked = true, uuid = "4a5d2052-d627-403a-a1b4-823e89b38a10")
    @TableField(value = "visit_number")
    private String visitNumber;

    @AutoGenerated(locked = true, uuid = "09d6a00f-5874-4247-92e4-db2df71a10d5")
    @TableField(value = "visit_status")
    private VisitStatusEnum visitStatus;

    @AutoGenerated(locked = true, uuid = "95b8f232-ec8c-421d-8468-1084ae855037")
    @TableField(value = "visit_type")
    private String visitType;
}
