package com.pulse.visit.persist.mapper.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pulse.visit.persist.dos.OutpVisitEncounter;
import com.pulse.visit.persist.mapper.OutpVisitEncounterDao;
import com.pulse.visit.persist.mapper.mybatis.OutpVisitEncounterMapper;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "579ebd22-56df-47e4-b672-fce06135bc16|ENTITY|DAO")
public class OutpVisitEncounterDaoImpl implements OutpVisitEncounterDao {
    @AutoGenerated(locked = true)
    @Resource
    private OutpVisitEncounterMapper outpVisitEncounterMapper;

    @AutoGenerated(locked = true, uuid = "3c7eff28-256a-3d6d-abea-a9b1ca9577b0")
    @Override
    public List<OutpVisitEncounter> getByOutpVisitId(String outpVisitId) {
        QueryWrapper<OutpVisitEncounter> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("outp_visit_id", outpVisitId).orderByAsc("id");
        return outpVisitEncounterMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "98cfe07a-7a6c-3b9f-b4d5-00b09d112ab2")
    @Override
    public OutpVisitEncounter getById(String id) {
        QueryWrapper<OutpVisitEncounter> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        return outpVisitEncounterMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "cd144ce5-50a4-332e-8431-ef16c7a5ea58")
    @Override
    public List<OutpVisitEncounter> getByOutpVisitIds(List<String> outpVisitId) {
        QueryWrapper<OutpVisitEncounter> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("outp_visit_id", outpVisitId).orderByAsc("id");
        return outpVisitEncounterMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "f2f65005-a65d-3c26-816f-6db71574dbd0")
    @Override
    public List<OutpVisitEncounter> getByIds(List<String> id) {
        QueryWrapper<OutpVisitEncounter> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", id).orderByAsc("id");
        return outpVisitEncounterMapper.selectList(queryWrapper);
    }
}
