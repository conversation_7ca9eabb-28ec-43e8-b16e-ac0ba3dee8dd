package com.pulse.dictionary_basic.persist.eo;

import com.vs.code.AutoGenerated;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** */
@Getter
@Setter
@EqualsAndHashCode
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoGenerated(locked = false, uuid = "9a90c422-4b6f-4f88-88a5-60001f03b6dc|EO|DEFINITION")
public class TimeEo {
    /** 时 */
    @AutoGenerated(locked = true, uuid = "1d0beca9-9f70-4295-bdf6-d1d06b276aad")
    private Long hour;

    /** 分钟 */
    @AutoGenerated(locked = true, uuid = "6cc7f66d-ca88-4698-b101-06c0e6951d04")
    private Long minute;

    /** 秒 */
    @AutoGenerated(locked = true, uuid = "a2236eae-3231-4a98-8a53-db51c1ba5ef9")
    private Long second;
}
