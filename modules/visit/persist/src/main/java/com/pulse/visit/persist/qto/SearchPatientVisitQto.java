package com.pulse.visit.persist.qto;

import com.pulse.appointment_schedule.common.enums.TimeDescriptionEnum;
import com.pulse.visit.common.enums.VisitStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Getter
@Setter
@AutoGenerated(locked = true, uuid = "67e1f2b5-143b-4042-b4cd-a2aab9d36e58|QTO|DEFINITION")
public class SearchPatientVisitQto {
    /** 排班ID outp_appoint.appointment_schedule_id */
    @Valid
    @AutoGenerated(locked = true, uuid = "dc8a953a-6d78-488d-909a-a0e96255b4bc")
    private List<String> appointmentScheduleIdIn;

    /** 挂号科室id outp_register.registration_department_id */
    @AutoGenerated(locked = true, uuid = "e63bcd25-6df9-4334-b293-aa0111c50e28")
    private String departmentIdIs;

    /** 挂号医生id outp_register.registration_doctor_id */
    @AutoGenerated(locked = true, uuid = "54c96e8f-9cc0-41c9-8a6b-e24f153f0c4b")
    private String doctorIdIs;

    /** 挂号日期 outp_register.register_date */
    @AutoGenerated(locked = true, uuid = "a75bf08a-c42d-4f29-9ed9-3170c67dc15b")
    private Date outpRegisterRegisterDateBiggerThanEqual;

    /** 挂号日期 outp_register.register_date */
    @AutoGenerated(locked = true, uuid = "135ed578-289d-47a5-a39e-8077f5debf2a")
    private Date outpRegisterRegisterDateLessThanEqual;

    /** 门诊挂号大类 outp_register.outpatient_registration_category */
    @AutoGenerated(locked = true, uuid = "9c1c1bee-a734-4d01-9121-a31c447f0d37")
    private String registrationCategoryIs;

    /** 午别 outp_register.time_description */
    @Valid
    @AutoGenerated(locked = true, uuid = "8d924bf7-a39b-46e9-ba41-6e211f360499")
    private List<TimeDescriptionEnum> timeDescriptionIn;

    /** 门诊就诊状态 outp_visit.visit_status */
    @AutoGenerated(locked = true, uuid = "d23415e3-a8de-42e0-a419-65eb14140705")
    private VisitStatusEnum visitStatusIs;
}
