package com.pulse.organization.persist.qto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AutoGenerated(locked = true, uuid = "180edb3f-f88f-4a1b-b0f7-a88f4f284182|QTO|DEFINITION")
public class SearchRegisterDoctorQto {
    @AutoGenerated(locked = true, uuid = "e91793a6-7f38-4035-9030-483b810b3249")
    private Integer from;

    /** 输入码 staff.input_code */
    @AutoGenerated(locked = true, uuid = "e8235c1f-d429-419f-b0a0-f3b9928c9bcd")
    private String inputCodeCustomLike;

    /** 输入码 staff.input_code */
    @AutoGenerated(locked = true, uuid = "de282366-ed1a-4896-bfaf-7241f45d6a72")
    private String inputCodePinyinLike;

    /** 输入码 staff.input_code */
    @AutoGenerated(locked = true, uuid = "d07597e7-287d-462c-9477-cd534960b716")
    private String inputCodeWubiLike;

    /** 姓名 staff.name */
    @AutoGenerated(locked = true, uuid = "0edb9ae3-db46-4e68-aff7-85ddaa9a1191")
    private String nameLike;

    /** 主键 organization.id */
    @AutoGenerated(locked = true, uuid = "16c190c3-ea0d-4df8-b9c9-172f011dae1e")
    private String organizationIdIs;

    /** 挂号医生启用标志 staff.register_doctor_enable_flag */
    @AutoGenerated(locked = true, uuid = "55e7978b-034e-4bb8-979b-5cad0c865ba5")
    private Boolean registerDoctorEnableFlagIs;

    /** 挂号医生标志 staff.register_doctor_flag */
    @AutoGenerated(locked = true, uuid = "58a48885-3d1b-4811-96e7-617759a9e056")
    private Boolean registerDoctorFlagIs;

    @AutoGenerated(locked = true, uuid = "9d04835d-8079-4e94-9671-df63c38acb76")
    private String scrollId;

    @AutoGenerated(locked = true, uuid = "47d44166-6bd2-4304-91eb-29099159beaf")
    private Integer size;

    /** 员工编号 staff.staff_number */
    @AutoGenerated(locked = true, uuid = "c8724b2a-d642-42d6-b3fe-9cc23b6575df")
    private String staffNumberIs;

    /** 职工类别id staff.staff_type_id */
    @AutoGenerated(locked = true, uuid = "ca746bfe-4e4f-47c9-9fbe-d2e11aad68bd")
    private String staffTypeIdIs;
}
