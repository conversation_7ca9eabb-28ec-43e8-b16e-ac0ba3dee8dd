package com.pulse.visit.persist.qto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@AutoGenerated(locked = true, uuid = "69419952-026a-498b-8d05-d9792ac1a17f|QTO|DEFINITION")
public class SearchCancellationReviewedQto {
    @AutoGenerated(locked = true, uuid = "49e99474-2fc5-4bbe-b261-b99b562a5582")
    private Integer from;

    /** 挂号日期 outp_register.register_date */
    @AutoGenerated(locked = true, uuid = "0ffb14bd-af07-4e9a-9e7c-10318bbe286e")
    private Date outpRegisterRegisterDateBiggerThanEqual;

    /** 挂号日期 outp_register.register_date */
    @AutoGenerated(locked = true, uuid = "f937c48e-d8e2-46d9-a6c2-9a631b3f8961")
    private Date outpRegisterRegisterDateLessThanEqual;

    /** 用于显示的id patient.display_id */
    @AutoGenerated(locked = true, uuid = "b3996296-dffd-420f-8825-17a9f6657470")
    private String patientDisplayIdIs;

    @AutoGenerated(locked = true, uuid = "233587fa-c96a-4943-b452-6b5c84a5daa3")
    private Integer size;
}
