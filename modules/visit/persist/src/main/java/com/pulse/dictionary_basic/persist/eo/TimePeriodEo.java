package com.pulse.dictionary_basic.persist.eo;

import com.vs.code.AutoGenerated;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.Valid;

/** */
@Getter
@Setter
@EqualsAndHashCode
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoGenerated(locked = false, uuid = "8300c2c6-355e-4088-9515-0aa967cda49a|EO|DEFINITION")
public class TimePeriodEo {
    /** 开始时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "af7ac9a3-5c20-42e8-ad8b-58204bc6bb8c")
    private TimeEo startTime;

    /** 结束时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "070fe542-3f52-422b-845e-cc9e78acb0b3")
    private TimeEo endTime;
}
