package com.pulse.patient_information.persist.mapper;

import com.pulse.patient_information.persist.dos.PatientIdentification;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "8f1891bc-03ba-41a2-81ed-8af1ce6bc8e4|ENTITY|IDAO")
public interface PatientIdentificationDao {

    @AutoGenerated(locked = true, uuid = "54f78aa3-e488-38c8-84e3-51ea6af476ab")
    List<PatientIdentification> getByPatientIds(List<String> patientId);

    @AutoGenerated(locked = true, uuid = "67e4d972-5ee2-3048-8b04-994e7a5e2e4f")
    List<PatientIdentification> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "94db2710-03fe-33b9-9104-cd82e4e6453c")
    PatientIdentification getByIdentificationClassAndIdentificationNumber(
            String identificationClass, String identificationNumber);

    @AutoGenerated(locked = true, uuid = "b00330aa-98b0-3b71-8b9d-69ad8a888861")
    List<PatientIdentification> getByPatientId(String patientId);

    @AutoGenerated(locked = true, uuid = "c841a830-980c-37a9-8dbe-2a7fe1c51ce3")
    PatientIdentification getById(String id);

    @AutoGenerated(locked = true, uuid = "da16d53d-8af5-3036-97fd-270a9f086d8d")
    List<PatientIdentification> getByIdentificationClasssAndIdentificationNumbers(
            List<PatientIdentification.IdentificationClassAndIdentificationNumber> varList);
}
