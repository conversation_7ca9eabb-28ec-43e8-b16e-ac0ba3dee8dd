package com.pulse.application.entrance.web.vo;

import com.pulse.application.common.enums.ApplicationStatusEnum;
import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "4783b5c7-ac89-4c4a-9f44-fd8cddb74f29|VO|DEFINITION")
public class ApplicationSimpleVo {
    /** 缩写 */
    @AutoGenerated(locked = true, uuid = "112bc13d-bcca-4292-945b-f458fcc6d9d8")
    private String abbreviation;

    /** 应用编码 */
    @AutoGenerated(locked = true, uuid = "aa080f53-6446-463e-aa15-01da7a446408")
    private String code;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "5aaf9e8c-5374-49f5-86c9-c34bdd94a081")
    private String id;

    /** 输入码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "9f370028-654e-4fe5-9395-bd012ed503bc")
    private InputCodeEo inputCode;

    /** 名称 */
    @AutoGenerated(locked = true, uuid = "a36b4303-3174-459b-83d0-0a9ad4bc4312")
    private String name;

    /** 状态 */
    @AutoGenerated(locked = true, uuid = "4491b424-a90c-4b45-bba6-7aa7d2a72e0c")
    private ApplicationStatusEnum status;
}
