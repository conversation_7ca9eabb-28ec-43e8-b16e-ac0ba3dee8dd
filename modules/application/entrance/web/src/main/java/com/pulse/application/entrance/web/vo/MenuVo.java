package com.pulse.application.entrance.web.vo;

import com.pulse.application.common.enums.MenuEnum;
import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "e62c858b-68b3-4c27-a443-b576cc5ff8f4|VO|DEFINITION")
public class MenuVo {
    /** 菜单编码 */
    @AutoGenerated(locked = true, uuid = "0293c7e3-20f3-4a7a-8372-c0f2340e72c0")
    private String code;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "f5310db1-b2b8-4cf9-9594-29c8b32be8fb")
    private Date createdAt;

    /** 启用标识 */
    @AutoGenerated(locked = true, uuid = "f0f3e81a-b9eb-4bf9-ac19-21dac19c2c80")
    private Boolean enableFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "2f88a133-20ba-4505-8678-a051a3f9ea22")
    private String id;

    /** 输入码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "194b2009-d130-4cf0-b222-617a57ddbe70")
    private InputCodeEo inputCode;

    /** 菜单图标 */
    @AutoGenerated(locked = true, uuid = "880b10aa-5d33-4e85-a6aa-d9d2d962bfba")
    private String menuLogo;

    /** 菜单类型 */
    @AutoGenerated(locked = true, uuid = "9bc43dcc-8334-4fa8-a5bf-952312726b73")
    private MenuEnum menuType;

    /** 名称 */
    @AutoGenerated(locked = true, uuid = "716da817-81bc-414d-a410-bedce5875aa4")
    private String name;

    /** 父菜单ID */
    @AutoGenerated(locked = true, uuid = "f94436b7-7374-4324-be3a-d609365ba05f")
    private String parentId;

    /** 菜单说明 */
    @AutoGenerated(locked = true, uuid = "ee717e70-ea9c-4f75-8c11-f4a39de82485")
    private String remark;

    /** 关联路由ID */
    @AutoGenerated(locked = true, uuid = "9b646053-53e2-470f-a056-b477b608157e")
    private String routerId;

    /** 同级分组号 */
    @AutoGenerated(locked = true, uuid = "ae02e80f-67f3-48a1-be44-70223f259e7a")
    private String sameLevelGroupNumber;

    /** 快捷键 */
    @AutoGenerated(locked = true, uuid = "ce83664d-6c70-49d0-a4c8-783468b90ab5")
    private String shortcutKey;

    /** 排序号 */
    @AutoGenerated(locked = true, uuid = "c36a070b-5501-4e05-b9bc-fa05aade114b")
    private Integer sortNumber;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "cd28ce62-44f3-4365-ae25-500fcabc608a")
    private Date updatedAt;

    /** 可见标识 */
    @AutoGenerated(locked = true, uuid = "3d0baf7c-493e-47a8-a1b5-90f90282d6ce")
    private Boolean visibleFlag;
}
