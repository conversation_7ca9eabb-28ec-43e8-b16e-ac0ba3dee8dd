package com.pulse.application.entrance.web.vo;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "a3a445dd-13f3-4708-aaa4-c54202712723|VO|DEFINITION")
public class CampusApplicationVo {
    /** 应用集 */
    @Valid
    @AutoGenerated(locked = true, uuid = "900c37f8-288b-4c37-a418-49937ab177a9")
    private List<ApplicationSubVo> listApplication;

    /** 院区名 */
    @AutoGenerated(locked = true, uuid = "2498e577-1ac0-47a9-b939-06b543ad6438")
    private String name;

    /** 院区id */
    @AutoGenerated(locked = true, uuid = "5eae70a9-1326-4a1e-aacd-c66229f106cf")
    private String organizationId;
}
