package com.pulse.application.entrance.web.vo;

import com.pulse.application.common.enums.CallTypeEnum;
import com.pulse.application.common.enums.ProgramEnum;
import com.pulse.application.common.enums.RouterTypeEnum;
import com.pulse.application.common.enums.SourceSystemTypeEnum;
import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "69e9b1cc-ac37-4722-b5f2-be05f641544a|VO|DEFINITION")
public class RouterVo {
    /** 调用类型 */
    @AutoGenerated(locked = true, uuid = "8f43a774-01b6-44fe-9493-0db0c18b3c46")
    private CallTypeEnum callType;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "f3deec11-a029-4066-82a9-a47c955b8cd1")
    private Date createdAt;

    /** 描述 */
    @AutoGenerated(locked = true, uuid = "b0085533-3f7d-4718-82be-37478ce63a48")
    private String description;

    /** 启用标识 */
    @AutoGenerated(locked = true, uuid = "be9d1c61-c341-4dc3-bdca-4777f5b426d5")
    private Boolean enableFlag;

    /** 关联功能ID */
    @AutoGenerated(locked = true, uuid = "c64aad83-7ade-430d-9239-24bf461b72b6")
    private String featureId;

    /** 前端页面文件路径 */
    @AutoGenerated(locked = true, uuid = "09334973-4f31-44c4-ad55-105c3b6a195a")
    private String frontPageUrl;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "aec1fcd5-7cb3-49ea-a13b-45971a60ba5f")
    private String id;

    /** 输入码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "84d8bb21-9009-437d-9126-2c055105fcc1")
    private InputCodeEo inputCode;

    /** 标签ID列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "5b182b01-7249-450b-b058-9787c0d1f22d")
    private List<String> labelIdList;

    /** 名称 */
    @AutoGenerated(locked = true, uuid = "3c9ec18b-3156-4f63-9ecd-6d320becd4f0")
    private String name;

    /** 程序名称 */
    @AutoGenerated(locked = true, uuid = "6f7c11ae-303f-4117-b814-9d65802a3066")
    private ProgramEnum openProgramName;

    /** 系统类型 */
    @AutoGenerated(locked = true, uuid = "f0246372-f85e-4ff9-b1cb-c832a4334c40")
    private SourceSystemTypeEnum openType;

    /** 路由名称 */
    @AutoGenerated(locked = true, uuid = "bb2ef4c1-7631-40ad-a3ed-7a9143f008ae")
    private String parameter;

    /** 路径 */
    @AutoGenerated(locked = true, uuid = "160d9901-b3f2-43de-b25a-5cf2702c97d2")
    private String path;

    /** 路由URL */
    @AutoGenerated(locked = true, uuid = "84116202-17e3-4baa-b010-12ee630712d4")
    private String routerUrl;

    /** 排序号 */
    @AutoGenerated(locked = true, uuid = "b1c0a95b-2120-48ce-8d16-acb0f256e685")
    private Integer sortNumber;

    /** 系统架构 */
    @AutoGenerated(locked = true, uuid = "83503c42-93c8-4570-be1a-b12f9408019c")
    private String systemArchitecture;

    /** 类型 */
    @AutoGenerated(locked = true, uuid = "c088ddcc-8eda-46ab-99db-79582b7177dd")
    private RouterTypeEnum type;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "f6519f7e-6414-418c-88f9-a603bc159749")
    private Date updatedAt;
}
