package com.pulse.application.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

import javax.validation.Valid;

@Data
@TableName(value = "application_organization", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "e0fbd07c-873b-4aa6-a90e-b44569381457|ENTITY|DEFINITION")
public class ApplicationOrganization {
    @AutoGenerated(locked = true, uuid = "200a3916-61a0-487d-a8a6-df8375b635ff")
    @TableField(value = "application_id")
    private String applicationId;

    @AutoGenerated(locked = true, uuid = "19e208fc-f529-592d-bfd7-9c53ccfc986c")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "f2a5d69f-d61c-4526-9e1a-8e3ff535b931")
    @TableField(value = "description")
    private String description;

    @AutoGenerated(locked = true, uuid = "85559252-a0a1-4f1a-8846-6598356cfaf3")
    @TableField(value = "emergency_flag")
    private Boolean emergencyFlag;

    @AutoGenerated(locked = true, uuid = "7218377d-ecd9-457f-930b-0f562eec8071")
    @TableId(value = "id")
    private String id;

    @Valid
    @AutoGenerated(locked = true, uuid = "02a8615f-9cb2-49bc-ba26-b6abd4e1edd9")
    @TableField(value = "input_code", typeHandler = JacksonTypeHandler.class)
    private InputCodeEo inputCode;

    @AutoGenerated(locked = true, uuid = "af7e4527-89ef-4da0-a252-7d2450b34d68")
    @TableField(value = "inventory")
    private String inventory;

    @AutoGenerated(locked = true, uuid = "07bce922-116e-4497-97b1-03273bfa9d42")
    @TableField(value = "organization_id")
    private String organizationId;

    @AutoGenerated(locked = true, uuid = "7cc5da5e-e3c1-4478-912b-d65d4b788917")
    @TableField(value = "outpatient_fee_flag")
    private Boolean outpatientFeeFlag;

    @AutoGenerated(locked = true, uuid = "d1d43df4-7081-420a-a224-61ca03dba63f")
    @TableField(value = "pharmacy_flag")
    private Boolean pharmacyFlag;

    @AutoGenerated(locked = true, uuid = "4eddc2c9-e293-4ae9-bce9-05b4213dfbac")
    @TableField(value = "short_name")
    private String shortName;

    @AutoGenerated(locked = true, uuid = "0cd01057-29d8-4828-877b-a097576cf33a")
    @TableField(value = "sort_number")
    private Integer sortNumber;

    @AutoGenerated(locked = true, uuid = "026d41d0-9242-5ad6-b3ec-ba1ea5ab5b1a")
    @TableField(value = "updated_at")
    private Date updatedAt;
}
