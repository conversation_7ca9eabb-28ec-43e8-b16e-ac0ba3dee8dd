package com.pulse.application.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.application.manager.ApplicationMenuBaseDtoManager;
import com.pulse.application.manager.ApplicationMenuRouterDtoManager;
import com.pulse.application.manager.MenuRouterDtoManager;
import com.pulse.application.manager.converter.ApplicationMenuBaseDtoConverter;
import com.pulse.application.manager.converter.ApplicationMenuRouterDtoConverter;
import com.pulse.application.manager.dto.ApplicationMenuBaseDto;
import com.pulse.application.manager.dto.ApplicationMenuRouterDto;
import com.pulse.application.manager.dto.MenuRouterDto;
import com.pulse.application.persist.dos.ApplicationMenu;
import com.pulse.application.persist.mapper.ApplicationMenuDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "82f4e2c1-d699-477a-912e-e818a5c6c56d|DTO|BASE_MANAGER_IMPL")
public abstract class ApplicationMenuRouterDtoManagerBaseImpl
        implements ApplicationMenuRouterDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private ApplicationMenuBaseDtoConverter applicationMenuBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private ApplicationMenuBaseDtoManager applicationMenuBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private ApplicationMenuDao applicationMenuDao;

    @AutoGenerated(locked = true)
    @Autowired
    private ApplicationMenuRouterDtoConverter applicationMenuRouterDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private MenuRouterDtoManager menuRouterDtoManager;

    @AutoGenerated(locked = true, uuid = "11c6afd1-6afe-3fdf-96b8-f4d7291faf47")
    @Override
    public List<ApplicationMenuRouterDto> getByParentMenuId(String parentMenuId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ApplicationMenuRouterDto> applicationMenuRouterDtoList =
                getByParentMenuIds(Arrays.asList(parentMenuId));
        return applicationMenuRouterDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "1a615c39-36d0-30b9-b39b-95f9ad91a068")
    public List<ApplicationMenuRouterDto> doConvertFromApplicationMenuToApplicationMenuRouterDto(
            List<ApplicationMenu> applicationMenuList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(applicationMenuList)) {
            return Collections.emptyList();
        }

        Map<String, String> menuIdMap =
                applicationMenuList.stream()
                        .filter(i -> i.getMenuId() != null)
                        .collect(
                                Collectors.toMap(
                                        ApplicationMenu::getId, ApplicationMenu::getMenuId));
        List<MenuRouterDto> menuIdMenuRouterDtoList =
                menuRouterDtoManager.getByIds(new ArrayList<>(new HashSet<>(menuIdMap.values())));
        Map<String, MenuRouterDto> menuIdMenuRouterDtoMapRaw =
                menuIdMenuRouterDtoList.stream()
                        .collect(Collectors.toMap(MenuRouterDto::getId, i -> i));
        Map<String, MenuRouterDto> menuIdMenuRouterDtoMap =
                menuIdMap.entrySet().stream()
                        .filter(i -> menuIdMenuRouterDtoMapRaw.get(i.getValue()) != null)
                        .collect(
                                Collectors.toMap(
                                        i -> i.getKey(),
                                        i -> menuIdMenuRouterDtoMapRaw.get(i.getValue())));

        List<ApplicationMenuBaseDto> baseDtoList =
                applicationMenuBaseDtoConverter.convertFromApplicationMenuToApplicationMenuBaseDto(
                        applicationMenuList);
        Map<String, ApplicationMenuRouterDto> dtoMap =
                applicationMenuRouterDtoConverter
                        .convertFromApplicationMenuBaseDtoToApplicationMenuRouterDto(baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        ApplicationMenuRouterDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<ApplicationMenuRouterDto> applicationMenuRouterDtoList = new ArrayList<>();
        for (ApplicationMenu i : applicationMenuList) {
            ApplicationMenuRouterDto applicationMenuRouterDto = dtoMap.get(i.getId());
            if (applicationMenuRouterDto == null) {
                continue;
            }

            if (null != i.getMenuId()) {
                applicationMenuRouterDto.setMenu(
                        menuIdMenuRouterDtoMap.getOrDefault(i.getId(), null));
            }
            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            applicationMenuRouterDtoList.add(applicationMenuRouterDto);
        }
        return applicationMenuRouterDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "340c82a9-47fe-32cc-84f5-9ef49271dab2")
    @Override
    public List<ApplicationMenuRouterDto> getByMenuIds(List<String> menuId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(menuId)) {
            return Collections.emptyList();
        }

        List<ApplicationMenu> applicationMenuList = applicationMenuDao.getByMenuIds(menuId);
        if (CollectionUtil.isEmpty(applicationMenuList)) {
            return Collections.emptyList();
        }

        return doConvertFromApplicationMenuToApplicationMenuRouterDto(applicationMenuList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "396568cb-7cdf-31f5-b401-e367deaa1d0a")
    @Override
    public List<ApplicationMenuRouterDto> getByParentMenuIds(List<String> parentMenuId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(parentMenuId)) {
            return Collections.emptyList();
        }

        List<ApplicationMenu> applicationMenuList =
                applicationMenuDao.getByParentMenuIds(parentMenuId);
        if (CollectionUtil.isEmpty(applicationMenuList)) {
            return Collections.emptyList();
        }

        return doConvertFromApplicationMenuToApplicationMenuRouterDto(applicationMenuList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "7b92b605-c1ab-3217-a2d9-b75a94173eaf")
    @Override
    public List<ApplicationMenuRouterDto> getByMenuId(String menuId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ApplicationMenuRouterDto> applicationMenuRouterDtoList =
                getByMenuIds(Arrays.asList(menuId));
        return applicationMenuRouterDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "90f2ef4e-f78c-3eb9-9772-875a5ce1c0de")
    @Override
    public List<ApplicationMenuRouterDto> getByApplicationId(String applicationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ApplicationMenuRouterDto> applicationMenuRouterDtoList =
                getByApplicationIds(Arrays.asList(applicationId));
        return applicationMenuRouterDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "c7871af0-dc55-3bf4-bdb6-ffbf3425b591")
    @Override
    public ApplicationMenuRouterDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ApplicationMenuRouterDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        ApplicationMenuRouterDto applicationMenuRouterDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return applicationMenuRouterDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "cb891728-7aa2-3c63-b030-69fedc7b7112")
    @Override
    public List<ApplicationMenuRouterDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<ApplicationMenu> applicationMenuList = applicationMenuDao.getByIds(id);
        if (CollectionUtil.isEmpty(applicationMenuList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, ApplicationMenu> applicationMenuMap =
                applicationMenuList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        applicationMenuList =
                id.stream()
                        .map(i -> applicationMenuMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromApplicationMenuToApplicationMenuRouterDto(applicationMenuList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "e40c8fe6-9884-3ad7-8e40-0764867525d9")
    @Override
    public List<ApplicationMenuRouterDto> getByApplicationIds(List<String> applicationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(applicationId)) {
            return Collections.emptyList();
        }

        List<ApplicationMenu> applicationMenuList =
                applicationMenuDao.getByApplicationIds(applicationId);
        if (CollectionUtil.isEmpty(applicationMenuList)) {
            return Collections.emptyList();
        }

        return doConvertFromApplicationMenuToApplicationMenuRouterDto(applicationMenuList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
