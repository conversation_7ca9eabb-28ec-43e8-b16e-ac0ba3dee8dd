package com.pulse.organization.manager.dto;

import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@AutoGenerated(locked = false, uuid = "d90b90f7-320d-4db1-983e-a0d53e0c023c|DTO|DEFINITION")
public class CampusBaseDto {
    /** 院区编号 */
    @AutoGenerated(locked = true, uuid = "682118b8-7246-4ff3-b553-9ad55c2b57b9")
    private String campusNumber;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "90b28513-0022-41be-879c-9d996ad24e39")
    private Date createdAt;

    /** 创建者id */
    @AutoGenerated(locked = true, uuid = "f542ebca-a648-4835-aeb1-29b3b66c7b81")
    private String createdBy;

    /** 删除时间 */
    @AutoGenerated(locked = true, uuid = "52227e0d-bce4-43ec-bcd4-03809238a934")
    private Long deletedAt;

    /** 删除者id */
    @AutoGenerated(locked = true, uuid = "3b8bcb99-2309-4bd7-ae03-f10153c9d471")
    private String deletedBy;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "21a62bbb-bfb9-4c1e-ae4b-312db4eaa082")
    private String id;

    /** 医保编码 */
    @AutoGenerated(locked = true, uuid = "071ab21b-b8f9-4c62-b437-ead71eb20baf")
    private String medicalInsuranceCode;

    /** 组织ID */
    @AutoGenerated(locked = true, uuid = "42f9827b-f172-4ef2-91ad-f77570a73f19")
    private String organizationId;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "454caf2d-**************-2f8e0354ee11")
    private Date updatedAt;

    /** 更新者id */
    @AutoGenerated(locked = true, uuid = "1b17a930-11e0-45c2-a67b-f9bbc39eac43")
    private String updatedBy;
}
