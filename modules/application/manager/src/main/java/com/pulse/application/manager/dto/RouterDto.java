package com.pulse.application.manager.dto;

import com.pulse.application.common.enums.CallTypeEnum;
import com.pulse.application.common.enums.ProgramEnum;
import com.pulse.application.common.enums.RouterTypeEnum;
import com.pulse.application.common.enums.SourceSystemTypeEnum;
import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "6f106570-3c24-41d4-b5b9-94e05885a252|DTO|DEFINITION")
public class RouterDto {
    /** 调用类型 */
    @AutoGenerated(locked = true, uuid = "5ff96511-b2da-4710-a13c-8616daa2321c")
    private CallTypeEnum callType;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "6eb2a029-b52b-430a-90be-400a76ac7e9e")
    private Date createdAt;

    /** 描述 */
    @AutoGenerated(locked = true, uuid = "fb84fb5a-4c7e-41db-9101-0ccfb84d77f1")
    private String description;

    /** 启用标识 */
    @AutoGenerated(locked = true, uuid = "ecc76dc3-e901-48c9-a132-b150fbfffd0f")
    private Boolean enableFlag;

    /** 关联功能ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "b6d8ae32-8fcd-4ed3-bd7b-55f1b862cd72")
    private FeatureBaseDto feature;

    /** 前端页面文件路径 */
    @AutoGenerated(locked = true, uuid = "e7fbf174-c5f6-40c4-94c9-7e0f91d826da")
    private String frontPageUrl;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "3c252ba4-03e3-4f2f-8384-6a25235bb1f7")
    private String id;

    /** 输入码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "e5093e99-87ee-4206-8e6a-ef83765d6444")
    private InputCodeEo inputCode;

    /** 标签ID列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "6eaae11b-e386-4ba7-860e-76a0b81d1e69")
    private List<String> labelIdList;

    /** 名称 */
    @AutoGenerated(locked = true, uuid = "0c1497ad-fd75-4503-8432-4eb3e989c064")
    private String name;

    /** 打开程序名称 */
    @AutoGenerated(locked = true, uuid = "e172fa89-4de2-4b85-9136-f2938054f41d")
    private ProgramEnum openProgramName;

    /** 打开方式 */
    @AutoGenerated(locked = true, uuid = "621d1603-69d6-4301-b766-6d8a163703d1")
    private SourceSystemTypeEnum openType;

    /** 参数 */
    @AutoGenerated(locked = true, uuid = "b8134dc1-299b-4837-8f60-384b1c22fa7c")
    private String parameter;

    /** 路径 */
    @AutoGenerated(locked = true, uuid = "94ca8476-8779-45d3-b056-087628608981")
    private String path;

    /** 路由URL */
    @AutoGenerated(locked = true, uuid = "6bda13d4-df93-4d0d-85fb-67e2c1bed084")
    private String routerUrl;

    /** 排序号 */
    @AutoGenerated(locked = true, uuid = "c4b156ed-c76b-447c-8518-5e8806d2e4eb")
    private Integer sortNumber;

    /** 系统架构 */
    @AutoGenerated(locked = true, uuid = "6a253446-8a9f-41a6-b528-6b8ebbdd1a36")
    private String systemArchitecture;

    /** 类型 */
    @AutoGenerated(locked = true, uuid = "6b239ac4-fa07-41ec-8fb9-8d843b8f2813")
    private RouterTypeEnum type;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "90927834-22ae-4952-9515-d9eb98713204")
    private Date updatedAt;
}
