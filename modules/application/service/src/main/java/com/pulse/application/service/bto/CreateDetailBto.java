package com.pulse.application.service.bto;

import com.pulse.application.common.enums.ApplicationStatusEnum;
import com.pulse.application.common.enums.ApplicationTypeEnum;
import com.pulse.application.service.bto.CreateDetailBto.ApplicationDetailBto.ApplicationOrganization;
import com.pulse.drug_dictionary.common.enums.ProducerDrugTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> Application
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "e9d57302-8b6d-483d-a47a-12f49213d064|BTO|DEFINITION")
public class CreateDetailBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    @Valid
    @AutoGenerated(locked = true, uuid = "d1616b85-b2fd-4fa7-b288-ddefeeab4af1")
    private List<CreateDetailBto.ApplicationOrganizationBto> applicationOrganizationBtoList;

    /** 应用编码 */
    @AutoGenerated(locked = true, uuid = "3eea8d22-f4a5-4969-8939-6e265bbe2704")
    private String code;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "a97fc016-c27d-4599-9af4-7624ff7d72fa")
    private String id;

    /** 状态 */
    @AutoGenerated(locked = true, uuid = "2f53133d-213b-4d1f-9f72-c0327b21b356")
    private ApplicationStatusEnum status;

    @AutoGenerated(locked = true)
    public void setApplicationOrganizationBtoList(
            List<CreateDetailBto.ApplicationOrganizationBto> applicationOrganizationBtoList) {
        this.__$validPropertySet.add("applicationOrganizationBtoList");
        this.applicationOrganizationBtoList = applicationOrganizationBtoList;
    }

    @AutoGenerated(locked = true)
    public void setCode(String code) {
        this.__$validPropertySet.add("code");
        this.code = code;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setStatus(ApplicationStatusEnum status) {
        this.__$validPropertySet.add("status");
        this.status = status;
    }

    /**
     * <b>[源自]</b> ApplicationDetail
     *
     * <p><b>[操作]</b> CREATE
     */
    @Getter
    @NoArgsConstructor
    public static class ApplicationDetailBto {
        /** 目标应用ID */
        @AutoGenerated(locked = true, uuid = "d7bc8114-f9b1-4e7f-aabb-cfd2a29d0dd6")
        private String targetApplicationId;

        /**
         * 目标类型 对应类型：9手麻-门诊药房, 8手麻-住院药房,7手麻-药库,6住院收费-药房,5药
         * 房-窗口,4住院医生站-药房,3病区护士站-药房,2门诊诊间-药房,1门诊收费-药房(手工处方),18对应普通手术应用,17对应介入手术应用,16药房-药房(代发医嘱用药),15住院医生站-三升袋,14病区护士站-三升袋,13住院医生站-化疗,12病区护士站-化疗,11住院医生站-静脉配,10病区护士站-静脉配,23门办-挂号收费
         */
        @AutoGenerated(locked = true, uuid = "cc58948f-8d6b-4b4e-b2b3-02bcc9886672")
        private String targetType;

        /** 启用标志 */
        @AutoGenerated(locked = true, uuid = "bf38da0f-163f-4f8b-bf50-e98288fe597c")
        private Boolean activeFlag;

        /** 类型 项目类型:1西药，2成药，3草药 */
        @AutoGenerated(locked = true, uuid = "19dbf59c-6b44-4eba-8285-252be515a760")
        private ProducerDrugTypeEnum type;

        /** 应用类型 门诊住院标志,0：门诊 1：住院 3：急诊 , 对于病区护士站与药房对应时 0 表示夜间医嘱对应的药房 */
        @AutoGenerated(locked = true, uuid = "577e2dd3-e143-446c-8749-25540c518f81")
        private ApplicationTypeEnum applicationType;

        /** 默认标志 */
        @AutoGenerated(locked = true, uuid = "fdbcd597-a6db-42d6-ba18-c3a31229c8cf")
        private Boolean defaultFlag;

        @Valid
        @AutoGenerated(locked = true, uuid = "e8cf00c8-ca8f-4420-a2a5-55f3664cae50")
        private ApplicationOrganization applicationOrganization;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setTargetApplicationId(String targetApplicationId) {
            this.__$validPropertySet.add("targetApplicationId");
            this.targetApplicationId = targetApplicationId;
        }

        @AutoGenerated(locked = true)
        public void setTargetType(String targetType) {
            this.__$validPropertySet.add("targetType");
            this.targetType = targetType;
        }

        @AutoGenerated(locked = true)
        public void setActiveFlag(Boolean activeFlag) {
            this.__$validPropertySet.add("activeFlag");
            this.activeFlag = activeFlag;
        }

        @AutoGenerated(locked = true)
        public void setType(ProducerDrugTypeEnum type) {
            this.__$validPropertySet.add("type");
            this.type = type;
        }

        @AutoGenerated(locked = true)
        public void setApplicationType(ApplicationTypeEnum applicationType) {
            this.__$validPropertySet.add("applicationType");
            this.applicationType = applicationType;
        }

        @AutoGenerated(locked = true)
        public void setDefaultFlag(Boolean defaultFlag) {
            this.__$validPropertySet.add("defaultFlag");
            this.defaultFlag = defaultFlag;
        }

        @AutoGenerated(locked = true)
        public void setApplicationOrganization(ApplicationOrganization applicationOrganization) {
            this.__$validPropertySet.add("applicationOrganization");
            this.applicationOrganization = applicationOrganization;
        }

        /** 定位器 */
        @Getter
        @Setter
        public static class ApplicationOrganization {
            /** 主键 */
            @AutoGenerated(locked = true, uuid = "c5726f0f-62f8-4ee5-ae15-83321658c3bd")
            private String id;
        }
    }

    /**
     * <b>[源自]</b> ApplicationOrganization
     *
     * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
     */
    @Getter
    @NoArgsConstructor
    public static class ApplicationOrganizationBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "989d2785-7119-4c70-afbe-0d66126e3247")
        private String id;

        @Valid
        @AutoGenerated(locked = true, uuid = "8bd4c679-c0d2-4810-9b4b-2113a0267f59")
        private List<CreateDetailBto.ApplicationDetailBto> applicationDetailBtoList;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setApplicationDetailBtoList(
                List<CreateDetailBto.ApplicationDetailBto> applicationDetailBtoList) {
            this.__$validPropertySet.add("applicationDetailBtoList");
            this.applicationDetailBtoList = applicationDetailBtoList;
        }
    }
}
