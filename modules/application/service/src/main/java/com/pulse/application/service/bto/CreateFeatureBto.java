package com.pulse.application.service.bto;

import com.pulse.application.common.enums.ApplicationStatusEnum;
import com.pulse.application.common.enums.FeatureTypeEnum;
import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> Feature
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "534b03a8-19f5-48d2-834d-8abeff195612|BTO|DEFINITION")
public class CreateFeatureBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 分类ID */
    @AutoGenerated(locked = true, uuid = "1cb66150-5176-4a54-9f15-276489031de2")
    private String categoryId;

    /** 功能编码 */
    @AutoGenerated(locked = true, uuid = "65061331-67c0-4b84-9404-b735da0d3d75")
    private String code;

    /** 描述 */
    @AutoGenerated(locked = true, uuid = "fba2f2cf-e09b-4b7a-8836-1b9192d67949")
    private String description;

    /** 启用标识 */
    @AutoGenerated(locked = true, uuid = "37c6d844-dbe4-4dd1-85ae-274775fd5bab")
    private Boolean enableFlag;

    /** 功能标识 */
    @AutoGenerated(locked = true, uuid = "1aea6678-5eaa-45cc-ab58-5fcb1f005f19")
    private String featureKey;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "cbd23deb-883a-4e7a-b547-fb15ffb183aa")
    private String id;

    /** 输入码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "c835dcba-1f3d-4848-83ec-169d18fc1911")
    private InputCodeEo inputCode;

    /** 名称 */
    @AutoGenerated(locked = true, uuid = "05ca8a24-428e-43f8-a46b-bd406f893a56")
    private String name;

    /** 父功能ID */
    @AutoGenerated(locked = true, uuid = "31c1e1a9-c1ef-4dd2-b812-a64472447655")
    private String parentId;

    /** 排序号 */
    @AutoGenerated(locked = true, uuid = "022f135d-9e6a-4f8e-b94a-f0a9c5c47695")
    private Integer sortNumber;

    /** 状态 */
    @AutoGenerated(locked = true, uuid = "21e3204c-3360-4bbe-82c0-f7fb408d9001")
    private ApplicationStatusEnum status;

    /** 标签ID列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "b5a0806c-70fb-466a-8c37-ff3e7f9c9751")
    private List<String> tagIdList;

    /** 功能类型 */
    @AutoGenerated(locked = true, uuid = "1de8b488-824c-4057-9654-d8bc7b02f1db")
    private FeatureTypeEnum type;

    @AutoGenerated(locked = true)
    public void setCategoryId(String categoryId) {
        this.__$validPropertySet.add("categoryId");
        this.categoryId = categoryId;
    }

    @AutoGenerated(locked = true)
    public void setCode(String code) {
        this.__$validPropertySet.add("code");
        this.code = code;
    }

    @AutoGenerated(locked = true)
    public void setDescription(String description) {
        this.__$validPropertySet.add("description");
        this.description = description;
    }

    @AutoGenerated(locked = true)
    public void setEnableFlag(Boolean enableFlag) {
        this.__$validPropertySet.add("enableFlag");
        this.enableFlag = enableFlag;
    }

    @AutoGenerated(locked = true)
    public void setFeatureKey(String featureKey) {
        this.__$validPropertySet.add("featureKey");
        this.featureKey = featureKey;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setInputCode(InputCodeEo inputCode) {
        this.__$validPropertySet.add("inputCode");
        this.inputCode = inputCode;
    }

    @AutoGenerated(locked = true)
    public void setName(String name) {
        this.__$validPropertySet.add("name");
        this.name = name;
    }

    @AutoGenerated(locked = true)
    public void setParentId(String parentId) {
        this.__$validPropertySet.add("parentId");
        this.parentId = parentId;
    }

    @AutoGenerated(locked = true)
    public void setSortNumber(Integer sortNumber) {
        this.__$validPropertySet.add("sortNumber");
        this.sortNumber = sortNumber;
    }

    @AutoGenerated(locked = true)
    public void setStatus(ApplicationStatusEnum status) {
        this.__$validPropertySet.add("status");
        this.status = status;
    }

    @AutoGenerated(locked = true)
    public void setTagIdList(List<String> tagIdList) {
        this.__$validPropertySet.add("tagIdList");
        this.tagIdList = tagIdList;
    }

    @AutoGenerated(locked = true)
    public void setType(FeatureTypeEnum type) {
        this.__$validPropertySet.add("type");
        this.type = type;
    }
}
