package com.pulse.parameter.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.parameter.entrance.web.query.assembler.ParameterConfigWithValueVoDataAssembler;
import com.pulse.parameter.entrance.web.query.assembler.ParameterConfigWithValueVoDataAssembler.ParameterConfigWithValueVoDataHolder;
import com.pulse.parameter.entrance.web.query.collector.ParameterConfigWithValueVoDataCollector;
import com.pulse.parameter.entrance.web.vo.ParameterConfigWithValueVo;
import com.pulse.parameter.entrance.web.vo.ParameterValueVo;
import com.pulse.parameter.manager.dto.ParameterConfigWithValueDto;
import com.pulse.parameter.manager.dto.ParameterValueBaseDto;
import com.pulse.parameter.service.ParameterConfigBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到ParameterConfigWithValueVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "5e73de5b-dbf0-4277-adcd-4951ebf62079|VO|CONVERTER")
public class ParameterConfigWithValueVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigBaseDtoService parameterConfigBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigWithValueVoDataAssembler parameterConfigWithValueVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigWithValueVoDataCollector parameterConfigWithValueVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private ParameterValueVoConverter parameterValueVoConverter;

    /** 把ParameterConfigWithValueDto转换成ParameterConfigWithValueVo */
    @AutoGenerated(locked = true, uuid = "1a812e5c-30e1-3926-922d-e6e3f9da2025")
    public ParameterConfigWithValueVo convertToParameterConfigWithValueVo(
            ParameterConfigWithValueDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToParameterConfigWithValueVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 使用默认方式组装ParameterConfigWithValueVo数据 */
    @AutoGenerated(locked = true, uuid = "1a9b810a-f19d-30e4-b96e-2bbc2eef041e")
    public ParameterConfigWithValueVo convertAndAssembleData(ParameterConfigWithValueDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把ParameterConfigWithValueDto转换成ParameterConfigWithValueVo */
    @AutoGenerated(locked = false, uuid = "5e73de5b-dbf0-4277-adcd-4951ebf62079-converter-Map")
    public Map<ParameterConfigWithValueDto, ParameterConfigWithValueVo>
            convertToParameterConfigWithValueVoMap(List<ParameterConfigWithValueDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<ParameterValueBaseDto, ParameterValueVo> parameterValueListMap =
                parameterValueVoConverter.convertToParameterValueVoMap(
                        dtoList.stream()
                                .filter(
                                        dto ->
                                                CollectionUtil.isNotEmpty(
                                                        dto.getParameterValueList()))
                                .flatMap(dto -> dto.getParameterValueList().stream())
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<ParameterConfigWithValueDto, ParameterConfigWithValueVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            ParameterConfigWithValueVo vo =
                                                    new ParameterConfigWithValueVo();
                                            vo.setId(dto.getId());
                                            vo.setConfigType(dto.getConfigType());
                                            vo.setCategory(dto.getCategory());
                                            vo.setEnvironmentLevel(dto.getEnvironmentLevel());
                                            vo.setEnvironmentId(dto.getEnvironmentId());
                                            vo.setCode(dto.getCode());
                                            vo.setName(dto.getName());
                                            vo.setInputCode(dto.getInputCode());
                                            vo.setDescription(dto.getDescription());
                                            vo.setValueType(dto.getValueType());
                                            vo.setValueConstraintType(dto.getValueConstraintType());
                                            vo.setDefaultValue(dto.getDefaultValue());
                                            vo.setValueDescription(dto.getValueDescription());
                                            vo.setRuleType(dto.getRuleType());
                                            vo.setRuleConfig(dto.getRuleConfig());
                                            vo.setDatasourceType(dto.getDatasourceType());
                                            vo.setDatasourceValue(dto.getDatasourceValue());
                                            vo.setArrayFlag(dto.getArrayFlag());
                                            vo.setRequiredFlag(dto.getRequiredFlag());
                                            vo.setStatus(dto.getStatus());
                                            vo.setParentConfigId(dto.getParentConfigId());
                                            vo.setLabel(dto.getLabelList());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setParameterValueList(
                                                    dto.getParameterValueList() == null
                                                            ? null
                                                            : dto.getParameterValueList().stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    parameterValueListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            vo.setEnableFlag(dto.getEnableFlag());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把ParameterConfigWithValueDto转换成ParameterConfigWithValueVo */
    @AutoGenerated(locked = true, uuid = "5e73de5b-dbf0-4277-adcd-4951ebf62079-converter-list")
    public List<ParameterConfigWithValueVo> convertToParameterConfigWithValueVoList(
            List<ParameterConfigWithValueDto> dtoList) {
        return new ArrayList<>(convertToParameterConfigWithValueVoMap(dtoList).values());
    }

    /** 使用默认方式组装ParameterConfigWithValueVo列表数据 */
    @AutoGenerated(locked = true, uuid = "7d550e6f-a719-3c84-81b5-31fd2665106a")
    public List<ParameterConfigWithValueVo> convertAndAssembleDataList(
            List<ParameterConfigWithValueDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        ParameterConfigWithValueVoDataHolder dataHolder =
                new ParameterConfigWithValueVoDataHolder();
        dataHolder.setRootBaseDtoList(
                parameterConfigBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(ParameterConfigWithValueDto::getId)
                                .collect(Collectors.toList())));
        Map<String, ParameterConfigWithValueVo> voMap =
                convertToParameterConfigWithValueVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        parameterConfigWithValueVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        parameterConfigWithValueVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
