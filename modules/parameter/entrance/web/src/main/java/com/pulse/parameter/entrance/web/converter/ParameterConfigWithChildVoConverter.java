package com.pulse.parameter.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.parameter.entrance.web.query.assembler.ParameterConfigWithChildVoDataAssembler;
import com.pulse.parameter.entrance.web.vo.ParameterConfigVo;
import com.pulse.parameter.entrance.web.vo.ParameterConfigWithChildVo;
import com.pulse.parameter.manager.dto.ParameterConfigBaseDto;
import com.pulse.parameter.manager.dto.ParameterConfigWithChildDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到ParameterConfigWithChildVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "9c9ab424-d2d7-4ec0-8de1-97f93023d249|VO|CONVERTER")
public class ParameterConfigWithChildVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigVoConverter parameterConfigVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigWithChildVoDataAssembler parameterConfigWithChildVoDataAssembler;

    /** 使用默认方式组装ParameterConfigWithChildVo列表数据 */
    @AutoGenerated(locked = true, uuid = "14f59602-a509-3e20-a17c-3e399abd25b9")
    public List<ParameterConfigWithChildVo> convertAndAssembleDataList(
            List<ParameterConfigWithChildDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, ParameterConfigWithChildVo> voMap =
                convertToParameterConfigWithChildVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        parameterConfigWithChildVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 使用默认方式组装ParameterConfigWithChildVo数据 */
    @AutoGenerated(locked = true, uuid = "2fc4c6ab-**************-3e66d1c6e29b")
    public ParameterConfigWithChildVo convertAndAssembleData(ParameterConfigWithChildDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把ParameterConfigWithChildDto转换成ParameterConfigWithChildVo */
    @AutoGenerated(locked = false, uuid = "9c9ab424-d2d7-4ec0-8de1-97f93023d249-converter-Map")
    public Map<ParameterConfigWithChildDto, ParameterConfigWithChildVo>
            convertToParameterConfigWithChildVoMap(List<ParameterConfigWithChildDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<ParameterConfigBaseDto, ParameterConfigVo> childConfigVoListMap =
                parameterConfigVoConverter.convertToParameterConfigVoMap(
                        dtoList.stream()
                                .filter(
                                        dto ->
                                                CollectionUtil.isNotEmpty(
                                                        dto.getChildConfigDtoList()))
                                .flatMap(dto -> dto.getChildConfigDtoList().stream())
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<ParameterConfigWithChildDto, ParameterConfigWithChildVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            ParameterConfigWithChildVo vo =
                                                    new ParameterConfigWithChildVo();
                                            vo.setId(dto.getId());
                                            vo.setConfigType(dto.getConfigType());
                                            vo.setCategory(dto.getCategory());
                                            vo.setEnvironmentLevel(dto.getEnvironmentLevel());
                                            vo.setEnvironmentId(dto.getEnvironmentId());
                                            vo.setCode(dto.getCode());
                                            vo.setName(dto.getName());
                                            vo.setInputCode(dto.getInputCode());
                                            vo.setDescription(dto.getDescription());
                                            vo.setValueType(dto.getValueType());
                                            vo.setValueConstraintType(dto.getValueConstraintType());
                                            vo.setDefaultValue(dto.getDefaultValue());
                                            vo.setValueDescription(dto.getValueDescription());
                                            vo.setRuleType(dto.getRuleType());
                                            vo.setRuleConfig(dto.getRuleConfig());
                                            vo.setDatasourceType(dto.getDatasourceType());
                                            vo.setDatasourceValue(dto.getDatasourceValue());
                                            vo.setArrayFlag(dto.getArrayFlag());
                                            vo.setRequiredFlag(dto.getRequiredFlag());
                                            vo.setStatus(dto.getStatus());
                                            vo.setParentConfigId(dto.getParentConfigId());
                                            vo.setLabel(dto.getLabelList());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setChildConfigVoList(
                                                    dto.getChildConfigDtoList() == null
                                                            ? null
                                                            : dto.getChildConfigDtoList().stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    childConfigVoListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            vo.setEnableFlag(dto.getEnableFlag());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把ParameterConfigWithChildDto转换成ParameterConfigWithChildVo */
    @AutoGenerated(locked = true, uuid = "9c9ab424-d2d7-4ec0-8de1-97f93023d249-converter-list")
    public List<ParameterConfigWithChildVo> convertToParameterConfigWithChildVoList(
            List<ParameterConfigWithChildDto> dtoList) {
        return new ArrayList<>(convertToParameterConfigWithChildVoMap(dtoList).values());
    }

    /** 把ParameterConfigWithChildDto转换成ParameterConfigWithChildVo */
    @AutoGenerated(locked = true, uuid = "bb1f3013-3b3d-344b-a25e-7f600c964876")
    public ParameterConfigWithChildVo convertToParameterConfigWithChildVo(
            ParameterConfigWithChildDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToParameterConfigWithChildVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }
}
