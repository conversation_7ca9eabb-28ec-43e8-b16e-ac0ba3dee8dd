package com.pulse.parameter.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.parameter.entrance.web.query.assembler.ParameterValueVoDataAssembler;
import com.pulse.parameter.entrance.web.vo.ParameterValueVo;
import com.pulse.parameter.manager.dto.ParameterValueBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到ParameterValueVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "3c2ae59c-04c8-462b-9c15-97df1d97b273|VO|CONVERTER")
public class ParameterValueVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private ParameterValueVoDataAssembler parameterValueVoDataAssembler;

    /** 把ParameterValueBaseDto转换成ParameterValueVo */
    @AutoGenerated(locked = true, uuid = "1fcfec51-3d16-34e2-a085-ecebb1471a19")
    public ParameterValueVo convertToParameterValueVo(ParameterValueBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToParameterValueVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把ParameterValueBaseDto转换成ParameterValueVo */
    @AutoGenerated(locked = false, uuid = "3c2ae59c-04c8-462b-9c15-97df1d97b273-converter-Map")
    public Map<ParameterValueBaseDto, ParameterValueVo> convertToParameterValueVoMap(
            List<ParameterValueBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<ParameterValueBaseDto, ParameterValueVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            ParameterValueVo vo = new ParameterValueVo();
                                            vo.setId(dto.getId());
                                            vo.setConfigId(dto.getConfigId());
                                            vo.setValue(dto.getValue());
                                            vo.setEnvironmentLevel(dto.getEnvironmentLevel());
                                            vo.setEnvironmentId(dto.getEnvironmentId());
                                            vo.setRemark(dto.getRemark());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把ParameterValueBaseDto转换成ParameterValueVo */
    @AutoGenerated(locked = true, uuid = "3c2ae59c-04c8-462b-9c15-97df1d97b273-converter-list")
    public List<ParameterValueVo> convertToParameterValueVoList(
            List<ParameterValueBaseDto> dtoList) {
        return new ArrayList<>(convertToParameterValueVoMap(dtoList).values());
    }

    /** 使用默认方式组装ParameterValueVo数据 */
    @AutoGenerated(locked = true, uuid = "e66e6c1e-f56f-3f57-ac32-292eac2f592b")
    public ParameterValueVo convertAndAssembleData(ParameterValueBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装ParameterValueVo列表数据 */
    @AutoGenerated(locked = true, uuid = "fa544bec-acf1-3939-a54a-02ee5f8fc756")
    public List<ParameterValueVo> convertAndAssembleDataList(List<ParameterValueBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, ParameterValueVo> voMap =
                convertToParameterValueVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        parameterValueVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
