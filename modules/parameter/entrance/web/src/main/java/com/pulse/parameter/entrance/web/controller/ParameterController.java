package com.pulse.parameter.entrance.web.controller;

import com.pulse.parameter.entrance.web.converter.ParameterConfigVoConverter;
import com.pulse.parameter.entrance.web.converter.ParameterConfigWithChildVoConverter;
import com.pulse.parameter.entrance.web.converter.ParameterConfigWithValueVoConverter;
import com.pulse.parameter.entrance.web.vo.ParameterConfigVo;
import com.pulse.parameter.entrance.web.vo.ParameterConfigWithChildVo;
import com.pulse.parameter.entrance.web.vo.ParameterConfigWithValueVo;
import com.pulse.parameter.manager.dto.ParameterConfigBaseDto;
import com.pulse.parameter.manager.dto.ParameterConfigWithChildDto;
import com.pulse.parameter.manager.dto.ParameterConfigWithValueDto;
import com.pulse.parameter.persist.qto.SearchParameterConfigQto;
import com.pulse.parameter.persist.qto.SearchParameterWithValueQto;
import com.pulse.parameter.service.ParameterConfigBOService;
import com.pulse.parameter.service.ParameterConfigBaseDtoService;
import com.pulse.parameter.service.ParameterConfigWithChildDtoService;
import com.pulse.parameter.service.ParameterConfigWithValueDtoService;
import com.pulse.parameter.service.bto.MergeParameterConfigBto;
import com.pulse.parameter.service.query.ParameterConfigBaseDtoQueryService;
import com.pulse.parameter.service.query.ParameterConfigWithValueDtoQueryService;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "3c093763-74a3-36e3-8991-b9e38934e12f")
public class ParameterController {
    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigBOService parameterConfigBOService;

    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigBaseDtoQueryService parameterConfigBaseDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigBaseDtoService parameterConfigBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigVoConverter parameterConfigVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigWithChildDtoService parameterConfigWithChildDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigWithChildVoConverter parameterConfigWithChildVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigWithValueDtoQueryService parameterConfigWithValueDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigWithValueDtoService parameterConfigWithValueDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigWithValueVoConverter parameterConfigWithValueVoConverter;

    /** 停/启用参数 */
    @PublicInterface(id = "0c55dd8f-64d8-4d82-ba60-d14b15a40fdd", version = "1744097335098")
    @AutoGenerated(locked = false, uuid = "0c55dd8f-64d8-4d82-ba60-d14b15a40fdd")
    @RequestMapping(
            value = {"/api/parameter/enable-parameter"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String enableParameter(String parameterConfigId) {
        // TODO implement method
        return parameterConfigBOService.enableParameter(parameterConfigId);
    }

    /** 根据父配置ID获取参数配置列表 */
    @PublicInterface(id = "1b20787d-5bb9-499b-9bc6-e802be1bf4d6", version = "1741768807649")
    @AutoGenerated(locked = false, uuid = "1b20787d-5bb9-499b-9bc6-e802be1bf4d6")
    @RequestMapping(
            value = {"/api/parameter/list-parameter-config-by-parent-config-id"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<ParameterConfigVo> listParameterConfigByParentConfigId(String parentConfigId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ParameterConfigBaseDto> rpcResult =
                parameterConfigBaseDtoService.getByParentConfigId(parentConfigId);
        List<ParameterConfigVo> result =
                parameterConfigVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查询参数元信息 */
    @PublicInterface(id = "2c120416-453d-40b9-b610-5fc9179c53ea", version = "1743499838164")
    @AutoGenerated(locked = false, uuid = "2c120416-453d-40b9-b610-5fc9179c53ea")
    @RequestMapping(
            value = {"/api/parameter/search-parameter-config"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<ParameterConfigVo> searchParameterConfig(@Valid SearchParameterConfigQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ParameterConfigBaseDto> rpcResult =
                parameterConfigBaseDtoQueryService.searchParameterConfig(qto);
        List<ParameterConfigVo> result =
                parameterConfigVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 根据主键获取参数 */
    @PublicInterface(id = "952047f2-e51a-4baf-8993-81afeb2f926a", version = "1743154318267")
    @AutoGenerated(locked = false, uuid = "952047f2-e51a-4baf-8993-81afeb2f926a")
    @RequestMapping(
            value = {"/api/parameter/get-parameter-with-value-by-id"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public ParameterConfigWithValueVo getParameterWithValueById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        ParameterConfigWithValueDto rpcResult = parameterConfigWithValueDtoService.getById(id);
        ParameterConfigWithValueVo result =
                parameterConfigWithValueVoConverter.convertAndAssembleData(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查询参数元信息（分页） */
    @PublicInterface(id = "9e8350c9-e46b-43d7-8896-43d657db8973", version = "1741771200220")
    @AutoGenerated(locked = false, uuid = "9e8350c9-e46b-43d7-8896-43d657db8973")
    @RequestMapping(
            value = {"/api/parameter/search-parameter-config-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<ParameterConfigVo> searchParameterConfigPaged(
            @Valid SearchParameterConfigQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<ParameterConfigBaseDto> dtoResult =
                parameterConfigBaseDtoQueryService.searchParameterConfigPaged(qto);
        VSQueryResult<ParameterConfigVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(
                parameterConfigVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查询参数含参数值（分页） */
    @PublicInterface(id = "b7770737-9c77-4e91-81d7-620461e369cf", version = "1741748866932")
    @AutoGenerated(locked = false, uuid = "b7770737-9c77-4e91-81d7-620461e369cf")
    @RequestMapping(
            value = {"/api/parameter/search-parameter-with-value-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public ParameterConfigWithValueVo searchParameterWithValuePaged(
            @Valid SearchParameterWithValueQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        //        VSQueryResult<ParameterConfigWithValueDto> rpcResult =
        //                parameterConfigWithValueDtoQueryService.searchParameterPaged(qto);
        //        parameterConfigWithValueVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // TODO access response
        return null;
    }

    /** 保存参数元信息 保存参数元信息，并保存子参数元信息 */
    @PublicInterface(id = "b802849a-ed34-447d-92a5-6046e05c99d4", version = "1744098474937")
    @AutoGenerated(locked = false, uuid = "b802849a-ed34-447d-92a5-6046e05c99d4")
    @RequestMapping(
            value = {"/api/parameter/merge-parameter-config-with-child"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeParameterConfigWithChild(
            @Valid MergeParameterConfigBto mergeParameterConfigBto,
            @Valid List<MergeParameterConfigBto> mergeParameterConfigBtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = parameterConfigBOService.mergeParameterConfig(mergeParameterConfigBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 根据ID查询带子配置的参数配置元信息 */
    @PublicInterface(id = "cec78e9f-7a32-433b-b024-80d4cc7bddb1", version = "1741769983292")
    @AutoGenerated(locked = false, uuid = "cec78e9f-7a32-433b-b024-80d4cc7bddb1")
    @RequestMapping(
            value = {"/api/parameter/get-parameter-config-with-child-by-id"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public ParameterConfigWithChildVo getParameterConfigWithChildById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        ParameterConfigWithChildDto rpcResult = parameterConfigWithChildDtoService.getById(id);
        ParameterConfigWithChildVo result =
                parameterConfigWithChildVoConverter.convertAndAssembleData(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }
}
