package com.pulse.parameter.entrance.web.controller;

import com.pulse.parameter.entrance.web.converter.ParameterValueVoConverter;
import com.pulse.parameter.entrance.web.vo.ParameterValueVo;
import com.pulse.parameter.manager.dto.ParameterValueBaseDto;
import com.pulse.parameter.service.ParameterConfigBOService;
import com.pulse.parameter.service.ParameterValueBaseDtoService;
import com.pulse.parameter.service.bto.MergeParameterValueListBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "b652d3c8-f1aa-3f8f-be52-36fb26f391fe")
public class ParameterValueController {
    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigBOService parameterConfigBOService;

    @AutoGenerated(locked = true)
    @Resource
    private ParameterValueBaseDtoService parameterValueBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ParameterValueVoConverter parameterValueVoConverter;

    /** 全量保存参数值 */
    @PublicInterface(id = "58e90984-ac03-467b-915e-5daebe77c29b", version = "1744100017081")
    @AutoGenerated(locked = false, uuid = "58e90984-ac03-467b-915e-5daebe77c29b")
    @RequestMapping(
            value = {"/api/parameter/merge-parameter-value-list"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeParameterValueList(
            @Valid MergeParameterValueListBto mergeParameterValueListBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result =
                parameterConfigBOService.mergeParameterValueList(mergeParameterValueListBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 根据参数配置ID获取参数配置-值信息列表 */
    @PublicInterface(id = "b1a1f596-9f94-4e8f-995f-da8ef2d915ce", version = "1743154925431")
    @AutoGenerated(locked = false, uuid = "b1a1f596-9f94-4e8f-995f-da8ef2d915ce")
    @RequestMapping(
            value = {"/api/parameter/get-parameter-value-by-config-id"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<ParameterValueVo> getParameterValueByConfigId(String configId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ParameterValueBaseDto> rpcResult =
                parameterValueBaseDtoService.getByConfigId(configId);
        List<ParameterValueVo> result =
                parameterValueVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }
}
