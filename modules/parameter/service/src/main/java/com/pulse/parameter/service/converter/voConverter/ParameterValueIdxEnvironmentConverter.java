package com.pulse.parameter.service.converter.voConverter;

import com.pulse.parameter.persist.dos.ParameterValue;
import com.pulse.parameter.persist.dos.ParameterValue.EnvironmentIdAndEnvironmentLevel;
import com.pulse.parameter.persist.eo.IdxEnvironmentEo;
import com.vs.code.AutoGenerated;

@AutoGenerated(locked = true, uuid = "0db1022a-2c36-373b-af9b-88a7a1e98ad8")
public class ParameterValueIdxEnvironmentConverter {

    @AutoGenerated(locked = true)
    public static ParameterValue.EnvironmentIdAndEnvironmentLevel convertFromIdxEnvironmentToInner(
            IdxEnvironmentEo idxEnvironment) {
        if (null == idxEnvironment) {
            return null;
        }

        EnvironmentIdAndEnvironmentLevel environmentIdAndEnvironmentLevel =
                new EnvironmentIdAndEnvironmentLevel();
        environmentIdAndEnvironmentLevel.setEnvironmentId(idxEnvironment.getEnvironmentId());
        environmentIdAndEnvironmentLevel.setEnvironmentLevel(idxEnvironment.getEnvironmentLevel());
        return environmentIdAndEnvironmentLevel;
    }
}
