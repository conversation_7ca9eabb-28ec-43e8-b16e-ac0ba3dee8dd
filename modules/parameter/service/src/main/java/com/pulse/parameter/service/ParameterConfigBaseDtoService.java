package com.pulse.parameter.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.parameter.manager.ParameterConfigBaseDtoManager;
import com.pulse.parameter.manager.dto.ParameterConfigBaseDto;
import com.pulse.parameter.service.converter.ParameterConfigBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "3cf409e3-fe45-4542-a96e-746909372e60|DTO|SERVICE")
public class ParameterConfigBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private ParameterConfigBaseDtoManager parameterConfigBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private ParameterConfigBaseDtoServiceConverter parameterConfigBaseDtoServiceConverter;

    @PublicInterface(
            id = "19d92b70-8635-4997-9ecf-1eec9e50925d",
            module = "parameter",
            moduleId = "965ab305-d041-4cde-93f0-320085d4673b",
            pubRpc = true,
            version = "1744788456590")
    @AutoGenerated(locked = false, uuid = "111b731f-db4a-3ea4-88a2-9fa9dae96e5d")
    public ParameterConfigBaseDto getByCode(@NotNull(message = "编码不能为空") String code) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ParameterConfigBaseDto> ret = getByCodes(Arrays.asList(code));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "d45c3bf2-7e4b-4c60-a134-5e2ff65dd2b1",
            module = "parameter",
            moduleId = "965ab305-d041-4cde-93f0-320085d4673b",
            pubRpc = true,
            version = "1744788205401")
    @AutoGenerated(locked = false, uuid = "23136c55-7c8d-37b7-a3c1-68252ccff95f")
    public List<ParameterConfigBaseDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<ParameterConfigBaseDto> parameterConfigBaseDtoList =
                parameterConfigBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return parameterConfigBaseDtoServiceConverter.ParameterConfigBaseDtoConverter(
                parameterConfigBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "ad20151e-3c0b-4804-8081-03537d870c8a",
            module = "parameter",
            moduleId = "965ab305-d041-4cde-93f0-320085d4673b",
            pubRpc = true,
            version = "1744788205405")
    @AutoGenerated(locked = false, uuid = "679f3e2f-0d40-356c-92a6-6ee0659c9ac0")
    public List<ParameterConfigBaseDto> getByParentConfigId(
            @NotNull(message = "父配置ID不能为空") String parentConfigId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByParentConfigIds(Arrays.asList(parentConfigId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "e1aed805-58f5-46bf-a893-771593a0ecb7",
            module = "parameter",
            moduleId = "965ab305-d041-4cde-93f0-320085d4673b",
            pubRpc = true,
            version = "1744788456594")
    @AutoGenerated(locked = false, uuid = "85285fd3-b3fc-3fc4-b9f2-1e7ceb7915f9")
    public List<ParameterConfigBaseDto> getByCodes(
            @Valid @NotNull(message = "编码不能为空") List<String> code) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        code = new ArrayList<>(new HashSet<>(code));
        List<ParameterConfigBaseDto> parameterConfigBaseDtoList =
                parameterConfigBaseDtoManager.getByCodes(code);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return parameterConfigBaseDtoServiceConverter.ParameterConfigBaseDtoConverter(
                parameterConfigBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "1191dd0c-2295-4b7c-87c6-80f7fa44302e",
            module = "parameter",
            moduleId = "965ab305-d041-4cde-93f0-320085d4673b",
            pubRpc = true,
            version = "1744788205418")
    @AutoGenerated(locked = false, uuid = "8e5a6a7e-5f5c-3e68-bb79-9794101e7205")
    public List<ParameterConfigBaseDto> getByParentConfigIds(
            @Valid @NotNull(message = "父配置ID不能为空") List<String> parentConfigId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        parentConfigId = new ArrayList<>(new HashSet<>(parentConfigId));
        List<ParameterConfigBaseDto> parameterConfigBaseDtoList =
                parameterConfigBaseDtoManager.getByParentConfigIds(parentConfigId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return parameterConfigBaseDtoServiceConverter.ParameterConfigBaseDtoConverter(
                parameterConfigBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "ab451940-957e-4f81-9b85-2f074c24d8b2",
            module = "parameter",
            moduleId = "965ab305-d041-4cde-93f0-320085d4673b",
            pubRpc = true,
            version = "1744788205398")
    @AutoGenerated(locked = false, uuid = "c9f95d9b-ccb0-3d8f-bbae-9bb64f75b38e")
    public ParameterConfigBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ParameterConfigBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
