package com.pulse.parameter.service.query;

import com.pulse.parameter.manager.converter.ParameterConfigWithValueDtoConverter;
import com.pulse.parameter.manager.dto.ParameterConfigBaseDto;
import com.pulse.parameter.manager.dto.ParameterConfigWithValueDto;
import com.pulse.parameter.persist.qto.SearchParameterWithValueQto;
import com.pulse.parameter.service.ParameterConfigBaseDtoService;
import com.pulse.parameter.service.index.entity.SearchParameterWithValueQtoService;
import com.pulse.parameter.service.query.assembler.ParameterConfigWithValueDtoDataAssembler;
import com.pulse.parameter.service.query.assembler.ParameterConfigWithValueDtoDataAssembler.ParameterConfigWithValueDtoDataHolder;
import com.pulse.parameter.service.query.collector.ParameterConfigWithValueDtoDataCollector;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** ParameterConfigWithValueDto查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "ef6f56ad-26ea-3572-b1b8-42058b77fc8a")
public class ParameterConfigWithValueDtoQueryService {
    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigBaseDtoService parameterConfigBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigWithValueDtoConverter parameterConfigWithValueDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigWithValueDtoDataAssembler parameterConfigWithValueDtoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigWithValueDtoDataCollector parameterConfigWithValueDtoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private SearchParameterWithValueQtoService searchParameterWithValueQtoService;

    /** 根据SearchParameterWithValueQto查询ParameterConfigWithValueDto列表,上限500 */
    @PublicInterface(id = "7f6f6cbb-f0dc-40cc-be79-fe1443be14a7", module = "parameter")
    @AutoGenerated(locked = false, uuid = "0a07ef98-ed7f-3280-b66d-63ed8416c047")
    public List<ParameterConfigWithValueDto> searchParameter(
            @Valid @NotNull SearchParameterWithValueQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchParameterWithValueQtoService.query(qto);
        ParameterConfigWithValueDtoDataHolder dataHolder =
                new ParameterConfigWithValueDtoDataHolder();
        List<ParameterConfigWithValueDto> result = toDtoList(ids, dataHolder);
        parameterConfigWithValueDtoDataCollector.collectDataDefault(dataHolder);
        parameterConfigWithValueDtoDataAssembler.assembleData(result, dataHolder);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 根据SearchParameterWithValueQto查询ParameterConfigWithValueDto列表,分页 */
    @PublicInterface(id = "cf5b855c-8cda-444d-8d53-f3d705862cf0", module = "parameter")
    @AutoGenerated(locked = false, uuid = "85e2de2e-7ad7-3b17-a474-c097ce81e260")
    public VSQueryResult<ParameterConfigWithValueDto> searchParameterPaged(
            @Valid @NotNull SearchParameterWithValueQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchParameterWithValueQtoService.queryPaged(qto);
        ParameterConfigWithValueDtoDataHolder dataHolder =
                new ParameterConfigWithValueDtoDataHolder();
        List<ParameterConfigWithValueDto> dtoList = toDtoList(ids, dataHolder);
        parameterConfigWithValueDtoDataCollector.collectDataDefault(dataHolder);
        parameterConfigWithValueDtoDataAssembler.assembleData(dtoList, dataHolder);
        VSQueryResult result = new VSQueryResult();
        result.setCount(searchParameterWithValueQtoService.count(qto));
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 将ID列表转换为DtoList */
    @AutoGenerated(locked = true, uuid = "872b30f7-517b-3950-8b2f-e68c6696d9b0")
    private List<ParameterConfigWithValueDto> toDtoList(
            List<String> ids, ParameterConfigWithValueDtoDataHolder dataHolder) {
        List<ParameterConfigBaseDto> baseDtoList = parameterConfigBaseDtoService.getByIds(ids);
        dataHolder.setRootBaseDtoList(baseDtoList);
        Map<String, ParameterConfigWithValueDto> dtoMap =
                parameterConfigWithValueDtoConverter
                        .convertFromParameterConfigBaseDtoToParameterConfigWithValueDto(baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        ParameterConfigWithValueDto::getId, Function.identity()));
        return ids.stream()
                .map(id -> dtoMap.get(id))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
