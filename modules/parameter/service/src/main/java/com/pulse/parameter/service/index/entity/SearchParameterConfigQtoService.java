package com.pulse.parameter.service.index.entity;

import cn.hutool.core.lang.Assert;

import com.pulse.parameter.persist.mapper.SearchParameterConfigQtoDao;
import com.pulse.parameter.persist.qto.SearchParameterConfigQto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = true, uuid = "776c1dbe-f286-4e28-9f8b-384a74a5f292|QTO|SERVICE")
public class SearchParameterConfigQtoService {
    @AutoGenerated(locked = true)
    @Resource
    private SearchParameterConfigQtoDao searchParameterConfigMapper;

    /** 不分页查询入口 */
    @AutoGenerated(locked = true, uuid = "776c1dbe-f286-4e28-9f8b-384a74a5f292-query")
    public List<String> query(SearchParameterConfigQto qto) {
        return searchParameterConfigMapper.query(qto);
    }

    /** 分页查询入口 */
    @AutoGenerated(locked = true, uuid = "776c1dbe-f286-4e28-9f8b-384a74a5f292-query-paged")
    public List<String> queryPaged(SearchParameterConfigQto qto) {
        Assert.isTrue(qto.getSize() != null && qto.getSize() > 0, "size 字段必须大于0 ");
        Assert.isTrue(qto.getFrom() != null && qto.getFrom() >= 0, "from 字段必须大于等于0 ");
        return searchParameterConfigMapper.queryPaged(qto);
    }

    /** 计算总数量 */
    @AutoGenerated(locked = true)
    public Integer count(SearchParameterConfigQto qto) {
        return searchParameterConfigMapper.count(qto);
    }
}
