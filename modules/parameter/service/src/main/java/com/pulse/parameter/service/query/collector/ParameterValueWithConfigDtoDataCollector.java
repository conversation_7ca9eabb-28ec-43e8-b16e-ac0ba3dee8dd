package com.pulse.parameter.service.query.collector;

import com.pulse.parameter.manager.dto.ParameterConfigBaseDto;
import com.pulse.parameter.manager.dto.ParameterValueBaseDto;
import com.pulse.parameter.service.ParameterConfigBaseDtoService;
import com.pulse.parameter.service.ParameterValueBaseDtoService;
import com.pulse.parameter.service.query.assembler.ParameterValueWithConfigDtoDataAssembler.ParameterValueWithConfigDtoDataHolder;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装ParameterValueWithConfigDto所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "d6d3915d-9cb2-3a9e-94cd-83dd09cd00f3")
public class ParameterValueWithConfigDtoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigBaseDtoService parameterConfigBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ParameterValueBaseDtoService parameterValueBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ParameterValueWithConfigDtoDataCollector parameterValueWithConfigDtoDataCollector;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "b5c5ffce-1d28-3cca-acd7-4e4d6287e42c")
    private void fillDataWhenNecessary(ParameterValueWithConfigDtoDataHolder dataHolder) {
        List<ParameterValueBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.config == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(ParameterValueBaseDto::getConfigId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ParameterConfigBaseDto> baseDtoList =
                    parameterConfigBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(ParameterConfigBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, ParameterConfigBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            ParameterConfigBaseDto::getId, Function.identity()));
            dataHolder.config =
                    rootDtoList.stream()
                            .map(ParameterValueBaseDto::getConfigId)
                            .distinct()
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "ee7a31cd-729e-37b7-88c8-6bdecb0445ac")
    public void collectDataDefault(ParameterValueWithConfigDtoDataHolder dataHolder) {
        parameterValueWithConfigDtoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
