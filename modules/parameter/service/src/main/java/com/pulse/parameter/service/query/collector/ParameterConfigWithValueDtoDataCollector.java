package com.pulse.parameter.service.query.collector;

import com.pulse.parameter.manager.dto.ParameterConfigBaseDto;
import com.pulse.parameter.manager.dto.ParameterValueBaseDto;
import com.pulse.parameter.service.ParameterConfigBaseDtoService;
import com.pulse.parameter.service.ParameterValueBaseDtoService;
import com.pulse.parameter.service.query.assembler.ParameterConfigWithValueDtoDataAssembler.ParameterConfigWithValueDtoDataHolder;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装ParameterConfigWithValueDto所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "61304b3d-80f0-396c-acd8-16551300f7d6")
public class ParameterConfigWithValueDtoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigBaseDtoService parameterConfigBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigWithValueDtoDataCollector parameterConfigWithValueDtoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private ParameterValueBaseDtoService parameterValueBaseDtoService;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "003c90d0-e8dd-3642-9440-ef3f0115b75e")
    private void fillDataWhenNecessary(ParameterConfigWithValueDtoDataHolder dataHolder) {
        List<ParameterConfigBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.parameterValueList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(ParameterConfigBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ParameterValueBaseDto> baseDtoList =
                    parameterValueBaseDtoService.getByConfigIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(ParameterValueBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<ParameterValueBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(ParameterValueBaseDto::getConfigId));
            dataHolder.parameterValueList =
                    rootDtoList.stream()
                            .map(ParameterConfigBaseDto::getId)
                            .distinct()
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "aef9f2cd-defc-300d-b30b-ac24f4b099ab")
    public void collectDataDefault(ParameterConfigWithValueDtoDataHolder dataHolder) {
        parameterConfigWithValueDtoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
