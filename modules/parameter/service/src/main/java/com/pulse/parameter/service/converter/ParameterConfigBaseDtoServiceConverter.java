package com.pulse.parameter.service.converter;

import com.pulse.parameter.manager.dto.ParameterConfigBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "7dcbb4c1-1c8a-3fbe-816c-ab1da841b776")
public class ParameterConfigBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<ParameterConfigBaseDto> ParameterConfigBaseDtoConverter(
            List<ParameterConfigBaseDto> parameterConfigBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return parameterConfigBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
