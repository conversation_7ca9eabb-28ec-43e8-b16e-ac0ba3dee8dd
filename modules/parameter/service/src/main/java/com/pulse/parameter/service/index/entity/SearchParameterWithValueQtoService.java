package com.pulse.parameter.service.index.entity;

import cn.hutool.core.lang.Assert;

import com.pulse.parameter.persist.mapper.SearchParameterWithValueQtoDao;
import com.pulse.parameter.persist.qto.SearchParameterWithValueQto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = true, uuid = "c6960e52-28ea-4aae-ae30-bf858e428ec8|QTO|SERVICE")
public class SearchParameterWithValueQtoService {
    @AutoGenerated(locked = true)
    @Resource
    private SearchParameterWithValueQtoDao searchParameterWithValueMapper;

    /** 不分页查询入口 */
    @AutoGenerated(locked = true, uuid = "c6960e52-28ea-4aae-ae30-bf858e428ec8-query")
    public List<String> query(SearchParameterWithValueQto qto) {
        return searchParameterWithValueMapper.query(qto);
    }

    /** 分页查询入口 */
    @AutoGenerated(locked = true, uuid = "c6960e52-28ea-4aae-ae30-bf858e428ec8-query-paged")
    public List<String> queryPaged(SearchParameterWithValueQto qto) {
        Assert.isTrue(qto.getSize() != null && qto.getSize() > 0, "size 字段必须大于0 ");
        Assert.isTrue(qto.getFrom() != null && qto.getFrom() >= 0, "from 字段必须大于等于0 ");
        return searchParameterWithValueMapper.queryPaged(qto);
    }

    /** 计算总数量 */
    @AutoGenerated(locked = true)
    public Integer count(SearchParameterWithValueQto qto) {
        return searchParameterWithValueMapper.count(qto);
    }
}
