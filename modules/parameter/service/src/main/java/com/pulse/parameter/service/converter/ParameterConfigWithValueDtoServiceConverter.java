package com.pulse.parameter.service.converter;

import com.pulse.parameter.manager.dto.ParameterConfigWithValueDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "6fa65e4d-52b0-372b-89cd-3f48b99f5e74")
public class ParameterConfigWithValueDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<ParameterConfigWithValueDto> ParameterConfigWithValueDtoConverter(
            List<ParameterConfigWithValueDto> parameterConfigWithValueDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return parameterConfigWithValueDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
