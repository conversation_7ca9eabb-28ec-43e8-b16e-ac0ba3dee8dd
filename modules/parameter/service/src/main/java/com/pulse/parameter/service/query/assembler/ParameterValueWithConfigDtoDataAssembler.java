package com.pulse.parameter.service.query.assembler;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.parameter.manager.dto.ParameterConfigBaseDto;
import com.pulse.parameter.manager.dto.ParameterValueBaseDto;
import com.pulse.parameter.manager.dto.ParameterValueWithConfigDto;
import com.pulse.parameter.service.ParameterValueBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** ParameterValueWithConfigDto数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "4ee65e63-e61e-35b7-9455-b0cc659dbc57")
public class ParameterValueWithConfigDtoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private ParameterValueBaseDtoService parameterValueBaseDtoService;

    /** 组装ParameterValueWithConfigDto数据 */
    @AutoGenerated(locked = true, uuid = "3813236f-bd91-3f4a-92ef-f8af415acb2b")
    public void assembleData(
            List<ParameterValueWithConfigDto> dtoList,
            ParameterValueWithConfigDtoDataAssembler.ParameterValueWithConfigDtoDataHolder
                    dataHolder) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        Map<String, ParameterValueBaseDto> baseDtoMap =
                dataHolder.getRootBaseDtoList().stream()
                        .collect(
                                Collectors.toMap(
                                        ParameterValueBaseDto::getId, Function.identity()));

        Map<String, ParameterConfigBaseDto> config =
                dataHolder.config.stream()
                        .collect(
                                Collectors.toMap(
                                        ParameterConfigBaseDto::getId, Function.identity()));

        for (ParameterValueWithConfigDto dto : dtoList) {
            dto.setConfig(
                    Optional.ofNullable(config.get(baseDtoMap.get(dto.getId()).getConfigId()))
                            .orElse(null));
        }

        assembleDataCustomized(dtoList); // 自定义处理逻辑
    }

    /** 批量自定义组装ParameterValueWithConfigDto数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "f4f46b52-d653-3d71-b185-a8b8c74cc198")
    public void assembleDataCustomized(List<ParameterValueWithConfigDto> dataList) {
        // 自定义数据组装

    }

    /** 持有Dto需要的全部数据 */
    @Getter
    @Setter
    public static class ParameterValueWithConfigDtoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<ParameterValueBaseDto> rootBaseDtoList;

        /** 持有dto字段config的Dto数据 */
        @AutoGenerated(locked = true)
        public List<ParameterConfigBaseDto> config;
    }
}
