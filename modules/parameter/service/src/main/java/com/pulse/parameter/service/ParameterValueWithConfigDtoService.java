package com.pulse.parameter.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.parameter.manager.ParameterValueWithConfigDtoManager;
import com.pulse.parameter.manager.dto.ParameterValueWithConfigDto;
import com.pulse.parameter.persist.dos.ParameterValue.EnvironmentIdAndEnvironmentLevel;
import com.pulse.parameter.persist.eo.IdxEnvironmentEo;
import com.pulse.parameter.service.converter.ParameterValueWithConfigDtoServiceConverter;
import com.pulse.parameter.service.converter.voConverter.ParameterValueIdxEnvironmentConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "6528d574-a410-43c8-bd8b-aa9b802a133a|DTO|SERVICE")
public class ParameterValueWithConfigDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private ParameterValueWithConfigDtoManager parameterValueWithConfigDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private ParameterValueWithConfigDtoServiceConverter parameterValueWithConfigDtoServiceConverter;

    @PublicInterface(id = "ee3d7bf5-ae00-4a74-a954-242e1c994b9a", module = "parameter")
    @AutoGenerated(locked = false, uuid = "488876d4-3067-3ea0-8d8a-68392a76ac33")
    public List<ParameterValueWithConfigDto> getByConfigId(
            @NotNull(message = "参数配置ID不能为空") String configId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByConfigIds(Arrays.asList(configId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "d70993b0-5353-4a81-b44c-5eca07e3b8b7", module = "parameter")
    @AutoGenerated(locked = false, uuid = "545f13f5-0600-3ec1-96e9-99535238f321")
    public List<ParameterValueWithConfigDto> getByConfigIds(
            @Valid @NotNull(message = "参数配置ID不能为空") List<String> configId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        configId = new ArrayList<>(new HashSet<>(configId));
        List<ParameterValueWithConfigDto> parameterValueWithConfigDtoList =
                parameterValueWithConfigDtoManager.getByConfigIds(configId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return parameterValueWithConfigDtoServiceConverter.ParameterValueWithConfigDtoConverter(
                parameterValueWithConfigDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "2cccad27-fec0-43f3-a524-f26bad135d25", module = "parameter")
    @AutoGenerated(locked = false, uuid = "5f8ea67e-5ebc-3856-afe9-2451322e09c9")
    public List<ParameterValueWithConfigDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<ParameterValueWithConfigDto> parameterValueWithConfigDtoList =
                parameterValueWithConfigDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return parameterValueWithConfigDtoServiceConverter.ParameterValueWithConfigDtoConverter(
                parameterValueWithConfigDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "4d648d2a-0cbd-4268-a394-fbd4ed0055fe", module = "parameter")
    @AutoGenerated(locked = false, uuid = "7a4d3a17-5f43-34b7-9156-37018c3485c3")
    public ParameterValueWithConfigDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ParameterValueWithConfigDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "917f0201-a112-48f3-b82b-b1e8b8bd4724", module = "parameter")
    @AutoGenerated(locked = false, uuid = "b9bf009f-7131-391c-a141-f5d8adecadc5")
    public List<ParameterValueWithConfigDto> getByEnvironmentIdsAndEnvironmentLevels(
            @Valid @NotNull List<IdxEnvironmentEo> idxEnvironmentEo) {
        List<EnvironmentIdAndEnvironmentLevel> environmentIdAndEnvironmentLevel =
                idxEnvironmentEo.stream()
                        .map(
                                ParameterValueIdxEnvironmentConverter
                                        ::convertFromIdxEnvironmentToInner)
                        .collect(Collectors.toList());
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ParameterValueWithConfigDto> parameterValueWithConfigDtoList =
                parameterValueWithConfigDtoManager.getByEnvironmentIdsAndEnvironmentLevels(
                        environmentIdAndEnvironmentLevel);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return parameterValueWithConfigDtoServiceConverter.ParameterValueWithConfigDtoConverter(
                parameterValueWithConfigDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "d2d6ceb5-0bd6-4090-bc5e-0f84e96c3135", module = "parameter")
    @AutoGenerated(locked = false, uuid = "ca0e3dd7-c858-3e08-84fa-c6ac3bd40e4e")
    public List<ParameterValueWithConfigDto> getByEnvironmentIdAndEnvironmentLevel(
            @Valid @NotNull IdxEnvironmentEo var) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByEnvironmentIdsAndEnvironmentLevels(Arrays.asList(var));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
