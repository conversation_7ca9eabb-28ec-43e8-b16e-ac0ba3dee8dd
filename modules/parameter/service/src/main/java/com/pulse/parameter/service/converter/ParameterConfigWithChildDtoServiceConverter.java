package com.pulse.parameter.service.converter;

import com.pulse.parameter.manager.dto.ParameterConfigWithChildDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "13e5fa0e-b0e1-3518-8a35-129998846af3")
public class ParameterConfigWithChildDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<ParameterConfigWithChildDto> ParameterConfigWithChildDtoConverter(
            List<ParameterConfigWithChildDto> parameterConfigWithChildDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return parameterConfigWithChildDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
