package com.pulse.drug_inventory.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_dictionary.manager.dto.DrugOriginExtensionBaseDto;
import com.pulse.drug_inventory.entrance.web.query.assembler.DrugInventoryRefDrugOriginExtensionBaseVoDataAssembler;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugOriginExtensionBaseVo;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到DrugInventoryRefDrugOriginExtensionBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "75d8523b-5750-45d3-9060-35fa825d429c|VO|CONVERTER")
public class DrugInventoryRefDrugOriginExtensionBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private DrugInventoryRefDrugOriginExtensionBaseVoDataAssembler
            drugInventoryRefDrugOriginExtensionBaseVoDataAssembler;

    /** 把DrugOriginExtensionBaseDto转换成DrugInventoryRefDrugOriginExtensionBaseVo */
    @AutoGenerated(locked = false, uuid = "75d8523b-5750-45d3-9060-35fa825d429c-converter-Map")
    public Map<DrugOriginExtensionBaseDto, DrugInventoryRefDrugOriginExtensionBaseVo>
            convertToDrugInventoryRefDrugOriginExtensionBaseVoMap(
                    List<DrugOriginExtensionBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<DrugOriginExtensionBaseDto, DrugInventoryRefDrugOriginExtensionBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugInventoryRefDrugOriginExtensionBaseVo vo =
                                                    new DrugInventoryRefDrugOriginExtensionBaseVo();
                                            vo.setPurchasePlatformDrugId(
                                                    dto.getPurchasePlatformDrugId());
                                            vo.setQualificationCode(dto.getQualificationCode());
                                            vo.setBidFlag(dto.getBidFlag());
                                            vo.setBidType(dto.getBidType());
                                            vo.setGcpCode(dto.getGcpCode());
                                            vo.setNationalMedicalInsuranceCode(
                                                    dto.getNationalMedicalInsuranceCode());
                                            vo.setNationalMedicalInsuranceName(
                                                    dto.getNationalMedicalInsuranceName());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugOriginExtensionBaseDto转换成DrugInventoryRefDrugOriginExtensionBaseVo */
    @AutoGenerated(locked = true, uuid = "75d8523b-5750-45d3-9060-35fa825d429c-converter-list")
    public List<DrugInventoryRefDrugOriginExtensionBaseVo>
            convertToDrugInventoryRefDrugOriginExtensionBaseVoList(
                    List<DrugOriginExtensionBaseDto> dtoList) {
        return new ArrayList<>(
                convertToDrugInventoryRefDrugOriginExtensionBaseVoMap(dtoList).values());
    }

    /** 把DrugOriginExtensionBaseDto转换成DrugInventoryRefDrugOriginExtensionBaseVo */
    @AutoGenerated(locked = true, uuid = "7dec68ae-529d-3123-b1a4-fff2d0115011")
    public DrugInventoryRefDrugOriginExtensionBaseVo
            convertToDrugInventoryRefDrugOriginExtensionBaseVo(DrugOriginExtensionBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugInventoryRefDrugOriginExtensionBaseVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 使用默认方式组装DrugInventoryRefDrugOriginExtensionBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "9e0179ea-b086-3a14-b578-3c77277cb0a6")
    public List<DrugInventoryRefDrugOriginExtensionBaseVo> convertAndAssembleDataList(
            List<DrugOriginExtensionBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, DrugInventoryRefDrugOriginExtensionBaseVo> voMap =
                convertToDrugInventoryRefDrugOriginExtensionBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        drugInventoryRefDrugOriginExtensionBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 使用默认方式组装DrugInventoryRefDrugOriginExtensionBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "f50f7ab7-b2ce-380e-983a-c8a48fb37739")
    public DrugInventoryRefDrugOriginExtensionBaseVo convertAndAssembleData(
            DrugOriginExtensionBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }
}
