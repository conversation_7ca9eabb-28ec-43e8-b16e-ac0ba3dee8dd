package com.pulse.drug_inventory.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;

import com.pulse.drug_dictionary.manager.dto.DrugNameDictionaryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginExtensionBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginForDetailDto;
import com.pulse.drug_dictionary.manager.dto.DrugSpecificationDictionaryWithDrugDto;
import com.pulse.drug_inventory.entrance.web.query.assembler.DrugInventoryRefDrugOriginForDetailVoDataAssembler;
import com.pulse.drug_inventory.entrance.web.query.assembler.DrugInventoryRefDrugOriginForDetailVoDataAssembler.DrugInventoryRefDrugOriginForDetailVoDataHolder;
import com.pulse.drug_inventory.entrance.web.query.collector.DrugInventoryRefDrugOriginForDetailVoDataCollector;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugNameDictionaryBaseVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugOriginExtensionBaseVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugOriginForDetailVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugOriginInventoryBaseVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugSpecificationDictionaryWithDrugVo;
import com.pulse.drug_inventory.manager.dto.DrugLocationBaseDto;
import com.pulse.drug_inventory.manager.dto.DrugOriginInventoryBaseDto;
import com.pulse.drug_inventory.manager.facade.drug_dictionary.DrugOriginBaseDtoServiceInDrugInventoryRpcAdapter;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到DrugInventoryRefDrugOriginForDetailVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "d51f3767-7c16-4857-af08-afb290b2c6ba|VO|CONVERTER")
public class DrugInventoryRefDrugOriginForDetailVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private DrugInventoryRefDrugNameDictionaryBaseVoConverter
            drugInventoryRefDrugNameDictionaryBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugInventoryRefDrugOriginExtensionBaseVoConverter
            drugInventoryRefDrugOriginExtensionBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugInventoryRefDrugOriginForDetailVoDataAssembler
            drugInventoryRefDrugOriginForDetailVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private DrugInventoryRefDrugOriginForDetailVoDataCollector
            drugInventoryRefDrugOriginForDetailVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private DrugInventoryRefDrugOriginInventoryBaseVoConverter
            drugInventoryRefDrugOriginInventoryBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginBaseDtoServiceInDrugInventoryRpcAdapter
            drugOriginBaseDtoServiceInDrugInventoryRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugSpecificationDictionaryWithDrugVoConverter
            drugSpecificationDictionaryWithDrugVoConverter;

    /** 把DrugOriginForDetailDto转换成DrugInventoryRefDrugOriginForDetailVo */
    @AutoGenerated(locked = true, uuid = "05c2547a-4a65-3a42-ba74-ed98caab76ae")
    public DrugInventoryRefDrugOriginForDetailVo convertToDrugInventoryRefDrugOriginForDetailVo(
            DrugOriginForDetailDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugInventoryRefDrugOriginForDetailVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 使用默认方式组装DrugInventoryRefDrugOriginForDetailVo数据 */
    @AutoGenerated(locked = true, uuid = "b5209f65-62ce-39f6-af0a-46efb6610a72")
    public DrugInventoryRefDrugOriginForDetailVo convertAndAssembleData(
            DrugOriginForDetailDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把DrugOriginForDetailDto转换成DrugInventoryRefDrugOriginForDetailVo */
    @AutoGenerated(locked = false, uuid = "d51f3767-7c16-4857-af08-afb290b2c6ba-converter-Map")
    public Map<DrugOriginForDetailDto, DrugInventoryRefDrugOriginForDetailVo>
            convertToDrugInventoryRefDrugOriginForDetailVoMap(
                    List<DrugOriginForDetailDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<DrugSpecificationDictionaryWithDrugDto, DrugSpecificationDictionaryWithDrugVo>
                drugSpecificationMap =
                        drugSpecificationDictionaryWithDrugVoConverter
                                .convertToDrugSpecificationDictionaryWithDrugVoMap(
                                        dtoList.stream()
                                                .filter(Objects::nonNull)
                                                .map(DrugOriginForDetailDto::getDrugSpecification)
                                                .filter(Objects::nonNull)
                                                .collect(Collectors.toList()));
        Map<DrugOriginExtensionBaseDto, DrugInventoryRefDrugOriginExtensionBaseVo>
                drugOriginExtensionMap =
                        drugInventoryRefDrugOriginExtensionBaseVoConverter
                                .convertToDrugInventoryRefDrugOriginExtensionBaseVoMap(
                                        dtoList.stream()
                                                .filter(Objects::nonNull)
                                                .map(DrugOriginForDetailDto::getDrugOriginExtension)
                                                .filter(Objects::nonNull)
                                                .collect(Collectors.toList()));
        Map<DrugNameDictionaryBaseDto, DrugInventoryRefDrugNameDictionaryBaseVo>
                drugNameDictionaryListMap =
                        drugInventoryRefDrugNameDictionaryBaseVoConverter
                                .convertToDrugInventoryRefDrugNameDictionaryBaseVoMap(
                                        dtoList.stream()
                                                .filter(
                                                        dto ->
                                                                CollectionUtil.isNotEmpty(
                                                                        dto
                                                                                .getDrugNameDictionaryList()))
                                                .flatMap(
                                                        dto ->
                                                                dto
                                                                        .getDrugNameDictionaryList()
                                                                        .stream())
                                                .filter(Objects::nonNull)
                                                .collect(Collectors.toList()));
        Map<DrugOriginInventoryBaseDto, DrugInventoryRefDrugOriginInventoryBaseVo>
                drugOriginInventoryListMap =
                        drugInventoryRefDrugOriginInventoryBaseVoConverter
                                .convertToDrugInventoryRefDrugOriginInventoryBaseVoMap(
                                        dtoList.stream()
                                                .filter(
                                                        dto ->
                                                                CollectionUtil.isNotEmpty(
                                                                        dto
                                                                                .getDrugOriginInventoryList()))
                                                .flatMap(
                                                        dto ->
                                                                dto
                                                                        .getDrugOriginInventoryList()
                                                                        .stream())
                                                .filter(Objects::nonNull)
                                                .collect(Collectors.toList()));
        Map<DrugOriginForDetailDto, DrugInventoryRefDrugOriginForDetailVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugInventoryRefDrugOriginForDetailVo vo =
                                                    new DrugInventoryRefDrugOriginForDetailVo();
                                            vo.setDrugOriginCode(dto.getDrugOriginCode());
                                            vo.setDrugOriginName(dto.getDrugOriginName());
                                            vo.setDrugCode(dto.getDrugCode());
                                            vo.setDrugSpecification(
                                                    dto.getDrugSpecification() == null
                                                            ? null
                                                            : drugSpecificationMap.get(
                                                                    dto.getDrugSpecification()));
                                            vo.setEnableFlag(dto.getEnableFlag());
                                            vo.setPackageSpecification(
                                                    dto.getPackageSpecification());
                                            vo.setPackageUnit(dto.getPackageUnit());
                                            vo.setGcpFlag(dto.getGcpFlag());
                                            vo.setGmpFlag(dto.getGmpFlag());
                                            vo.setTraceabilityCode(dto.getTraceabilityCode());
                                            vo.setDonationFlag(dto.getDonationFlag());
                                            vo.setFreeFlag(dto.getFreeFlag());
                                            vo.setDrugType(dto.getDrugType());
                                            vo.setToxicType(dto.getToxicType());
                                            vo.setWinningNumber(dto.getWinningNumber());
                                            vo.setRegisterBrand(dto.getRegisterBrand());
                                            vo.setDrugOriginExtension(
                                                    dto.getDrugOriginExtension() == null
                                                            ? null
                                                            : drugOriginExtensionMap.get(
                                                                    dto.getDrugOriginExtension()));
                                            vo.setDrugNameDictionaryList(
                                                    dto.getDrugNameDictionaryList() == null
                                                            ? null
                                                            : dto
                                                                    .getDrugNameDictionaryList()
                                                                    .stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    drugNameDictionaryListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            vo.setDrugOriginInventoryList(
                                                    dto.getDrugOriginInventoryList() == null
                                                            ? null
                                                            : dto
                                                                    .getDrugOriginInventoryList()
                                                                    .stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    drugOriginInventoryListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    public Map<DrugOriginForDetailDto, DrugInventoryRefDrugOriginForDetailVo>
            convertToDrugInventoryRefDrugOriginForDetailVoMap(
                    List<DrugOriginForDetailDto> dtoList, String storageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<DrugSpecificationDictionaryWithDrugDto, DrugSpecificationDictionaryWithDrugVo>
                drugSpecificationMap =
                        drugSpecificationDictionaryWithDrugVoConverter
                                .convertToDrugSpecificationDictionaryWithDrugVoMap(
                                        dtoList.stream()
                                                .filter(Objects::nonNull)
                                                .map(DrugOriginForDetailDto::getDrugSpecification)
                                                .filter(Objects::nonNull)
                                                .collect(Collectors.toList()));
        Map<DrugOriginExtensionBaseDto, DrugInventoryRefDrugOriginExtensionBaseVo>
                drugOriginExtensionMap =
                        drugInventoryRefDrugOriginExtensionBaseVoConverter
                                .convertToDrugInventoryRefDrugOriginExtensionBaseVoMap(
                                        dtoList.stream()
                                                .filter(Objects::nonNull)
                                                .map(DrugOriginForDetailDto::getDrugOriginExtension)
                                                .filter(Objects::nonNull)
                                                .collect(Collectors.toList()));
        Map<DrugNameDictionaryBaseDto, DrugInventoryRefDrugNameDictionaryBaseVo>
                drugNameDictionaryListMap =
                        drugInventoryRefDrugNameDictionaryBaseVoConverter
                                .convertToDrugInventoryRefDrugNameDictionaryBaseVoMap(
                                        dtoList.stream()
                                                .filter(
                                                        dto ->
                                                                CollectionUtil.isNotEmpty(
                                                                        dto
                                                                                .getDrugNameDictionaryList()))
                                                .flatMap(
                                                        dto ->
                                                                dto
                                                                        .getDrugNameDictionaryList()
                                                                        .stream())
                                                .filter(Objects::nonNull)
                                                .collect(Collectors.toList()));
        Map<DrugOriginForDetailDto, DrugInventoryRefDrugOriginForDetailVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugInventoryRefDrugOriginForDetailVo vo =
                                                    new DrugInventoryRefDrugOriginForDetailVo();
                                            vo.setDrugOriginCode(dto.getDrugOriginCode());
                                            vo.setDrugOriginName(dto.getDrugOriginName());
                                            vo.setDrugCode(dto.getDrugCode());
                                            vo.setDrugSpecification(
                                                    dto.getDrugSpecification() == null
                                                            ? null
                                                            : drugSpecificationMap.get(
                                                                    dto.getDrugSpecification()));
                                            vo.setEnableFlag(dto.getEnableFlag());
                                            vo.setPackageSpecification(
                                                    dto.getPackageSpecification());
                                            vo.setPackageUnit(dto.getPackageUnit());
                                            vo.setGcpFlag(dto.getGcpFlag());
                                            vo.setGmpFlag(dto.getGmpFlag());
                                            vo.setTraceabilityCode(dto.getTraceabilityCode());
                                            vo.setDonationFlag(dto.getDonationFlag());
                                            vo.setFreeFlag(dto.getFreeFlag());
                                            vo.setDrugType(dto.getDrugType());
                                            vo.setToxicType(dto.getToxicType());
                                            vo.setWinningNumber(dto.getWinningNumber());
                                            vo.setRegisterBrand(dto.getRegisterBrand());
                                            if (CollectionUtil.isNotEmpty(dto.getDrugLocationList())
                                                    && StrUtil.isNotBlank(storageCode)) {
                                                vo.setLocation(
                                                        dto.getDrugLocationList().stream()
                                                                .filter(
                                                                        item ->
                                                                                item.getStorageCode()
                                                                                        .equals(
                                                                                                storageCode))
                                                                .map(
                                                                        DrugLocationBaseDto
                                                                                ::getLocation)
                                                                .collect(Collectors.joining("，")));
                                            }
                                            vo.setDrugOriginExtension(
                                                    dto.getDrugOriginExtension() == null
                                                            ? null
                                                            : drugOriginExtensionMap.get(
                                                                    dto.getDrugOriginExtension()));
                                            vo.setDrugNameDictionaryList(
                                                    dto.getDrugNameDictionaryList() == null
                                                            ? null
                                                            : dto
                                                                    .getDrugNameDictionaryList()
                                                                    .stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    drugNameDictionaryListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugOriginForDetailDto转换成DrugInventoryRefDrugOriginForDetailVo */
    @AutoGenerated(locked = true, uuid = "d51f3767-7c16-4857-af08-afb290b2c6ba-converter-list")
    public List<DrugInventoryRefDrugOriginForDetailVo>
            convertToDrugInventoryRefDrugOriginForDetailVoList(
                    List<DrugOriginForDetailDto> dtoList) {
        return new ArrayList<>(convertToDrugInventoryRefDrugOriginForDetailVoMap(dtoList).values());
    }

    /** 使用默认方式组装DrugInventoryRefDrugOriginForDetailVo列表数据 */
    @AutoGenerated(locked = true, uuid = "da745738-f448-3c4b-b5c2-08a5fd6acf7a")
    public List<DrugInventoryRefDrugOriginForDetailVo> convertAndAssembleDataList(
            List<DrugOriginForDetailDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        DrugInventoryRefDrugOriginForDetailVoDataHolder dataHolder =
                new DrugInventoryRefDrugOriginForDetailVoDataHolder();
        dataHolder.setRootBaseDtoList(
                drugOriginBaseDtoServiceInDrugInventoryRpcAdapter.getByDrugOriginCodes(
                        dtoList.stream()
                                .map(DrugOriginForDetailDto::getDrugOriginCode)
                                .collect(Collectors.toList())));
        Map<String, DrugInventoryRefDrugOriginForDetailVo> voMap =
                convertToDrugInventoryRefDrugOriginForDetailVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getDrugOriginCode(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        drugInventoryRefDrugOriginForDetailVoDataCollector.collectDataWithDtoData(
                dtoList, dataHolder);
        drugInventoryRefDrugOriginForDetailVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getDrugOriginCode()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
