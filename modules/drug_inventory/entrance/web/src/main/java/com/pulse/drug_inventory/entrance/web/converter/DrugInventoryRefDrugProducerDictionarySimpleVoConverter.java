package com.pulse.drug_inventory.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_dictionary.manager.dto.DrugProducerDictionaryBaseDto;
import com.pulse.drug_inventory.entrance.web.query.assembler.DrugInventoryRefDrugProducerDictionarySimpleVoDataAssembler;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugProducerDictionarySimpleVo;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到DrugInventoryRefDrugProducerDictionarySimpleVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "a975a803-623b-40b3-9986-8ca1f51f0847|VO|CONVERTER")
public class DrugInventoryRefDrugProducerDictionarySimpleVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private DrugInventoryRefDrugProducerDictionarySimpleVoDataAssembler
            drugInventoryRefDrugProducerDictionarySimpleVoDataAssembler;

    /** 使用默认方式组装DrugInventoryRefDrugProducerDictionarySimpleVo数据 */
    @AutoGenerated(locked = true, uuid = "7a354d68-e181-328e-bf7c-54cacdb0dbe0")
    public DrugInventoryRefDrugProducerDictionarySimpleVo convertAndAssembleData(
            DrugProducerDictionaryBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把DrugProducerDictionaryBaseDto转换成DrugInventoryRefDrugProducerDictionarySimpleVo */
    @AutoGenerated(locked = false, uuid = "a975a803-623b-40b3-9986-8ca1f51f0847-converter-Map")
    public Map<DrugProducerDictionaryBaseDto, DrugInventoryRefDrugProducerDictionarySimpleVo>
            convertToDrugInventoryRefDrugProducerDictionarySimpleVoMap(
                    List<DrugProducerDictionaryBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<DrugProducerDictionaryBaseDto, DrugInventoryRefDrugProducerDictionarySimpleVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugInventoryRefDrugProducerDictionarySimpleVo vo =
                                                    new DrugInventoryRefDrugProducerDictionarySimpleVo();
                                            vo.setId(dto.getId());
                                            vo.setProducerName(dto.getProducerName());
                                            vo.setProducerNameAlias(dto.getProducerNameAlias());
                                            vo.setInputCode(dto.getInputCode());
                                            vo.setEnableFlag(dto.getEnableFlag());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugProducerDictionaryBaseDto转换成DrugInventoryRefDrugProducerDictionarySimpleVo */
    @AutoGenerated(locked = true, uuid = "a975a803-623b-40b3-9986-8ca1f51f0847-converter-list")
    public List<DrugInventoryRefDrugProducerDictionarySimpleVo>
            convertToDrugInventoryRefDrugProducerDictionarySimpleVoList(
                    List<DrugProducerDictionaryBaseDto> dtoList) {
        return new ArrayList<>(
                convertToDrugInventoryRefDrugProducerDictionarySimpleVoMap(dtoList).values());
    }

    /** 把DrugProducerDictionaryBaseDto转换成DrugInventoryRefDrugProducerDictionarySimpleVo */
    @AutoGenerated(locked = true, uuid = "f2dc1809-6a45-3316-89fc-56b67e6db829")
    public DrugInventoryRefDrugProducerDictionarySimpleVo
            convertToDrugInventoryRefDrugProducerDictionarySimpleVo(
                    DrugProducerDictionaryBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugInventoryRefDrugProducerDictionarySimpleVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 使用默认方式组装DrugInventoryRefDrugProducerDictionarySimpleVo列表数据 */
    @AutoGenerated(locked = true, uuid = "f37ba4ce-b92c-3fc1-8e2c-6c10a79dc810")
    public List<DrugInventoryRefDrugProducerDictionarySimpleVo> convertAndAssembleDataList(
            List<DrugProducerDictionaryBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, DrugInventoryRefDrugProducerDictionarySimpleVo> voMap =
                convertToDrugInventoryRefDrugProducerDictionarySimpleVoMap(dtoList)
                        .entrySet()
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        drugInventoryRefDrugProducerDictionarySimpleVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
