package com.pulse.drug_inventory.entrance.web.controller;

import com.pulse.drug_inventory.service.DrugExportBOService;
import com.pulse.drug_inventory.service.bto.DeleteDrugExportBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "299af16e-2955-3fdd-a478-2f8bb2c62da3")
public class DrugExportBOController {
    @AutoGenerated(locked = true)
    @Resource
    private DrugExportBOService drugExportBOService;

    /** 删除药品出库单 */
    @PublicInterface(id = "ec407987-435f-40ca-a055-ed731c187109", version = "1747039734011")
    @AutoGenerated(locked = false, uuid = "ec407987-435f-40ca-a055-ed731c187109")
    @RequestMapping(
            value = {"/api/drug-inventory/delete-drug-export"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String deleteDrugExport(@Valid @NotNull DeleteDrugExportBto deleteDrugExportBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = drugExportBOService.deleteDrugExport(deleteDrugExportBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }
}
