package com.pulse.drug_inventory.entrance.web.controller;

import com.pulse.drug_inventory.entrance.web.query.executor.ProfileOverstockVoQueryExecutor;
import com.pulse.drug_inventory.entrance.web.vo.ProfileOverstockVo;
import com.pulse.drug_inventory.persist.qto.ListProductOverstockQto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "ffdc85c2-c14a-3600-8726-5d26d51c4406")
public class ProductOverstockController {
    @AutoGenerated(locked = true)
    @Resource
    private ProfileOverstockVoQueryExecutor profileOverstockVoQueryExecutor;

    /** 功能：积压管理-获取积压商品列表（按包装规格） */
    @PublicInterface(id = "cc3bf21b-9b5d-4c98-a2d5-54c8c60b3051", version = "1741054903095")
    @AutoGenerated(locked = false, uuid = "cc3bf21b-9b5d-4c98-a2d5-54c8c60b3051")
    @RequestMapping(
            value = {
                "/api/drug-inventory/query-profile-overstock-vo-by-list-product-overstock-paged"
            },
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<ProfileOverstockVo> queryProfileOverstockVoByListProductOverstockPaged(
            @Valid @NotNull(message = "查询参数不能为空") ListProductOverstockQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<ProfileOverstockVo> result =
                profileOverstockVoQueryExecutor.queryByListProductOverstockPaged(qto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // access response
        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
