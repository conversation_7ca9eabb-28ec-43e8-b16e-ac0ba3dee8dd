package com.pulse.drug_inventory.entrance.web.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;

import com.pulse.drug_inventory.manager.dto.DrugExportDetailAggDto;
import com.pulse.drug_inventory.manager.dto.DrugExportWithDrugExportDetailDto;
import com.pulse.drug_inventory.service.DrugExportWithDrugExportDetailDtoService;
import com.pulse.drug_inventory.service.DrugInventoryFlowService;
import com.pulse.drug_inventory.service.bto.MergeDrugExportBto;
import com.pulse.drug_inventory.service.flow.context.DrugExportBillingContext;
import com.pulse.drug_inventory.service.flow.context.MergeDrugExportFlowContext;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "9b78bb61-2b3a-3abd-9c22-4998cadedb05")
public class DrugExportCustomBOController {

    @Resource private DrugInventoryFlowService drugInventoryFlowService;

    @Resource
    private DrugExportWithDrugExportDetailDtoService drugExportWithDrugExportDetailDtoService;

    /** 保存出库单并记账 */
    @PublicInterface(id = "06d2e36b-cbc3-4ecd-94e3-47741fd837a7", version = "1747037094741")
    @AutoGenerated(locked = false, uuid = "06d2e36b-cbc3-4ecd-94e3-47741fd837a7")
    @RequestMapping(
            value = {"/api/drug-inventory/merge-drug-export-billing"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    @Transactional
    public String mergeDrugExportBilling(@Valid @NotNull MergeDrugExportBto mergeDrugExportBto) {
        MergeDrugExportFlowContext context = new MergeDrugExportFlowContext();
        context.setMergeDrugExportBto(mergeDrugExportBto);
        drugInventoryFlowService.invokeMergeDrugExportFlow(context);
        DrugExportBillingContext exportBillingContext = new DrugExportBillingContext();
        String drugExportId = context.getMergeDrugExportBto().getId();
        exportBillingContext.setDrugExportId(drugExportId);
        drugInventoryFlowService.invokeDrugExportBilling(exportBillingContext);
        return drugExportId;
    }

    /** 保存出库单 */
    @PublicInterface(id = "1c67e1eb-0f3a-487e-8321-ffabbf0cb3f6", version = "1747036869929")
    @AutoGenerated(locked = false, uuid = "1c67e1eb-0f3a-487e-8321-ffabbf0cb3f6")
    @RequestMapping(
            value = {"/api/drug-inventory/merge-drug-export"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    @Transactional
    public String mergeDrugExport(@Valid @NotNull MergeDrugExportBto mergeDrugExportBto) {
        MergeDrugExportFlowContext context = new MergeDrugExportFlowContext();
        context.setMergeDrugExportBto(mergeDrugExportBto);
        drugInventoryFlowService.invokeMergeDrugExportFlow(context);
        return context.getMergeDrugExportBto().getId();
    }

    /** 药品出库记账 */
    @PublicInterface(id = "67866663-26e1-42df-8eca-0b4f0c2911a1", version = "1747036980863")
    @AutoGenerated(locked = false, uuid = "67866663-26e1-42df-8eca-0b4f0c2911a1")
    @RequestMapping(
            value = {"/api/drug-inventory/drug-export-billing"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    @Transactional
    public String drugExportBilling(@NotNull String drugExportId) {
        DrugExportBillingContext exportBillingContext = new DrugExportBillingContext();
        exportBillingContext.setDrugExportId(drugExportId);
        drugInventoryFlowService.invokeDrugExportBilling(exportBillingContext);
        return drugExportId;
    }

    /** 保存冲红出库单并记账 */
    @PublicInterface(id = "cff8c125-9f63-4ff6-be79-4166dc59d2c4", version = "1747984697145")
    @AutoGenerated(locked = false, uuid = "cff8c125-9f63-4ff6-be79-4166dc59d2c4")
    @RequestMapping(
            value = {"/api/drug-inventory/merge-red-drug-export-billing"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeRedDrugExportBilling(@Valid MergeDrugExportBto mergeDrugExportBto) {
        String drugExportId = mergeRedDrugExport(mergeDrugExportBto);
        DrugExportBillingContext exportBillingContext = new DrugExportBillingContext();
        exportBillingContext.setDrugExportId(drugExportId);
        drugInventoryFlowService.invokeDrugExportBilling(exportBillingContext);
        return drugExportId;
    }

    private Map<String, BigDecimal> buildRedDrugImportDetailMap(
            List<DrugExportWithDrugExportDetailDto> redDrugExportList) {
        Map<String, BigDecimal> redDrugImportDetailMap = new HashMap<>();
        for (DrugExportWithDrugExportDetailDto drugImportWithDetailForRpcDto : redDrugExportList) {
            for (DrugExportDetailAggDto drugExportDetailDto :
                    drugImportWithDetailForRpcDto.getDrugExportDetailList()) {
                redDrugImportDetailMap.merge(
                        drugExportDetailDto.getDrugOriginSpecificationId(),
                        drugExportDetailDto.getAmount(),
                        BigDecimal::add);
            }
        }
        return redDrugImportDetailMap;
    }

    /** 保存冲红出库单 */
    @PublicInterface(id = "dc6f7835-fdc3-478c-b388-f2a50ba9cb68", version = "1747984025712")
    @AutoGenerated(locked = false, uuid = "dc6f7835-fdc3-478c-b388-f2a50ba9cb68")
    @RequestMapping(
            value = {"/api/drug-inventory/merge-red-drug-export"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeRedDrugExport(@Valid @NotNull MergeDrugExportBto mergeDrugExportBto) {
        // 保存出库单信息
        Assert.isTrue(StrUtil.isNotBlank(mergeDrugExportBto.getRefundExportId()), "原单据id不能为空");
        if (StrUtil.isBlank(mergeDrugExportBto.getId())) {
            mergeDrugExportBto.setRedFlag(Boolean.TRUE);
        }

        // 查询原出库单
        DrugExportWithDrugExportDetailDto originalDrugExportDto =
                drugExportWithDrugExportDetailDtoService.getById(
                        mergeDrugExportBto.getRefundExportId());
        if (originalDrugExportDto == null) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "原单据不存在");
        }
        Assert.isTrue(!Boolean.TRUE.equals(originalDrugExportDto.getRedFlag()), "原单据是冲红单无法再次冲红");

        Map<String, DrugExportDetailAggDto> originalDrugImportDetailMap =
                originalDrugExportDto.getDrugExportDetailList().stream()
                        .collect(
                                Collectors.toMap(
                                        DrugExportDetailAggDto::getDrugOriginSpecificationId,
                                        Function.identity()));

        // 查询原出库单的冲红单
        List<DrugExportWithDrugExportDetailDto> redDrugImportList =
                drugExportWithDrugExportDetailDtoService.getByRefundExportId(
                        mergeDrugExportBto.getRefundExportId());

        Map<String, BigDecimal> redDrugImportDetailMap =
                CollUtil.isNotEmpty(redDrugImportList)
                        ? buildRedDrugImportDetailMap(redDrugImportList)
                        : new HashMap<>();

        for (MergeDrugExportBto.DrugExportDetailBto drugExportDetailBto :
                mergeDrugExportBto.getDrugExportDetailBtoList()) {
            if (!redDrugImportDetailMap.containsKey(
                    drugExportDetailBto.getDrugOriginSpecificationId())) {
                throw new IgnoredException(ErrorCode.SYS_ERROR, "原单据不存在此药品");
            }
            BigDecimal originalAmount =
                    originalDrugImportDetailMap
                            .get(drugExportDetailBto.getDrugOriginSpecificationId())
                            .getAmount();
            Assert.isTrue(
                    originalAmount
                                    .subtract(
                                            redDrugImportDetailMap.getOrDefault(
                                                    drugExportDetailBto
                                                            .getDrugOriginSpecificationId(),
                                                    BigDecimal.ZERO))
                                    .compareTo(drugExportDetailBto.getAmount())
                            >= 0,
                    "冲红单据药品数量不能超过可冲红数量");
        }

        MergeDrugExportFlowContext mergeDrugExportFlowContext = new MergeDrugExportFlowContext();
        mergeDrugExportFlowContext.setMergeDrugExportBto(mergeDrugExportBto);
        drugInventoryFlowService.invokeMergeDrugExportFlow(mergeDrugExportFlowContext);

        return mergeDrugExportFlowContext.getMergeDrugExportBto().getId();
    }
}
