package com.pulse.drug_inventory.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_dictionary.manager.dto.DrugOriginBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugProducerDictionaryBaseDto;
import com.pulse.drug_inventory.entrance.web.query.assembler.DrugStocktakingDetailAggVoDataAssembler;
import com.pulse.drug_inventory.entrance.web.query.assembler.DrugStocktakingDetailAggVoDataAssembler.DrugStocktakingDetailAggVoDataHolder;
import com.pulse.drug_inventory.entrance.web.query.collector.DrugStocktakingDetailAggVoDataCollector;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugOriginSpecificationSimpleVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugOriginVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugProducerDictionarySimpleVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugStocktakingDetailAggVo;
import com.pulse.drug_inventory.manager.dto.DrugStocktakingDetailAggDto;
import com.pulse.drug_inventory.service.DrugStocktakingDetailBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到DrugStocktakingDetailAggVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "7c8f075b-2339-4476-8eac-6c57100a4005|VO|CONVERTER")
public class DrugStocktakingDetailAggVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private DrugInventoryRefDrugOriginSpecificationSimpleVoConverter
            drugInventoryRefDrugOriginSpecificationSimpleVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugInventoryRefDrugOriginVoConverter drugInventoryRefDrugOriginVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugInventoryRefDrugProducerDictionarySimpleVoConverter
            drugInventoryRefDrugProducerDictionarySimpleVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugStocktakingDetailAggVoDataAssembler drugStocktakingDetailAggVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private DrugStocktakingDetailAggVoDataCollector drugStocktakingDetailAggVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private DrugStocktakingDetailBaseDtoService drugStocktakingDetailBaseDtoService;

    /** 把DrugStocktakingDetailAggDto转换成DrugStocktakingDetailAggVo */
    @AutoGenerated(locked = true, uuid = "11525144-151a-3127-9c5a-5f69995ae60f")
    public DrugStocktakingDetailAggVo convertToDrugStocktakingDetailAggVo(
            DrugStocktakingDetailAggDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugStocktakingDetailAggVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 使用默认方式组装DrugStocktakingDetailAggVo数据 */
    @AutoGenerated(locked = true, uuid = "21d047dd-1658-3932-8020-d23f1124c039")
    public DrugStocktakingDetailAggVo convertAndAssembleData(DrugStocktakingDetailAggDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装DrugStocktakingDetailAggVo列表数据 */
    @AutoGenerated(locked = true, uuid = "6618f45f-89e8-3e6a-9dc1-87dd598d5a71")
    public List<DrugStocktakingDetailAggVo> convertAndAssembleDataList(
            List<DrugStocktakingDetailAggDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        DrugStocktakingDetailAggVoDataHolder dataHolder =
                new DrugStocktakingDetailAggVoDataHolder();
        dataHolder.setRootBaseDtoList(
                drugStocktakingDetailBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(DrugStocktakingDetailAggDto::getId)
                                .collect(Collectors.toList())));
        Map<String, DrugStocktakingDetailAggVo> voMap =
                convertToDrugStocktakingDetailAggVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        drugStocktakingDetailAggVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        drugStocktakingDetailAggVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把DrugStocktakingDetailAggDto转换成DrugStocktakingDetailAggVo */
    @AutoGenerated(locked = false, uuid = "7c8f075b-2339-4476-8eac-6c57100a4005-converter-Map")
    public Map<DrugStocktakingDetailAggDto, DrugStocktakingDetailAggVo>
            convertToDrugStocktakingDetailAggVoMap(List<DrugStocktakingDetailAggDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<DrugOriginSpecificationBaseDto, DrugInventoryRefDrugOriginSpecificationSimpleVo>
                drugOriginSpecificationMap =
                        drugInventoryRefDrugOriginSpecificationSimpleVoConverter
                                .convertToDrugInventoryRefDrugOriginSpecificationSimpleVoMap(
                                        dtoList.stream()
                                                .filter(Objects::nonNull)
                                                .map(
                                                        DrugStocktakingDetailAggDto
                                                                ::getDrugOriginSpecification)
                                                .filter(Objects::nonNull)
                                                .collect(Collectors.toList()));
        Map<DrugProducerDictionaryBaseDto, DrugInventoryRefDrugProducerDictionarySimpleVo>
                drugProducerMap =
                        drugInventoryRefDrugProducerDictionarySimpleVoConverter
                                .convertToDrugInventoryRefDrugProducerDictionarySimpleVoMap(
                                        dtoList.stream()
                                                .filter(Objects::nonNull)
                                                .map(DrugStocktakingDetailAggDto::getDrugProducer)
                                                .filter(Objects::nonNull)
                                                .collect(Collectors.toList()));
        Map<DrugOriginBaseDto, DrugInventoryRefDrugOriginVo> drugOriginCodeMap =
                drugInventoryRefDrugOriginVoConverter.convertToDrugInventoryRefDrugOriginVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(DrugStocktakingDetailAggDto::getDrugOriginCode)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<DrugStocktakingDetailAggDto, DrugStocktakingDetailAggVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugStocktakingDetailAggVo vo =
                                                    new DrugStocktakingDetailAggVo();
                                            vo.setId(dto.getId());
                                            vo.setBatchInventoryId(dto.getBatchInventoryId());
                                            vo.setDrugOriginSpecification(
                                                    dto.getDrugOriginSpecification() == null
                                                            ? null
                                                            : drugOriginSpecificationMap.get(
                                                                    dto
                                                                            .getDrugOriginSpecification()));
                                            vo.setDrugStocktakingId(dto.getDrugStocktakingId());
                                            vo.setDrugProducer(
                                                    dto.getDrugProducer() == null
                                                            ? null
                                                            : drugProducerMap.get(
                                                                    dto.getDrugProducer()));
                                            vo.setAccountTypeCode(dto.getAccountTypeCode());
                                            vo.setAmountPerPackage(dto.getAmountPerPackage());
                                            vo.setBatchNumber(dto.getBatchNumber());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setDifferenceAmount(dto.getDifferenceAmount());
                                            vo.setDifferenceStockCost(dto.getDifferenceStockCost());
                                            vo.setDrugOriginName(dto.getDrugOriginName());
                                            vo.setExpirationDate(dto.getExpirationDate());
                                            vo.setLocationCode(dto.getLocationCode());
                                            vo.setMinDifferenceAmount(dto.getMinDifferenceAmount());
                                            vo.setMinStockAmount(dto.getMinStockAmount());
                                            vo.setMinStocktakingAmount(
                                                    dto.getMinStocktakingAmount());
                                            vo.setPackageDifferenceAmount(
                                                    dto.getPackageDifferenceAmount());
                                            vo.setSpecification(dto.getSpecification());
                                            vo.setPackageStockAmount(dto.getPackageStockAmount());
                                            vo.setPackageStocktakingAmount(
                                                    dto.getPackageStocktakingAmount());
                                            vo.setUnit(dto.getUnit());
                                            vo.setProfitLossType(dto.getProfitLossType());
                                            vo.setRemark(dto.getRemark());
                                            vo.setRetailPrice(dto.getRetailPrice());
                                            vo.setSortNumber(dto.getSortNumber());
                                            vo.setStockAmount(dto.getStockAmount());
                                            vo.setStockPrice(dto.getStockPrice());
                                            vo.setStocktakingAmount(dto.getStocktakingAmount());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setSummaryStocktakingDetailId(
                                                    dto.getSummaryStocktakingDetailId());
                                            vo.setManageMode(dto.getManageMode());
                                            vo.setDrugOriginCode(
                                                    dto.getDrugOriginCode() == null
                                                            ? null
                                                            : drugOriginCodeMap.get(
                                                                    dto.getDrugOriginCode()));
                                            vo.setDifferenceRetailCost(
                                                    dto.getDifferenceRetailCost());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugStocktakingDetailAggDto转换成DrugStocktakingDetailAggVo */
    @AutoGenerated(locked = true, uuid = "7c8f075b-2339-4476-8eac-6c57100a4005-converter-list")
    public List<DrugStocktakingDetailAggVo> convertToDrugStocktakingDetailAggVoList(
            List<DrugStocktakingDetailAggDto> dtoList) {
        return new ArrayList<>(convertToDrugStocktakingDetailAggVoMap(dtoList).values());
    }
}
