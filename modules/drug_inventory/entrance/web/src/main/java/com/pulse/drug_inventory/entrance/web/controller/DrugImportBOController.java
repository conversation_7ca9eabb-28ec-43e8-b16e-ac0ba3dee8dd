package com.pulse.drug_inventory.entrance.web.controller;

import com.pulse.drug_inventory.service.DrugImportBOService;
import com.pulse.drug_inventory.service.bto.DeleteDrugImportDetailBto;
import com.pulse.drug_inventory.service.bto.UpdateDrugDetailInvoiceNumberBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "5d683ed5-8bdf-3cd5-9e46-575e6a288b14")
public class DrugImportBOController {
    @AutoGenerated(locked = true)
    @Resource
    private DrugImportBOService drugImportBOService;

    /** 删除入库单明细 */
    @PublicInterface(id = "53f077aa-d2e2-432d-8e80-c59607853c92", version = "1744797451062")
    @AutoGenerated(locked = false, uuid = "53f077aa-d2e2-432d-8e80-c59607853c92")
    @RequestMapping(
            value = {"/api/drug-inventory/delete-drug-import-detail"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String deleteDrugImportDetail(
            @Valid DeleteDrugImportDetailBto.DrugImportDetailBto drugImportDetailBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = drugImportBOService.deleteDrugImportDetail(drugImportDetailBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 更新入库单明细发票号 */
    @PublicInterface(id = "a7c88416-e016-47d8-b2d7-21ae1307b896", version = "1744861798524")
    @AutoGenerated(locked = false, uuid = "a7c88416-e016-47d8-b2d7-21ae1307b896")
    @RequestMapping(
            value = {"/api/drug-inventory/update-drug-detail-invoice-number"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String updateDrugDetailInvoiceNumber(
            @Valid @NotNull
                    UpdateDrugDetailInvoiceNumberBto.DrugImportDetailBto drugImportDetailBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = drugImportBOService.updateDrugDetailInvoiceNumber(drugImportDetailBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }
}
