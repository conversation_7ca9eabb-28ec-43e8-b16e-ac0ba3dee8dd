package com.pulse.drug_inventory.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationBaseDto;
import com.pulse.drug_inventory.entrance.web.query.assembler.DrugInventoryRefDrugOriginSpecificationSimpleVoDataAssembler;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugOriginSpecificationSimpleVo;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到DrugInventoryRefDrugOriginSpecificationSimpleVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "298c4d6b-6693-4ed3-bdda-baa05d6c8b92|VO|CONVERTER")
public class DrugInventoryRefDrugOriginSpecificationSimpleVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private DrugInventoryRefDrugOriginSpecificationSimpleVoDataAssembler
            drugInventoryRefDrugOriginSpecificationSimpleVoDataAssembler;

    /** 把DrugOriginSpecificationBaseDto转换成DrugInventoryRefDrugOriginSpecificationSimpleVo */
    @AutoGenerated(locked = false, uuid = "298c4d6b-6693-4ed3-bdda-baa05d6c8b92-converter-Map")
    public Map<DrugOriginSpecificationBaseDto, DrugInventoryRefDrugOriginSpecificationSimpleVo>
            convertToDrugInventoryRefDrugOriginSpecificationSimpleVoMap(
                    List<DrugOriginSpecificationBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<DrugOriginSpecificationBaseDto, DrugInventoryRefDrugOriginSpecificationSimpleVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugInventoryRefDrugOriginSpecificationSimpleVo vo =
                                                    new DrugInventoryRefDrugOriginSpecificationSimpleVo();
                                            vo.setId(dto.getId());
                                            vo.setDrugSpecificationDetailId(
                                                    dto.getDrugSpecificationDetailId());
                                            vo.setDrugOriginCode(dto.getDrugOriginCode());
                                            vo.setPriceItemCode(dto.getPriceItemCode());
                                            vo.setSpecificationType(dto.getSpecificationType());
                                            vo.setDrugSpecification(dto.getDrugSpecification());
                                            vo.setUnit(dto.getUnit());
                                            vo.setAmountPerPackage(dto.getAmountPerPackage());
                                            vo.setDefaultUsedFlag(dto.getDefaultUsedFlag());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setBidPurchasePrice(dto.getBidPurchasePrice());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugOriginSpecificationBaseDto转换成DrugInventoryRefDrugOriginSpecificationSimpleVo */
    @AutoGenerated(locked = true, uuid = "298c4d6b-6693-4ed3-bdda-baa05d6c8b92-converter-list")
    public List<DrugInventoryRefDrugOriginSpecificationSimpleVo>
            convertToDrugInventoryRefDrugOriginSpecificationSimpleVoList(
                    List<DrugOriginSpecificationBaseDto> dtoList) {
        return new ArrayList<>(
                convertToDrugInventoryRefDrugOriginSpecificationSimpleVoMap(dtoList).values());
    }

    /** 把DrugOriginSpecificationBaseDto转换成DrugInventoryRefDrugOriginSpecificationSimpleVo */
    @AutoGenerated(locked = true, uuid = "a1b722ca-fa3b-3f3c-b01b-df97766ba206")
    public DrugInventoryRefDrugOriginSpecificationSimpleVo
            convertToDrugInventoryRefDrugOriginSpecificationSimpleVo(
                    DrugOriginSpecificationBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugInventoryRefDrugOriginSpecificationSimpleVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 使用默认方式组装DrugInventoryRefDrugOriginSpecificationSimpleVo数据 */
    @AutoGenerated(locked = true, uuid = "a775de56-45ec-3228-83fc-736774ceb350")
    public DrugInventoryRefDrugOriginSpecificationSimpleVo convertAndAssembleData(
            DrugOriginSpecificationBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装DrugInventoryRefDrugOriginSpecificationSimpleVo列表数据 */
    @AutoGenerated(locked = true, uuid = "b256d8f2-8da2-3684-9332-a9650aa31534")
    public List<DrugInventoryRefDrugOriginSpecificationSimpleVo> convertAndAssembleDataList(
            List<DrugOriginSpecificationBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, DrugInventoryRefDrugOriginSpecificationSimpleVo> voMap =
                convertToDrugInventoryRefDrugOriginSpecificationSimpleVoMap(dtoList)
                        .entrySet()
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        drugInventoryRefDrugOriginSpecificationSimpleVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
