package com.pulse.drug_inventory.entrance.web.controller;

import com.pulse.drug_inventory.entrance.web.converter.DrugImportWithDetailVoConverter;
import com.pulse.drug_inventory.entrance.web.vo.DrugImportWithDetailVo;
import com.pulse.drug_inventory.manager.dto.DrugImportWithDetailDto;
import com.pulse.drug_inventory.persist.qto.SearchDrugImportQto;
import com.pulse.drug_inventory.service.DrugImportWithDetailDtoService;
import com.pulse.drug_inventory.service.query.DrugImportWithDetailDtoQueryService;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "63f1f075-e856-350b-bed3-a6d12a685a84")
public class DrugImportController {
    @AutoGenerated(locked = true)
    @Resource
    private DrugImportWithDetailDtoQueryService drugImportWithDetailDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugImportWithDetailDtoService drugImportWithDetailDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugImportWithDetailVoConverter drugImportWithDetailVoConverter;

    /** 根据主键获取入库单 */
    @PublicInterface(id = "1b78b32f-6354-4fc6-b2c8-2c1e900e05e6", version = "1744861131366")
    @AutoGenerated(locked = false, uuid = "1b78b32f-6354-4fc6-b2c8-2c1e900e05e6")
    @RequestMapping(
            value = {"/api/drug-inventory/get-drug-import-by-id"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public DrugImportWithDetailVo getDrugImportById(@NotNull String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        DrugImportWithDetailDto rpcResult = drugImportWithDetailDtoService.getById(id);
        DrugImportWithDetailVo result =
                drugImportWithDetailVoConverter.convertAndAssembleData(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 根据入库单号获取入库单 */
    @PublicInterface(id = "365acc3b-1b28-4bd8-af67-d9c433c5147d", version = "1747905320836")
    @AutoGenerated(locked = false, uuid = "365acc3b-1b28-4bd8-af67-d9c433c5147d")
    @RequestMapping(
            value = {"/api/drug-inventory/get-drug-import-by-document-number"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public DrugImportWithDetailVo getDrugImportByDocumentNumber(@NotNull String documentNumber) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        DrugImportWithDetailDto rpcResult =
                drugImportWithDetailDtoService.getByDocumentNumber(documentNumber);
        DrugImportWithDetailVo result =
                drugImportWithDetailVoConverter.convertAndAssembleData(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 搜索药品入库单 */
    @PublicInterface(id = "5b84b9e9-3c25-49f3-8b04-fc04aa64f2f3", version = "1745565078891")
    @AutoGenerated(locked = false, uuid = "5b84b9e9-3c25-49f3-8b04-fc04aa64f2f3")
    @RequestMapping(
            value = {"/api/drug-inventory/search-drug-import-waterfall"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<DrugImportWithDetailVo> searchDrugImportWaterfall(
            @Valid SearchDrugImportQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<DrugImportWithDetailDto> dtoResult =
                drugImportWithDetailDtoQueryService.searchDrugImportWaterfall(qto);
        VSQueryResult<DrugImportWithDetailVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(
                drugImportWithDetailVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }
}
