package com.pulse.drug_inventory.entrance.web.controller;

import com.pulse.drug_inventory.entrance.web.converter.DrugLossReportBaseVoConverter;
import com.pulse.drug_inventory.entrance.web.converter.DrugLossReportVoConverter;
import com.pulse.drug_inventory.entrance.web.vo.DrugLossReportBaseVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugLossReportVo;
import com.pulse.drug_inventory.manager.dto.DrugLossReportBaseDto;
import com.pulse.drug_inventory.manager.dto.DrugLossReportDto;
import com.pulse.drug_inventory.persist.qto.GetLossReportByNumberQto;
import com.pulse.drug_inventory.persist.qto.SearchLossReportQto;
import com.pulse.drug_inventory.service.DrugLossReportBOService;
import com.pulse.drug_inventory.service.bto.DeleteLossReportBto;
import com.pulse.drug_inventory.service.bto.MergeLossReportBto;
import com.pulse.drug_inventory.service.query.DrugLossReportBaseDtoQueryService;
import com.pulse.drug_inventory.service.query.DrugLossReportDtoQueryService;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "f61f47f3-dfb6-338a-84d9-50635a76d856")
public class DrugLossReportController {
    @AutoGenerated(locked = true)
    @Resource
    private DrugLossReportBOService drugLossReportBOService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugLossReportBaseDtoQueryService drugLossReportBaseDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugLossReportBaseVoConverter drugLossReportBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugLossReportDtoQueryService drugLossReportDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugLossReportVoConverter drugLossReportVoConverter;

    /** 查询报损单 查询报损单分页 */
    @PublicInterface(id = "3d0e232b-4569-4272-87bd-3908d445b2e7", version = "1746499501164")
    @AutoGenerated(locked = false, uuid = "3d0e232b-4569-4272-87bd-3908d445b2e7")
    @RequestMapping(
            value = {"/api/drug-inventory/search-loss-report-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<DrugLossReportBaseVo> searchLossReportPaged(
            @Valid SearchLossReportQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<DrugLossReportBaseDto> dtoResult =
                drugLossReportBaseDtoQueryService.searchLossReportPaged(qto);
        VSQueryResult<DrugLossReportBaseVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(
                drugLossReportBaseVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** merge报损单（按标识是否记账） */
    @PublicInterface(id = "4f1dc985-623f-4b4f-86f1-912895ddaebb", version = "1747825815249")
    @AutoGenerated(locked = false, uuid = "4f1dc985-623f-4b4f-86f1-912895ddaebb")
    @RequestMapping(
            value = {"/api/drug-inventory/merge-loss-report"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeLossReport(@Valid MergeLossReportBto mergeLossReportBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = drugLossReportBOService.mergeLossReport(mergeLossReportBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 获取报损单按单号 */
    @PublicInterface(id = "62517290-4ccc-48fd-8290-3b2dfb48c735", version = "1746500211062")
    @AutoGenerated(locked = false, uuid = "62517290-4ccc-48fd-8290-3b2dfb48c735")
    @RequestMapping(
            value = {"/api/drug-inventory/get-loss-report-by-number"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<DrugLossReportVo> getLossReportByNumber(@Valid GetLossReportByNumberQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugLossReportDto> rpcResult =
                drugLossReportDtoQueryService.getLossReportByNumber(qto);
        List<DrugLossReportVo> result =
                drugLossReportVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 删除报损单 */
    @PublicInterface(id = "be7ec3e3-8c9c-4f28-82e3-9af23c1375ef", version = "1746499841834")
    @AutoGenerated(locked = false, uuid = "be7ec3e3-8c9c-4f28-82e3-9af23c1375ef")
    @RequestMapping(
            value = {"/api/drug-inventory/delete-loss-report"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String deleteLossReport(@Valid DeleteLossReportBto deleteLossReportBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = drugLossReportBOService.deleteLossReport(deleteLossReportBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 报损单记账 按单号对报损单进行记账 */
    @PublicInterface(id = "e59477e2-da42-4647-9155-0d4eee1326e5", version = "*************")
    @AutoGenerated(locked = false, uuid = "e59477e2-da42-4647-9155-0d4eee1326e5")
    @RequestMapping(
            value = {"/api/drug-inventory/accountant-loss-report"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String accountantLossReport(String lossReportNumber) {
        // TODO implement method
        return null;
    }
}
