package com.pulse.drug_inventory.entrance.web.controller;

import com.pulse.drug_inventory.entrance.web.converter.DrugStocktakingForDetailVoConverter;
import com.pulse.drug_inventory.entrance.web.query.executor.DrugStocktakingVoQueryExecutor;
import com.pulse.drug_inventory.entrance.web.vo.DrugStocktakingForDetailVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugStocktakingVo;
import com.pulse.drug_inventory.manager.dto.DrugStocktakingForDetailDto;
import com.pulse.drug_inventory.persist.qto.SearchDrugStocktakingQto;
import com.pulse.drug_inventory.service.DrugStocktakingBOService;
import com.pulse.drug_inventory.service.DrugStocktakingForDetailDtoService;
import com.pulse.drug_inventory.service.bto.DeleteStocktakingBto;
import com.pulse.drug_inventory.service.bto.MergeStocktakingBto;
import com.pulse.drug_inventory.service.bto.UpdateDrugStocktakingStatusBto;
import com.pulse.drug_inventory.service.helper.DrugStocktakingHelper;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "b955b8e6-0056-37cc-b474-a29e4bc943ed")
public class DrugStocktakingController {
    @AutoGenerated(locked = true)
    @Resource
    private DrugStocktakingBOService drugStocktakingBOService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugStocktakingForDetailDtoService drugStocktakingForDetailDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugStocktakingForDetailVoConverter drugStocktakingForDetailVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugStocktakingVoQueryExecutor drugStocktakingVoQueryExecutor;

    @Resource private DrugStocktakingHelper drugStocktakingHelper;

    /** 根据主键获取药品盘点 */
    @PublicInterface(id = "3a4fbb1c-0dcc-4461-ba00-381580f037a9", version = "1748422826255")
    @AutoGenerated(locked = false, uuid = "3a4fbb1c-0dcc-4461-ba00-381580f037a9")
    @RequestMapping(
            value = {"/api/drug-inventory/get-drug_stocktaking-for-detail-by-id"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public DrugStocktakingForDetailVo getDrugStocktakingForDetailById(String stocktakingId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        DrugStocktakingForDetailDto rpcResult =
                drugStocktakingForDetailDtoService.getById(stocktakingId);
        DrugStocktakingForDetailVo result =
                drugStocktakingForDetailVoConverter.convertAndAssembleData(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 功能：删除盘存单 */
    @PublicInterface(id = "5d1ef528-e56a-484b-b116-c80dedc5ca3f", version = "1747906665478")
    @AutoGenerated(locked = false, uuid = "5d1ef528-e56a-484b-b116-c80dedc5ca3f")
    @RequestMapping(
            value = {"/api/drug-inventory/delete-stocktaking"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String deleteStocktaking(@Valid DeleteStocktakingBto deleteStocktakingBto) {
        drugStocktakingHelper.validateDeleteStocktaking(deleteStocktakingBto.getId());
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = drugStocktakingBOService.deleteStocktaking(deleteStocktakingBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 功能：盘存-保存盘存单 */
    @PublicInterface(id = "69fe56d9-36ec-490d-85f2-fff241578f0c", version = "1747964361645")
    @AutoGenerated(locked = false, uuid = "69fe56d9-36ec-490d-85f2-fff241578f0c")
    @RequestMapping(
            value = {"/api/drug-inventory/merge-stocktaking"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeStocktaking(@Valid MergeStocktakingBto mergeStocktakingBto) {
        drugStocktakingHelper.validateMeregeStockTakingStatus(mergeStocktakingBto);
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = drugStocktakingBOService.mergeStocktaking(mergeStocktakingBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 汇总盘存单 */
    @PublicInterface(id = "7105fbb6-d9d2-4463-bb58-1f6ebbae9a58", version = "1747985053177")
    @AutoGenerated(locked = false, uuid = "7105fbb6-d9d2-4463-bb58-1f6ebbae9a58")
    @RequestMapping(
            value = {"/api/drug-inventory/summary-drug-stocktaking"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String summaryDrugStocktaking() {
        return drugStocktakingBOService.summaryDrugStocktaking();
    }

    /** 获取药品盘存单列表 */
    @PublicInterface(id = "749588d1-331d-4ffc-9cc5-373e5bc38ec2", version = "1747903402122")
    @AutoGenerated(locked = false, uuid = "749588d1-331d-4ffc-9cc5-373e5bc38ec2")
    @RequestMapping(
            value = {"/api/drug-inventory/search-drug-stocktaking-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<DrugStocktakingVo> searchDrugStocktakingPaged(
            @Valid @NotNull(message = "查询参数不能为空") SearchDrugStocktakingQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<DrugStocktakingVo> result =
                drugStocktakingVoQueryExecutor.searchDrugStocktakingPaged(qto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // access response
        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 撤销汇总盘存单 */
    @PublicInterface(id = "a1d8809f-7475-4d07-810b-8399bb95e47e", version = "1747914724494")
    @AutoGenerated(locked = false, uuid = "a1d8809f-7475-4d07-810b-8399bb95e47e")
    @Transactional
    @RequestMapping(
            value = {"/api/drug-inventory/revoke-summary-drug-stocktaking"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public Boolean revokeSummaryDrugStocktaking(String id) {
        return drugStocktakingBOService.revokeSummaryDrugStocktaking(id);
    }

    /** 更新盘存单状态 */
    @PublicInterface(id = "a321ba9c-bc1b-4aec-8020-b5ebdb55def5", version = "1747911738237")
    @AutoGenerated(locked = false, uuid = "a321ba9c-bc1b-4aec-8020-b5ebdb55def5")
    @RequestMapping(
            value = {"/api/drug-inventory/update-drug-stocktaking-status"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String updateDrugStocktakingStatus(
            @Valid UpdateDrugStocktakingStatusBto updateDrugStocktakingStatusBto) {
        drugStocktakingHelper.validateUpdateStocktakingStatus(updateDrugStocktakingStatusBto);
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result =
                drugStocktakingBOService.updateDrugStocktakingStatus(
                        updateDrugStocktakingStatusBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }
}
