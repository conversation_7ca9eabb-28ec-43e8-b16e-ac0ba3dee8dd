package com.pulse.drug_inventory.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_dictionary.manager.dto.DrugOriginBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugProducerDictionaryBaseDto;
import com.pulse.drug_inventory.entrance.web.query.assembler.ProfileOverstockVoDataAssembler;
import com.pulse.drug_inventory.entrance.web.query.assembler.ProfileOverstockVoDataAssembler.ProfileOverstockVoDataHolder;
import com.pulse.drug_inventory.entrance.web.query.collector.ProfileOverstockVoDataCollector;
import com.pulse.drug_inventory.entrance.web.vo.ProfileOverstockVo;
import com.pulse.drug_inventory.entrance.web.vo.ProfileOverstockVo.DrugFirmDictionaryBaseVo;
import com.pulse.drug_inventory.entrance.web.vo.ProfileOverstockVo.DrugProductBaseVo;
import com.pulse.drug_inventory.manager.dto.DrugStorageProfileBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到ProfileOverstockVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "3b4349a4-e529-4823-bfdd-a5dd1980ac87|VO|CONVERTER")
public class ProfileOverstockVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private ProfileOverstockVoDataAssembler profileOverstockVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private ProfileOverstockVoDataCollector profileOverstockVoDataCollector;

    /** 使用默认方式组装ProfileOverstockVo数据 */
    @AutoGenerated(locked = true, uuid = "0f28a0f3-ab57-334d-9893-6d6b42326df4")
    public ProfileOverstockVo convertAndAssembleData(DrugStorageProfileBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把DrugStorageProfileBaseDto转换成ProfileOverstockVo */
    @AutoGenerated(locked = false, uuid = "3b4349a4-e529-4823-bfdd-a5dd1980ac87-converter-Map")
    public Map<DrugStorageProfileBaseDto, ProfileOverstockVo> convertToProfileOverstockVoMap(
            List<DrugStorageProfileBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<DrugStorageProfileBaseDto, ProfileOverstockVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            ProfileOverstockVo vo = new ProfileOverstockVo();
                                            vo.setId(dto.getId());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugStorageProfileBaseDto转换成ProfileOverstockVo */
    @AutoGenerated(locked = true, uuid = "3b4349a4-e529-4823-bfdd-a5dd1980ac87-converter-list")
    public List<ProfileOverstockVo> convertToProfileOverstockVoList(
            List<DrugStorageProfileBaseDto> dtoList) {
        return new ArrayList<>(convertToProfileOverstockVoMap(dtoList).values());
    }

    /** 把DrugStorageProfileBaseDto转换成ProfileOverstockVo */
    @AutoGenerated(locked = true, uuid = "68d6aae7-ce62-3677-b362-0e5ac461b303")
    public ProfileOverstockVo convertToProfileOverstockVo(DrugStorageProfileBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToProfileOverstockVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装ProfileOverstockVo列表数据 */
    @AutoGenerated(locked = true, uuid = "719836a7-bdef-3161-b457-90f2cc82c853")
    public List<ProfileOverstockVo> convertAndAssembleDataList(
            List<DrugStorageProfileBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        ProfileOverstockVoDataHolder dataHolder = new ProfileOverstockVoDataHolder();
        dataHolder.setRootBaseDtoList(dtoList);
        Map<String, ProfileOverstockVo> voMap =
                convertToProfileOverstockVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        profileOverstockVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        profileOverstockVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把DrugOriginBaseDto转换成DrugProductBaseVo */
    @AutoGenerated(locked = false, uuid = "b68ae933-34fe-4c43-8345-3a2e5fa472bd-converter-Map")
    public Map<DrugOriginBaseDto, ProfileOverstockVo.DrugProductBaseVo>
            convertToDrugProductBaseVoMap(List<DrugOriginBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<DrugOriginBaseDto, DrugProductBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugProductBaseVo vo = new DrugProductBaseVo();
                                            vo.setDrugOriginCode(dto.getDrugOriginCode());
                                            vo.setDrugOriginName(dto.getDrugOriginName());
                                            vo.setPackageSpecification(
                                                    dto.getPackageSpecification());
                                            vo.setPackageUnit(dto.getPackageUnit());
                                            vo.setAmountPerPackage(dto.getAmountPerPackage());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugOriginBaseDto转换成DrugProductBaseVo */
    @AutoGenerated(locked = true, uuid = "b68ae933-34fe-4c43-8345-3a2e5fa472bd-converter-list")
    public List<ProfileOverstockVo.DrugProductBaseVo> convertToDrugProductBaseVoList(
            List<DrugOriginBaseDto> dtoList) {
        return new ArrayList<>(convertToDrugProductBaseVoMap(dtoList).values());
    }

    /** 把DrugProducerDictionaryBaseDto转换成DrugFirmDictionaryBaseVo */
    @AutoGenerated(locked = false, uuid = "f39831fd-6ad4-4952-ab69-7873c21d25b0-converter-Map")
    public Map<DrugProducerDictionaryBaseDto, ProfileOverstockVo.DrugFirmDictionaryBaseVo>
            convertToDrugFirmDictionaryBaseVoMap(List<DrugProducerDictionaryBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<DrugProducerDictionaryBaseDto, DrugFirmDictionaryBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugFirmDictionaryBaseVo vo =
                                                    new DrugFirmDictionaryBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setProducerName(dto.getProducerName());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugProducerDictionaryBaseDto转换成DrugFirmDictionaryBaseVo */
    @AutoGenerated(locked = true, uuid = "f39831fd-6ad4-4952-ab69-7873c21d25b0-converter-list")
    public List<ProfileOverstockVo.DrugFirmDictionaryBaseVo> convertToDrugFirmDictionaryBaseVoList(
            List<DrugProducerDictionaryBaseDto> dtoList) {
        return new ArrayList<>(convertToDrugFirmDictionaryBaseVoMap(dtoList).values());
    }

    /** 把DrugProducerDictionaryBaseDto转换成DrugFirmDictionaryBaseVo */
    @AutoGenerated(locked = true, uuid = "f53cc2db-314a-3c19-bfa2-52035c13bc1d")
    public ProfileOverstockVo.DrugFirmDictionaryBaseVo convertToDrugFirmDictionaryBaseVo(
            DrugProducerDictionaryBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugFirmDictionaryBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把DrugOriginBaseDto转换成DrugProductBaseVo */
    @AutoGenerated(locked = true, uuid = "fede8ceb-53bb-3d15-b059-b7668bb8e6e1")
    public ProfileOverstockVo.DrugProductBaseVo convertToDrugProductBaseVo(DrugOriginBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugProductBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }
}
