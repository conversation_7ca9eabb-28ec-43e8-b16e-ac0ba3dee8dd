package com.pulse.drug_inventory.entrance.web.controller;

import cn.hutool.core.util.ReflectUtil;

import com.pulse.drug_financial.service.mq.mo.SingleDrugPriceAdjustMo;
import com.pulse.drug_inventory.service.mq.consumer.SingleDrugPriceAdjustMoConsumer;
import com.vs.code.AutoGenerated;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;

@Controller
@AutoGenerated(locked = true, uuid = "e4f3d1ac-747c-3e5c-b6f2-9593ee958c9d")
public class DrugInventoryMessageConsumerMocker {
    @AutoGenerated(locked = true)
    @Value("${mock.enabled:false}")
    private Boolean mockEnabled;

    @AutoGenerated(locked = true)
    @Resource
    private SingleDrugPriceAdjustMoConsumer singleDrugPriceAdjustMoConsumer;

    @AutoGenerated(locked = true)
    @RequestMapping("/api/mocker/message/drug_inventory/single_drug_price_adjust_mo")
    public Boolean mockSingleDrugPriceAdjustMoConsumer(
            @RequestBody SingleDrugPriceAdjustMo singleDrugPriceAdjustMo) {
        if (this.mockEnabled) {
            return ReflectUtil.invoke(
                    singleDrugPriceAdjustMoConsumer, "handleMessage", singleDrugPriceAdjustMo);
        } else {
            throw new RuntimeException("非法请求!");
        }
    }
}
