package com.pulse.drug_inventory.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_inventory.entrance.web.query.assembler.DrugLossReportBaseVoDataAssembler;
import com.pulse.drug_inventory.entrance.web.vo.DrugLossReportBaseVo;
import com.pulse.drug_inventory.manager.dto.DrugLossReportBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到DrugLossReportBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "484d1317-29ba-4e4d-b98a-67dc294928e4|VO|CONVERTER")
public class DrugLossReportBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private DrugLossReportBaseVoDataAssembler drugLossReportBaseVoDataAssembler;

    /** 把DrugLossReportBaseDto转换成DrugLossReportBaseVo */
    @AutoGenerated(locked = true, uuid = "123b66bf-a52a-3608-be6a-603538eb8cd6")
    public DrugLossReportBaseVo convertToDrugLossReportBaseVo(DrugLossReportBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugLossReportBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把DrugLossReportBaseDto转换成DrugLossReportBaseVo */
    @AutoGenerated(locked = false, uuid = "484d1317-29ba-4e4d-b98a-67dc294928e4-converter-Map")
    public Map<DrugLossReportBaseDto, DrugLossReportBaseVo> convertToDrugLossReportBaseVoMap(
            List<DrugLossReportBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<DrugLossReportBaseDto, DrugLossReportBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugLossReportBaseVo vo = new DrugLossReportBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setReportLossNumber(dto.getReportLossNumber());
                                            vo.setStorageCode(dto.getStorageCode());
                                            vo.setExportImportCode(dto.getExportImportCode());
                                            vo.setDepartmentId(dto.getDepartmentId());
                                            vo.setApplyStaffId(dto.getApplyStaffId());
                                            vo.setApplyDateTime(dto.getApplyDateTime());
                                            vo.setReviewStaffId(dto.getReviewStaffId());
                                            vo.setReviewDateTime(dto.getReviewDateTime());
                                            vo.setAccountantFlag(dto.getAccountantFlag());
                                            vo.setAccountantStaffId(dto.getAccountantStaffId());
                                            vo.setAccountantDateTime(dto.getAccountantDateTime());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setModifyDateTime(dto.getModifyDateTime());
                                            vo.setRemark(dto.getRemark());
                                            vo.setPrintFlag(dto.getPrintFlag());
                                            vo.setDocumentType(dto.getDocumentType());
                                            vo.setAccountingPeriodId(dto.getAccountingPeriodId());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugLossReportBaseDto转换成DrugLossReportBaseVo */
    @AutoGenerated(locked = true, uuid = "484d1317-29ba-4e4d-b98a-67dc294928e4-converter-list")
    public List<DrugLossReportBaseVo> convertToDrugLossReportBaseVoList(
            List<DrugLossReportBaseDto> dtoList) {
        return new ArrayList<>(convertToDrugLossReportBaseVoMap(dtoList).values());
    }

    /** 使用默认方式组装DrugLossReportBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "7ed64056-63d4-35fc-9cc4-f3e1aa8fa99a")
    public List<DrugLossReportBaseVo> convertAndAssembleDataList(
            List<DrugLossReportBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, DrugLossReportBaseVo> voMap =
                convertToDrugLossReportBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        drugLossReportBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 使用默认方式组装DrugLossReportBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "a7c2c874-38f0-3b3d-ac39-f6c3fdf48313")
    public DrugLossReportBaseVo convertAndAssembleData(DrugLossReportBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }
}
