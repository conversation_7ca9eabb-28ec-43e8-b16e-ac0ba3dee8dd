package com.pulse.drug_inventory.entrance.web.controller;

import com.pulse.drug_inventory.entrance.web.query.executor.DrugOriginLocationVoQueryExecutor;
import com.pulse.drug_inventory.entrance.web.vo.DrugOriginLocationVo;
import com.pulse.drug_inventory.persist.qto.ListProductLocationQto;
import com.pulse.drug_inventory.service.DrugOriginInventoryBOService;
import com.pulse.drug_inventory.service.bto.UpdateVitalAmountBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "02f20a39-f843-3444-9811-e6c721043ea6")
public class InventoryManageController {
    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginInventoryBOService drugOriginInventoryBOService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginLocationVoQueryExecutor drugOriginLocationVoQueryExecutor;

    /** 功能：库存管理-修改商品虚库存 */
    @PublicInterface(id = "8b300fbb-b32f-4c7e-b817-abbbeda12c9b", version = "1741075626981")
    @AutoGenerated(locked = false, uuid = "8b300fbb-b32f-4c7e-b817-abbbeda12c9b")
    @RequestMapping(
            value = {"/api/drug-inventory/update-vital-amount"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String updateVitalAmount(@Valid UpdateVitalAmountBto updateVitalAmountBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = drugOriginInventoryBOService.updateVitalAmount(updateVitalAmountBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 功能：库存管理-商品摆放位置下拉 */
    @PublicInterface(id = "a29f4486-03bc-443e-8c1e-068cd35fd0a5", version = "1741059147984")
    @AutoGenerated(locked = false, uuid = "a29f4486-03bc-443e-8c1e-068cd35fd0a5")
    @RequestMapping(
            value = {
                "/api/drug-inventory/query-drug-product-location-vo-by-list-product-location-waterfall"
            },
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<DrugOriginLocationVo>
            queryDrugProductLocationVoByListProductLocationWaterfall(
                    @Valid @NotNull(message = "查询参数不能为空") ListProductLocationQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<DrugOriginLocationVo> result =
                drugOriginLocationVoQueryExecutor.queryByListProductLocationWaterfall(qto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // access response
        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 功能：库存管理-调整预占库存 */
    @PublicInterface(id = "f86cca8e-45db-4db8-a1ad-58a15da1a6a5", version = "1741069984893")
    @AutoGenerated(locked = false, uuid = "f86cca8e-45db-4db8-a1ad-58a15da1a6a5")
    @RequestMapping(
            value = {"/api/drug-inventory/update-pre-occupied-amount"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public Boolean updatePreOccupiedAmount(String productCode) {
        // TODO implement method
        return null;
    }
}
