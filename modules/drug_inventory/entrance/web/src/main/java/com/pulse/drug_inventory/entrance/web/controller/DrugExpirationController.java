package com.pulse.drug_inventory.entrance.web.controller;

import com.pulse.drug_inventory.entrance.web.converter.DrugOriginInventoryExpirationVoConverter;
import com.pulse.drug_inventory.entrance.web.vo.DrugOriginInventoryExpirationVo;
import com.pulse.drug_inventory.manager.dto.DrugOriginInventoryExpirationDto;
import com.pulse.drug_inventory.persist.qto.SearchExpirationQto;
import com.pulse.drug_inventory.service.query.DrugOriginInventoryExpirationDtoQueryService;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.validation.Valid;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "a8d364c1-50c7-3201-92b2-49ce98575e10")
public class DrugExpirationController {
    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginInventoryExpirationDtoQueryService
            drugOriginInventoryExpirationDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginInventoryExpirationVoConverter drugOriginInventoryExpirationVoConverter;

    /** 按条件效期药品查询 */
    @PublicInterface(id = "2c1d3f06-0c3d-410a-b19a-44f20c3dad21", version = "1748229067710")
    @AutoGenerated(locked = false, uuid = "2c1d3f06-0c3d-410a-b19a-44f20c3dad21")
    @RequestMapping(
            value = {"/api/drug-inventory/search-expiration-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<DrugOriginInventoryExpirationVo> searchExpirationPaged(
            @Valid SearchExpirationQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<DrugOriginInventoryExpirationDto> dtoResult =
                drugOriginInventoryExpirationDtoQueryService.searchExpirationPaged(qto);
        VSQueryResult<DrugOriginInventoryExpirationVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(
                drugOriginInventoryExpirationVoConverter.convertAndAssembleDataList(
                        dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }
}
