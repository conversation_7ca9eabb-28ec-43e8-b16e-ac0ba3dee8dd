package com.pulse.drug_inventory.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_dictionary.manager.dto.DrugCategoryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugDictionaryWithCatalogDto;
import com.pulse.drug_inventory.entrance.web.query.assembler.DrugInventoryRefDrugDictionaryWithCatalogVoDataAssembler;
import com.pulse.drug_inventory.entrance.web.query.assembler.DrugInventoryRefDrugDictionaryWithCatalogVoDataAssembler.DrugInventoryRefDrugDictionaryWithCatalogVoDataHolder;
import com.pulse.drug_inventory.entrance.web.query.collector.DrugInventoryRefDrugDictionaryWithCatalogVoDataCollector;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugCategoryVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugDictionaryWithCatalogVo;
import com.pulse.drug_inventory.manager.facade.drug_dictionary.DrugDictionaryBaseDtoServiceInDrugInventoryRpcAdapter;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到DrugInventoryRefDrugDictionaryWithCatalogVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "0ac7a6d4-5b38-43f8-9ddf-4f471968ec18|VO|CONVERTER")
public class DrugInventoryRefDrugDictionaryWithCatalogVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private DrugDictionaryBaseDtoServiceInDrugInventoryRpcAdapter
            drugDictionaryBaseDtoServiceInDrugInventoryRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugInventoryRefDrugCategoryVoConverter drugInventoryRefDrugCategoryVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugInventoryRefDrugDictionaryWithCatalogVoDataAssembler
            drugInventoryRefDrugDictionaryWithCatalogVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private DrugInventoryRefDrugDictionaryWithCatalogVoDataCollector
            drugInventoryRefDrugDictionaryWithCatalogVoDataCollector;

    /** 把DrugDictionaryWithCatalogDto转换成DrugInventoryRefDrugDictionaryWithCatalogVo */
    @AutoGenerated(locked = false, uuid = "0ac7a6d4-5b38-43f8-9ddf-4f471968ec18-converter-Map")
    public Map<DrugDictionaryWithCatalogDto, DrugInventoryRefDrugDictionaryWithCatalogVo>
            convertToDrugInventoryRefDrugDictionaryWithCatalogVoMap(
                    List<DrugDictionaryWithCatalogDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<DrugCategoryBaseDto, DrugInventoryRefDrugCategoryVo> drugCatalogMap =
                drugInventoryRefDrugCategoryVoConverter.convertToDrugInventoryRefDrugCategoryVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(DrugDictionaryWithCatalogDto::getDrugCatalog)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<DrugDictionaryWithCatalogDto, DrugInventoryRefDrugDictionaryWithCatalogVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugInventoryRefDrugDictionaryWithCatalogVo vo =
                                                    new DrugInventoryRefDrugDictionaryWithCatalogVo();
                                            vo.setDrugCode(dto.getDrugCode());
                                            vo.setDrugName(dto.getDrugName());
                                            vo.setCommonNameCode(dto.getCommonNameCode());
                                            vo.setDrugType(dto.getDrugType());
                                            vo.setToxicType(dto.getToxicType());
                                            vo.setHerbType(dto.getHerbType());
                                            vo.setDrugCatalog(
                                                    dto.getDrugCatalog() == null
                                                            ? null
                                                            : drugCatalogMap.get(
                                                                    dto.getDrugCatalog()));
                                            vo.setEnglishName(dto.getEnglishName());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugDictionaryWithCatalogDto转换成DrugInventoryRefDrugDictionaryWithCatalogVo */
    @AutoGenerated(locked = true, uuid = "0ac7a6d4-5b38-43f8-9ddf-4f471968ec18-converter-list")
    public List<DrugInventoryRefDrugDictionaryWithCatalogVo>
            convertToDrugInventoryRefDrugDictionaryWithCatalogVoList(
                    List<DrugDictionaryWithCatalogDto> dtoList) {
        return new ArrayList<>(
                convertToDrugInventoryRefDrugDictionaryWithCatalogVoMap(dtoList).values());
    }

    /** 使用默认方式组装DrugInventoryRefDrugDictionaryWithCatalogVo列表数据 */
    @AutoGenerated(locked = true, uuid = "481cda80-f64c-3162-8bb5-7b8b57e5e27c")
    public List<DrugInventoryRefDrugDictionaryWithCatalogVo> convertAndAssembleDataList(
            List<DrugDictionaryWithCatalogDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        DrugInventoryRefDrugDictionaryWithCatalogVoDataHolder dataHolder =
                new DrugInventoryRefDrugDictionaryWithCatalogVoDataHolder();
        dataHolder.setRootBaseDtoList(
                drugDictionaryBaseDtoServiceInDrugInventoryRpcAdapter.getByDrugCodes(
                        dtoList.stream()
                                .map(DrugDictionaryWithCatalogDto::getDrugCode)
                                .collect(Collectors.toList())));
        Map<String, DrugInventoryRefDrugDictionaryWithCatalogVo> voMap =
                convertToDrugInventoryRefDrugDictionaryWithCatalogVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getDrugCode(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        drugInventoryRefDrugDictionaryWithCatalogVoDataCollector.collectDataWithDtoData(
                dtoList, dataHolder);
        drugInventoryRefDrugDictionaryWithCatalogVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getDrugCode()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 使用默认方式组装DrugInventoryRefDrugDictionaryWithCatalogVo数据 */
    @AutoGenerated(locked = true, uuid = "540180cd-bf94-33d1-b9f5-774dc30a89b0")
    public DrugInventoryRefDrugDictionaryWithCatalogVo convertAndAssembleData(
            DrugDictionaryWithCatalogDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把DrugDictionaryWithCatalogDto转换成DrugInventoryRefDrugDictionaryWithCatalogVo */
    @AutoGenerated(locked = true, uuid = "b959b04f-0ed9-3fc4-8a1a-bf07a50f728e")
    public DrugInventoryRefDrugDictionaryWithCatalogVo
            convertToDrugInventoryRefDrugDictionaryWithCatalogVo(DrugDictionaryWithCatalogDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugInventoryRefDrugDictionaryWithCatalogVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }
}
