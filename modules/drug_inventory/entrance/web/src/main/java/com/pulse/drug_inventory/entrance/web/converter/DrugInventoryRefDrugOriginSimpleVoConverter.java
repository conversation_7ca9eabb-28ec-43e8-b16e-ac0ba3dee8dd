package com.pulse.drug_inventory.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_dictionary.manager.dto.DrugOriginBaseDto;
import com.pulse.drug_inventory.entrance.web.query.assembler.DrugInventoryRefDrugOriginSimpleVoDataAssembler;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugOriginSimpleVo;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到DrugInventoryRefDrugOriginSimpleVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "bf2df2e5-ca86-4fcd-8ead-0ca9917808c4|VO|CONVERTER")
public class DrugInventoryRefDrugOriginSimpleVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private DrugInventoryRefDrugOriginSimpleVoDataAssembler
            drugInventoryRefDrugOriginSimpleVoDataAssembler;

    /** 使用默认方式组装DrugInventoryRefDrugOriginSimpleVo列表数据 */
    @AutoGenerated(locked = true, uuid = "977cc27c-75c6-30ad-8375-bfb8792ca68b")
    public List<DrugInventoryRefDrugOriginSimpleVo> convertAndAssembleDataList(
            List<DrugOriginBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, DrugInventoryRefDrugOriginSimpleVo> voMap =
                convertToDrugInventoryRefDrugOriginSimpleVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getDrugOriginCode(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        drugInventoryRefDrugOriginSimpleVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getDrugOriginCode()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 使用默认方式组装DrugInventoryRefDrugOriginSimpleVo数据 */
    @AutoGenerated(locked = true, uuid = "b3662f38-a98a-3662-8da6-53ca4a5a205e")
    public DrugInventoryRefDrugOriginSimpleVo convertAndAssembleData(DrugOriginBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把DrugOriginBaseDto转换成DrugInventoryRefDrugOriginSimpleVo */
    @AutoGenerated(locked = false, uuid = "bf2df2e5-ca86-4fcd-8ead-0ca9917808c4-converter-Map")
    public Map<DrugOriginBaseDto, DrugInventoryRefDrugOriginSimpleVo>
            convertToDrugInventoryRefDrugOriginSimpleVoMap(List<DrugOriginBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<DrugOriginBaseDto, DrugInventoryRefDrugOriginSimpleVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugInventoryRefDrugOriginSimpleVo vo =
                                                    new DrugInventoryRefDrugOriginSimpleVo();
                                            vo.setDrugOriginCode(dto.getDrugOriginCode());
                                            vo.setDrugOriginName(dto.getDrugOriginName());
                                            vo.setDrugCode(dto.getDrugCode());
                                            vo.setDrugSpecificationId(dto.getDrugSpecificationId());
                                            vo.setDrugProducerId(dto.getDrugProducerId());
                                            vo.setEnableFlag(dto.getEnableFlag());
                                            vo.setGcpFlag(dto.getGcpFlag());
                                            vo.setCentralPurchaseFlag(dto.getCentralPurchaseFlag());
                                            vo.setGmpFlag(dto.getGmpFlag());
                                            vo.setTraceabilityCode(dto.getTraceabilityCode());
                                            vo.setDonationFlag(dto.getDonationFlag());
                                            vo.setFreeFlag(dto.getFreeFlag());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugOriginBaseDto转换成DrugInventoryRefDrugOriginSimpleVo */
    @AutoGenerated(locked = true, uuid = "bf2df2e5-ca86-4fcd-8ead-0ca9917808c4-converter-list")
    public List<DrugInventoryRefDrugOriginSimpleVo> convertToDrugInventoryRefDrugOriginSimpleVoList(
            List<DrugOriginBaseDto> dtoList) {
        return new ArrayList<>(convertToDrugInventoryRefDrugOriginSimpleVoMap(dtoList).values());
    }

    /** 把DrugOriginBaseDto转换成DrugInventoryRefDrugOriginSimpleVo */
    @AutoGenerated(locked = true, uuid = "e0a1fbad-3495-3dd0-955b-035349fde30c")
    public DrugInventoryRefDrugOriginSimpleVo convertToDrugInventoryRefDrugOriginSimpleVo(
            DrugOriginBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugInventoryRefDrugOriginSimpleVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }
}
