package com.pulse.drug_inventory.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_inventory.entrance.web.query.assembler.DrugStockBatchBaseVoDataAssembler;
import com.pulse.drug_inventory.entrance.web.vo.DrugStockBatchBaseVo;
import com.pulse.drug_inventory.manager.dto.DrugStockBatchBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到DrugStockBatchBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "2389eb77-9428-48d6-b533-0d9ebb02cfbf|VO|CONVERTER")
public class DrugStockBatchBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private DrugStockBatchBaseVoDataAssembler drugStockBatchBaseVoDataAssembler;

    /** 把DrugStockBatchBaseDto转换成DrugStockBatchBaseVo */
    @AutoGenerated(locked = false, uuid = "2389eb77-9428-48d6-b533-0d9ebb02cfbf-converter-Map")
    public Map<DrugStockBatchBaseDto, DrugStockBatchBaseVo> convertToDrugStockBatchBaseVoMap(
            List<DrugStockBatchBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<DrugStockBatchBaseDto, DrugStockBatchBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugStockBatchBaseVo vo = new DrugStockBatchBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setDrugOriginCode(dto.getDrugOriginCode());
                                            vo.setDrugOriginSpecificationId(
                                                    dto.getDrugOriginSpecificationId());
                                            vo.setStorageCode(dto.getStorageCode());
                                            vo.setSupplierId(dto.getSupplierId());
                                            vo.setBatchDate(dto.getBatchDate());
                                            vo.setBatchNumber(dto.getBatchNumber());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setDiscountRate(dto.getDiscountRate());
                                            vo.setExpirationDate(dto.getExpirationDate());
                                            vo.setImportDateTime(dto.getImportDateTime());
                                            vo.setImportId(dto.getImportId());
                                            vo.setSupplyFlag(dto.getSupplyFlag());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugStockBatchBaseDto转换成DrugStockBatchBaseVo */
    @AutoGenerated(locked = true, uuid = "2389eb77-9428-48d6-b533-0d9ebb02cfbf-converter-list")
    public List<DrugStockBatchBaseVo> convertToDrugStockBatchBaseVoList(
            List<DrugStockBatchBaseDto> dtoList) {
        return new ArrayList<>(convertToDrugStockBatchBaseVoMap(dtoList).values());
    }

    /** 把DrugStockBatchBaseDto转换成DrugStockBatchBaseVo */
    @AutoGenerated(locked = true, uuid = "3dfb5697-0fcb-388c-91cd-eaba70b27f8e")
    public DrugStockBatchBaseVo convertToDrugStockBatchBaseVo(DrugStockBatchBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugStockBatchBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装DrugStockBatchBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "523fde62-2c63-34fa-ab8a-e8cdd46841e2")
    public DrugStockBatchBaseVo convertAndAssembleData(DrugStockBatchBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装DrugStockBatchBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "aee64957-7dbb-3106-8d46-7f1f2e9dbc70")
    public List<DrugStockBatchBaseVo> convertAndAssembleDataList(
            List<DrugStockBatchBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, DrugStockBatchBaseVo> voMap =
                convertToDrugStockBatchBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        drugStockBatchBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
