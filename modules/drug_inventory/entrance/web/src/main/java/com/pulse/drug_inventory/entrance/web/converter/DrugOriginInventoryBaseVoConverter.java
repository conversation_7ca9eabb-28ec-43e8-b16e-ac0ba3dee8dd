package com.pulse.drug_inventory.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_inventory.entrance.web.query.assembler.DrugOriginInventoryBaseVoDataAssembler;
import com.pulse.drug_inventory.entrance.web.vo.DrugOriginInventoryBaseVo;
import com.pulse.drug_inventory.manager.dto.DrugOriginInventoryBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到DrugOriginInventoryBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "4f0af627-5f5d-4f0e-a914-aec595ae4ade|VO|CONVERTER")
public class DrugOriginInventoryBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginInventoryBaseVoDataAssembler drugOriginInventoryBaseVoDataAssembler;

    /** 把DrugOriginInventoryBaseDto转换成DrugOriginInventoryBaseVo */
    @AutoGenerated(locked = false, uuid = "4f0af627-5f5d-4f0e-a914-aec595ae4ade-converter-Map")
    public Map<DrugOriginInventoryBaseDto, DrugOriginInventoryBaseVo>
            convertToDrugOriginInventoryBaseVoMap(List<DrugOriginInventoryBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<DrugOriginInventoryBaseDto, DrugOriginInventoryBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugOriginInventoryBaseVo vo =
                                                    new DrugOriginInventoryBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setStorageCode(dto.getStorageCode());
                                            vo.setDrugOriginCode(dto.getDrugOriginCode());
                                            vo.setAmount(dto.getAmount());
                                            vo.setPreOccupiedAmount(dto.getPreOccupiedAmount());
                                            vo.setInTransitAmount(dto.getInTransitAmount());
                                            vo.setVirtualAmount(dto.getVirtualAmount());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setUseFrequency(dto.getUseFrequency());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugOriginInventoryBaseDto转换成DrugOriginInventoryBaseVo */
    @AutoGenerated(locked = true, uuid = "4f0af627-5f5d-4f0e-a914-aec595ae4ade-converter-list")
    public List<DrugOriginInventoryBaseVo> convertToDrugOriginInventoryBaseVoList(
            List<DrugOriginInventoryBaseDto> dtoList) {
        return new ArrayList<>(convertToDrugOriginInventoryBaseVoMap(dtoList).values());
    }

    /** 使用默认方式组装DrugOriginInventoryBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "541ef27c-6cf4-3355-a1d0-44ee1cfcbc09")
    public List<DrugOriginInventoryBaseVo> convertAndAssembleDataList(
            List<DrugOriginInventoryBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, DrugOriginInventoryBaseVo> voMap =
                convertToDrugOriginInventoryBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        drugOriginInventoryBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 使用默认方式组装DrugOriginInventoryBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "5f19d54d-c9bb-3c75-bd91-352ae5cd100b")
    public DrugOriginInventoryBaseVo convertAndAssembleData(DrugOriginInventoryBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把DrugOriginInventoryBaseDto转换成DrugOriginInventoryBaseVo */
    @AutoGenerated(locked = true, uuid = "dbe78f4a-3d89-3fcb-b013-86cfd19b34b1")
    public DrugOriginInventoryBaseVo convertToDrugOriginInventoryBaseVo(
            DrugOriginInventoryBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugOriginInventoryBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }
}
