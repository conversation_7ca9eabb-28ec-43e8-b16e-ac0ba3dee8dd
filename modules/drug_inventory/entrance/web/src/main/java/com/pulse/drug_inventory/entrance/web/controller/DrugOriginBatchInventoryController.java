package com.pulse.drug_inventory.entrance.web.controller;

import com.pulse.drug_dictionary.common.enums.SpecificationTypeEnum;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationBaseDto;
import com.pulse.drug_financial.common.enums.PriceTypeEnum;
import com.pulse.drug_financial.manager.dto.DrugPriceContrastBaseDto;
import com.pulse.drug_inventory.entrance.web.converter.DrugOriginBatchInventoryForPriceSelectVoConverter;
import com.pulse.drug_inventory.entrance.web.query.executor.BatchInventoryWithOriginSpecificationVoQueryExecutor;
import com.pulse.drug_inventory.entrance.web.vo.BatchInventoryWithOriginSpecificationVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugOriginBatchInventoryForPriceSelectVo;
import com.pulse.drug_inventory.manager.dto.DrugOriginBatchInventoryWithSpecificationDto;
import com.pulse.drug_inventory.manager.facade.drug_dictionary.DrugOriginSpecificationBaseDtoServiceInDrugInventoryRpcAdapter;
import com.pulse.drug_inventory.manager.facade.drug_financial.base.DrugPriceContrastBaseDtoServiceInDrugInventoryBaseRpcAdapter;
import com.pulse.drug_inventory.manager.facade.parameter.ParameterConfigWithValueDtoServiceInDrugInventoryRpcAdapter;
import com.pulse.drug_inventory.manager.facade.pharmacy_warehouse_setting.ExportImportWayBaseDtoServiceInDrugInventoryRpcAdapter;
import com.pulse.drug_inventory.persist.eo.TargetSpecificationInventroyEo;
import com.pulse.drug_inventory.persist.qto.SearchBatchInventoryWithOriginSpecificationForGorupQto;
import com.pulse.drug_inventory.persist.qto.SearchDrugOriginBatchInventoryForPriceSelectQto;
import com.pulse.drug_inventory.service.DrugOriginBatchInventoryWithSpecificationDtoService;
import com.pulse.drug_inventory.service.query.DrugOriginBatchInventoryWithSpecificationDtoQueryService;
import com.pulse.parameter.manager.dto.ParameterConfigWithValueDto;
import com.pulse.pharmacy_warehouse_setting.common.enums.InventoryIncreaseReduceEnum;
import com.pulse.pharmacy_warehouse_setting.common.enums.ManageModelEnum;
import com.pulse.pharmacy_warehouse_setting.common.enums.SystemExportImportWayEnum;
import com.pulse.pharmacy_warehouse_setting.manager.dto.ExportImportWayBaseDto;
import com.pulse.pulse.common.constants.DrugConstants;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.math.BigDecimal;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "87423c9c-7f59-37c0-89d3-20b86d5c9a46")
public class DrugOriginBatchInventoryController {
    @AutoGenerated(locked = true)
    @Resource
    private BatchInventoryWithOriginSpecificationVoQueryExecutor
            batchInventoryWithOriginSpecificationVoQueryExecutor;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginBatchInventoryForPriceSelectVoConverter
            drugOriginBatchInventoryForPriceSelectVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginBatchInventoryWithSpecificationDtoQueryService
            drugOriginBatchInventoryWithSpecificationDtoQueryService;

    @Resource
    DrugPriceContrastBaseDtoServiceInDrugInventoryBaseRpcAdapter
            drugPriceContrastBaseDtoServiceInDrugInventoryBaseRpcAdapter;

    @Resource
    ParameterConfigWithValueDtoServiceInDrugInventoryRpcAdapter
            parameterConfigWithValueDtoServiceInDrugInventoryRpcAdapter;

    @Resource
    DrugOriginSpecificationBaseDtoServiceInDrugInventoryRpcAdapter
            drugOriginSpecificationBaseDtoServiceInDrugInventoryRpcAdapter;

    @Resource
    ExportImportWayBaseDtoServiceInDrugInventoryRpcAdapter
            exportImportWayBaseDtoServiceInDrugInventoryRpcAdapter;

    @Resource
    DrugOriginBatchInventoryWithSpecificationDtoService
            drugOriginBatchInventoryWithSpecificationDtoService;

    /** 目标规格库存信息列表 */
    @PublicInterface(id = "16079936-63c1-42dc-8f89-0082dcd47afa", version = "1748573443021")
    @AutoGenerated(locked = false, uuid = "16079936-63c1-42dc-8f89-0082dcd47afa")
    @RequestMapping(
            value = {"/api/drug-inventory/list-target-specification-inventory"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<TargetSpecificationInventroyEo> listTargetSpecificationInventory(
            @Valid List<TargetSpecificationInventroyEo> targetSpecificationInvetoryEoList,
            String storageCode) {
        return drugOriginBatchInventoryWithSpecificationDtoService.listTargetSpecificationInventory(
                targetSpecificationInvetoryEoList, storageCode);
    }

    /** 历次进价-批次：用于出入库选择 - 根据效期、生成批号、进价、零售价分组 */
    @PublicInterface(id = "c7ecc4de-f91d-48a2-a90d-a4b5f91c6a85", version = "1746510919666")
    @AutoGenerated(locked = false, uuid = "c7ecc4de-f91d-48a2-a90d-a4b5f91c6a85")
    @RequestMapping(
            value = {
                "/api/drug-inventory/search-batch-inventory-with-origin-specification-for-gorup-paged"
            },
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<BatchInventoryWithOriginSpecificationVo>
            searchBatchInventoryWithOriginSpecificationForGorupPaged(
                    @Valid @NotNull(message = "查询参数不能为空")
                            SearchBatchInventoryWithOriginSpecificationForGorupQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<BatchInventoryWithOriginSpecificationVo> result =
                batchInventoryWithOriginSpecificationVoQueryExecutor
                        .searchBatchInventoryWithOriginSpecificationForGorupPaged(qto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // access response
        // TODO 接口待删除
        // 取出result.getResult()的药品产地规格id,并去重，若数量>0需抛出异常
        var drugOriginSpecification =
                result.getResult().stream()
                        .filter(vo -> vo.getDrugOriginSpecification() != null)
                        .map(vo -> vo.getDrugOriginSpecification())
                        .distinct()
                        .collect(Collectors.toList());
        if (drugOriginSpecification.size() != 1) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "该药房库存批次中药品产地规格不统一，请检查！");
        }
        // 分组
        result.setResult(
                result.getResult().stream()
                        .collect(
                                Collectors.groupingBy(
                                        vo ->
                                                vo.getDrugOriginSpecification()
                                                        + "_"
                                                        + vo.getBatchNumber()
                                                        + "_"
                                                        + vo.getExpirationDate()
                                                        + "_"
                                                        + vo.getPurchasePrice()
                                                        + "_"
                                                        + vo.getRetailPrice(),
                                        Collectors.collectingAndThen(
                                                Collectors.toList(),
                                                list -> {
                                                    var maxDateVo =
                                                            list.stream()
                                                                    .max(
                                                                            (a, b) ->
                                                                                    a.getImportDateTime()
                                                                                            .compareTo(
                                                                                                    b
                                                                                                            .getImportDateTime()))
                                                                    .get();
                                                    maxDateVo.setAmount(
                                                            list.stream()
                                                                    .map(
                                                                            BatchInventoryWithOriginSpecificationVo
                                                                                    ::getAmount)
                                                                    .reduce(
                                                                            BigDecimal.ZERO,
                                                                            BigDecimal::add));
                                                    return maxDateVo;
                                                })))
                        .values()
                        .stream()
                        .collect(Collectors.toList()));
        //
        var originSpecification = drugOriginSpecification.get(0);
        if (qto.getDrugOriginSpecificationId().equals(originSpecification.getId())) {
            // 界面传入的规格id与返回的规格id一致，不用进行价格转换，直接返回结果
            return result;
        } else {
            if (SpecificationTypeEnum.PACKAGE_SPECIFICATION.equals(
                    originSpecification.getSpecificationType())) {
                // 界面传入为小规格，需要将result.getResult()中大规格数据转为小规格数据
                // 循环result.getResult()，找出drugPriceContrastList中大规格进价与其进价相同且价格类型为进价的的数据，并对对象的进价修改为小规格进价；
                // 找出drugPriceContrastList中大规格零售价与其零售价相同且价格类型为零售价的的数据，并对对象的零售价修改为小规格零售价；
                var drugPriceContrastList =
                        drugPriceContrastBaseDtoServiceInDrugInventoryBaseRpcAdapter
                                .getByPackageOriginSpecificationId(originSpecification.getId());
                for (var vo : result.getResult()) {
                    // 处理进价
                    var purchasePriceContrast =
                            drugPriceContrastList.stream()
                                    .filter(
                                            contrast ->
                                                    contrast.getPriceType()
                                                                    .equals(
                                                                            PriceTypeEnum
                                                                                    .PURCHASE_PRICE)
                                                            && contrast.getPackageSpecificationPrice()
                                                                    .equals(vo.getPurchasePrice()))
                                    .findFirst()
                                    .orElse(null);
                    if (purchasePriceContrast != null) {
                        vo.setPurchasePrice(purchasePriceContrast.getMinOriginSpecificationPrice());
                    }

                    // 处理零售价
                    var retailPriceContrast =
                            drugPriceContrastList.stream()
                                    .filter(
                                            contrast ->
                                                    contrast.getPriceType()
                                                                    .equals(
                                                                            PriceTypeEnum
                                                                                    .RETAIL_PRICE)
                                                            && contrast.getPackageSpecificationPrice()
                                                                    .equals(vo.getRetailPrice()))
                                    .findFirst()
                                    .orElse(null);
                    if (retailPriceContrast != null) {
                        vo.setRetailPrice(retailPriceContrast.getMinOriginSpecificationPrice());
                    }
                }
            } else if (SpecificationTypeEnum.MIN_SPECIFICATION.equals(
                    originSpecification.getSpecificationType())) {
                // 界面传入为大规格，需要将result.getResult()中小规格数据转为大规格数据
                var drugPriceContrastList =
                        drugPriceContrastBaseDtoServiceInDrugInventoryBaseRpcAdapter
                                .getByMinSpecificationId(originSpecification.getId());
                for (var vo : result.getResult()) {
                    // 处理进价
                    var purchasePriceContrast =
                            drugPriceContrastList.stream()
                                    .filter(
                                            contrast ->
                                                    contrast.getPriceType()
                                                                    .equals(
                                                                            PriceTypeEnum
                                                                                    .PURCHASE_PRICE)
                                                            && contrast.getMinOriginSpecificationPrice()
                                                                    .equals(vo.getPurchasePrice()))
                                    .findFirst()
                                    .orElse(null);
                    if (purchasePriceContrast != null) {
                        vo.setPurchasePrice(purchasePriceContrast.getPackageSpecificationPrice());
                    }

                    // 处理零售价
                    var retailPriceContrast =
                            drugPriceContrastList.stream()
                                    .filter(
                                            contrast ->
                                                    contrast.getPriceType()
                                                                    .equals(
                                                                            PriceTypeEnum
                                                                                    .RETAIL_PRICE)
                                                            && contrast.getMinOriginSpecificationPrice()
                                                                    .equals(vo.getRetailPrice()))
                                    .findFirst()
                                    .orElse(null);
                    if (retailPriceContrast != null) {
                        vo.setRetailPrice(retailPriceContrast.getPackageSpecificationPrice());
                    }
                }
            } else {
                throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "该药房库存批次中药品产地规格存储错误，请检查！");
            }
        }

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 历次价格选择列表 */
    @PublicInterface(id = "d2e765f6-19d1-4224-8915-a93623852015", version = "1748423501148")
    @AutoGenerated(locked = false, uuid = "d2e765f6-19d1-4224-8915-a93623852015")
    @RequestMapping(
            value = {"/api/drug-inventory/search-drug-origin-batch-inventory-for-price-select"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<DrugOriginBatchInventoryForPriceSelectVo>
            searchDrugOriginBatchInventoryForPriceSelect(
                    @Valid SearchDrugOriginBatchInventoryForPriceSelectQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugOriginBatchInventoryWithSpecificationDto> rpcResult =
                drugOriginBatchInventoryWithSpecificationDtoQueryService
                        .searchDrugOriginBatchInventoryForPriceSelect(qto);
        List<DrugOriginBatchInventoryForPriceSelectVo> result =
                drugOriginBatchInventoryForPriceSelectVoConverter.convertAndAssembleDataList(
                        rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        if (result == null || result.size() == 0) {
            return result;
        }

        // region 取管理模式
        // 药品管理模式
        var drugManageModel = qto.getDrugManageModel();
        // TODO 取管理模式参数
        var inventoryManaegeMode = ManageModelEnum.TOTAL_MANAGE;
        ParameterConfigWithValueDto parameter =
                parameterConfigWithValueDtoServiceInDrugInventoryRpcAdapter.getByCode(
                        "drug_inventory_manage_mode");
        if (parameter != null && parameter.getDefaultValue() != null) {
            inventoryManaegeMode = ManageModelEnum.valueOf(parameter.getDefaultValue());
        }
        var manageModel = ManageModelEnum.TOTAL_MANAGE;
        if (ManageModelEnum.BATCH_NUMBER_MANAGE.equals(inventoryManaegeMode)
                || ManageModelEnum.BATCH_NUMBER_MANAGE.equals(drugManageModel)) {
            manageModel = ManageModelEnum.BATCH_NUMBER_MANAGE;
        }
        // endregion 取管理模式

        // region 获取分组模式
        // 分组模式：1.价格分组，2.价格、批号、效期分组
        int groupByModel = 1;

        if (qto.getSystemExportImportWay() != null) {
            if (SystemExportImportWayEnum.PRICE_ADJUST.equals(qto.getSystemExportImportWay())
                    || SystemExportImportWayEnum.STOCKTAKING.equals(
                            qto.getSystemExportImportWay())) {
                if (ManageModelEnum.TOTAL_MANAGE.equals(manageModel)) {
                    groupByModel = 1;
                } else {
                    groupByModel = 2;
                }
            }
        } else if (qto.getExportImportWayId() != null) {
            ExportImportWayBaseDto exportImportWayBaseDto =
                    exportImportWayBaseDtoServiceInDrugInventoryRpcAdapter.getById(
                            qto.getExportImportWayId());
            if (exportImportWayBaseDto != null) {
                InventoryIncreaseReduceEnum inventoryIncreaseReduceType =
                        InventoryIncreaseReduceEnum.INCREASE;
                if (InventoryIncreaseReduceEnum.REDUCE.equals(
                        exportImportWayBaseDto.getInventoryIncreaseReduceType())) {
                    if (Boolean.TRUE.equals(qto.getRefFlag())) {
                        inventoryIncreaseReduceType = InventoryIncreaseReduceEnum.INCREASE;
                    } else {
                        inventoryIncreaseReduceType = InventoryIncreaseReduceEnum.REDUCE;
                    }
                } else if (InventoryIncreaseReduceEnum.INCREASE.equals(
                        exportImportWayBaseDto.getInventoryIncreaseReduceType())) {
                    if (Boolean.TRUE.equals(qto.getRefFlag())) {
                        inventoryIncreaseReduceType = InventoryIncreaseReduceEnum.REDUCE;
                    } else {
                        inventoryIncreaseReduceType = InventoryIncreaseReduceEnum.INCREASE;
                    }
                }
                if (InventoryIncreaseReduceEnum.INCREASE.equals(inventoryIncreaseReduceType)) {
                    groupByModel = 1;
                } else if (InventoryIncreaseReduceEnum.REDUCE.equals(inventoryIncreaseReduceType)) {
                    if (ManageModelEnum.TOTAL_MANAGE.equals(manageModel)) {
                        groupByModel = 1;
                    } else {
                        groupByModel = 2;
                    }
                }
            }
        }
        // endregion 获取分组模式

        // region 根据分组模式分组
        var targetSpecificationId = qto.getTargetDrugOriginSpecificatonId();
        DrugOriginSpecificationBaseDto targetSpec =
                drugOriginSpecificationBaseDtoServiceInDrugInventoryRpcAdapter.getById(
                        targetSpecificationId);
        if (targetSpec == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "未找到目标药品产地规格！");
        }
        if (groupByModel == 1) {
            // 按药品产地规格、进价、零售价分组
            result =
                    result.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            vo ->
                                                    new Object[] {
                                                        vo.getDrugOriginSpecification(),
                                                        vo.getPurchasePrice(),
                                                        vo.getRetailPrice()
                                                    },
                                            Collectors.collectingAndThen(
                                                    Collectors.toList(),
                                                    list -> {
                                                        var mergedVo =
                                                                new DrugOriginBatchInventoryForPriceSelectVo();
                                                        mergedVo.setPurchasePrice(
                                                                list.get(0).getPurchasePrice());
                                                        mergedVo.setRetailPrice(
                                                                list.get(0).getRetailPrice());
                                                        mergedVo.setDrugOriginSpecification(
                                                                list.get(0)
                                                                        .getDrugOriginSpecification());
                                                        mergedVo.setTargetManageModel(
                                                                ManageModelEnum.TOTAL_MANAGE);
                                                        return getDrugOriginBatchInventoryForPriceSelectVo(
                                                                list, mergedVo);
                                                    })))
                            .values()
                            .stream()
                            .collect(Collectors.toList());

        } else {
            // 按药品产地规格、进价、零售价、批号、效期分组
            result =
                    result.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            vo ->
                                                    new Object[] {
                                                        vo.getDrugOriginSpecification(),
                                                        vo.getPurchasePrice(),
                                                        vo.getRetailPrice(),
                                                        vo.getBatchNumber(),
                                                        vo.getExpirationDate()
                                                    },
                                            Collectors.collectingAndThen(
                                                    Collectors.toList(),
                                                    list -> {
                                                        var mergedVo =
                                                                new DrugOriginBatchInventoryForPriceSelectVo();
                                                        mergedVo.setPurchasePrice(
                                                                list.get(0).getPurchasePrice());
                                                        mergedVo.setRetailPrice(
                                                                list.get(0).getRetailPrice());
                                                        mergedVo.setBatchNumber(
                                                                list.get(0).getBatchNumber());
                                                        mergedVo.setExpirationDate(
                                                                list.get(0).getExpirationDate());
                                                        mergedVo.setDrugOriginSpecification(
                                                                list.get(0)
                                                                        .getDrugOriginSpecification());
                                                        mergedVo.setTargetManageModel(
                                                                ManageModelEnum
                                                                        .BATCH_NUMBER_MANAGE);
                                                        return getDrugOriginBatchInventoryForPriceSelectVo(
                                                                list, mergedVo);
                                                    })))
                            .values()
                            .stream()
                            .collect(Collectors.toList());
        }
        // endregion 根据分组模式分组

        // region 处理目标规格信息
        for (var vo : result) {
            vo.setTargetDrugOriginSpecificationId(targetSpec.getId());
            // 处理目标规格库存数量
            vo.setTargetSpecAmount(
                    vo.getAmount()
                            .multiply(
                                    BigDecimal.valueOf(
                                            vo.getDrugOriginSpecification().getAmountPerPackage()))
                            .divide(
                                    BigDecimal.valueOf(targetSpec.getAmountPerPackage()),
                                    DrugConstants.drugAmountAccuracy,
                                    BigDecimal.ROUND_HALF_UP));
            // 处理目标规格虚拟库存数量
            vo.setTargetSpecVirtualAmount(
                    vo.getVirtualAmount()
                            .multiply(
                                    BigDecimal.valueOf(
                                            vo.getDrugOriginSpecification().getAmountPerPackage()))
                            .divide(
                                    BigDecimal.valueOf(targetSpec.getAmountPerPackage()),
                                    DrugConstants.drugAmountAccuracy,
                                    BigDecimal.ROUND_HALF_UP));
        }

        var currentSpec =
                result.stream()
                        .findFirst()
                        .map(DrugOriginBatchInventoryForPriceSelectVo::getDrugOriginSpecification)
                        .orElse(null);
        if (currentSpec == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "未找到库存对应的药品产地规格！");
        }
        if (targetSpecificationId.equals(currentSpec.getId())) {
            // 目标规格与库存规格一致，不用进行价格转换，直接返回结果
            for (var vo : result) {
                // 处理目标进价、零售价
                vo.setTargetSpecPurchasePrice(vo.getPurchasePrice());
                vo.setTargetSpecRetailPrice(vo.getRetailPrice());
            }
            return result;
        } else {
            // 根据当前规格类型获取对应的价格对比数据
            List<DrugPriceContrastBaseDto> drugPriceContrastList;
            Function<DrugPriceContrastBaseDto, BigDecimal> specPriceGetter;

            if (SpecificationTypeEnum.PACKAGE_SPECIFICATION.equals(
                    currentSpec.getSpecificationType())) {
                drugPriceContrastList =
                        drugPriceContrastBaseDtoServiceInDrugInventoryBaseRpcAdapter
                                .getByPackageOriginSpecificationId(currentSpec.getId());
                specPriceGetter = DrugPriceContrastBaseDto::getPackageSpecificationPrice;
            } else if (SpecificationTypeEnum.MIN_SPECIFICATION.equals(
                    currentSpec.getSpecificationType())) {
                drugPriceContrastList =
                        drugPriceContrastBaseDtoServiceInDrugInventoryBaseRpcAdapter
                                .getByMinSpecificationId(currentSpec.getId());
                specPriceGetter = DrugPriceContrastBaseDto::getMinOriginSpecificationPrice;
            } else {
                throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "该药房库存批次中药品产地规格存储错误，请检查！");
            }

            // 批量处理每个 vo 的价格转换
            for (var vo : result) {
                setConvertedPricesWithFallback(
                        vo,
                        drugPriceContrastList,
                        specPriceGetter,
                        targetSpec,
                        currentSpec.getAmountPerPackage());
            }
        }
        // endregion 处理目标规格信息

        return result;
    }

    /**
     * 组装数据
     *
     * @param list
     * @param mergedVo
     * @return
     */
    private DrugOriginBatchInventoryForPriceSelectVo getDrugOriginBatchInventoryForPriceSelectVo(
            List<DrugOriginBatchInventoryForPriceSelectVo> list,
            DrugOriginBatchInventoryForPriceSelectVo mergedVo) {

        mergedVo.setAmount(
                list.stream()
                        .map(DrugOriginBatchInventoryForPriceSelectVo::getAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
        mergedVo.setVirtualAmount(
                list.stream()
                        .map(DrugOriginBatchInventoryForPriceSelectVo::getVirtualAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));

        return mergedVo;
    }

    /**
     * 转换价格
     *
     * @param drugPriceContrastList
     * @param originalPrice
     * @param priceType
     * @param specPriceGetter
     * @param targetSpec
     * @return
     */
    private BigDecimal convertPriceWithFallback(
            List<DrugPriceContrastBaseDto> drugPriceContrastList,
            BigDecimal originalPrice,
            PriceTypeEnum priceType,
            Function<DrugPriceContrastBaseDto, BigDecimal> specPriceGetter,
            DrugOriginSpecificationBaseDto targetSpec,
            Long currentAmountPerPackage) {

        return drugPriceContrastList.stream()
                .filter(
                        contrast ->
                                contrast.getPriceType() == priceType
                                        && specPriceGetter.apply(contrast).equals(originalPrice))
                .findFirst()
                .map(DrugPriceContrastBaseDto::getMinOriginSpecificationPrice)
                .orElseGet(
                        () ->
                                originalPrice
                                        .multiply(BigDecimal.valueOf(currentAmountPerPackage))
                                        .divide(
                                                BigDecimal.valueOf(
                                                        targetSpec.getAmountPerPackage()),
                                                DrugConstants.drugAmountAccuracy,
                                                BigDecimal.ROUND_HALF_UP));
    }

    /**
     * 设置转换后的价格
     *
     * @param vo
     * @param drugPriceContrastList
     * @param specPriceGetter
     * @param targetSpec
     */
    private void setConvertedPricesWithFallback(
            DrugOriginBatchInventoryForPriceSelectVo vo,
            List<DrugPriceContrastBaseDto> drugPriceContrastList,
            Function<DrugPriceContrastBaseDto, BigDecimal> specPriceGetter,
            DrugOriginSpecificationBaseDto targetSpec,
            Long currentAmountPerPackage) {

        vo.setTargetSpecPurchasePrice(
                convertPriceWithFallback(
                        drugPriceContrastList,
                        vo.getPurchasePrice(),
                        PriceTypeEnum.PURCHASE_PRICE,
                        specPriceGetter,
                        targetSpec,
                        currentAmountPerPackage));

        vo.setTargetSpecRetailPrice(
                convertPriceWithFallback(
                        drugPriceContrastList,
                        vo.getRetailPrice(),
                        PriceTypeEnum.RETAIL_PRICE,
                        specPriceGetter,
                        targetSpec,
                        currentAmountPerPackage));
    }
}
