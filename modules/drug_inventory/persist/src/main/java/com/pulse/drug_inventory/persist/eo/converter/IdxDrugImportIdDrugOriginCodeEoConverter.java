package com.pulse.drug_inventory.persist.eo.converter;

import cn.hutool.core.util.StrUtil;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.drug_inventory.persist.eo.IdxDrugImportIdDrugOriginCodeEo;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.utils.JsonUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/** converter for IdxDrugImportIdDrugOriginCodeEo */
@Converter
@AutoGenerated(locked = true, uuid = "8cfb25d2-ee93-3f6d-a870-138ad29f67b6")
public class IdxDrugImportIdDrugOriginCodeEoConverter
        implements AttributeConverter<IdxDrugImportIdDrugOriginCodeEo, String> {

    /** convert DB column to IdxDrugImportIdDrugOriginCodeEo */
    @AutoGenerated(locked = true)
    public String convertToDatabaseColumn(
            IdxDrugImportIdDrugOriginCodeEo idxDrugImportIdDrugOriginCodeEo) {
        if (idxDrugImportIdDrugOriginCodeEo == null) {
            return new String();
        } else {
            return JsonUtils.toJson(idxDrugImportIdDrugOriginCodeEo);
        }
    }

    /** convert DB column to IdxDrugImportIdDrugOriginCodeEo */
    @AutoGenerated(locked = true)
    public IdxDrugImportIdDrugOriginCodeEo convertToEntityAttribute(
            String idxDrugImportIdDrugOriginCodeEoJson) {
        if (StrUtil.isEmpty(idxDrugImportIdDrugOriginCodeEoJson)) {
            return null;
        } else {
            return JsonUtils.readObject(
                    idxDrugImportIdDrugOriginCodeEoJson,
                    new TypeReference<IdxDrugImportIdDrugOriginCodeEo>() {});
        }
    }
}
