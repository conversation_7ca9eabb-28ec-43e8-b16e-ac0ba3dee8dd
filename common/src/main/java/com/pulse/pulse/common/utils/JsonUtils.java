package com.pulse.pulse.common.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.MissingNode;
import com.vs.ox.common.utils.ObjectMapperFactory;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

/**
 * JSON工具类，提供JSON序列化和反序列化功能
 *
 * <p>主要功能：
 *
 * <ul>
 *   <li>基本的JSON序列化和反序列化
 *   <li>处理循环引用问题，避免StackOverflowError
 *   <li>支持友好的循环引用格式，使JSON更易读
 *   <li>支持路径查询，从JSON中提取特定路径的值
 * </ul>
 *
 * <p>使用说明：
 *
 * <ul>
 *   <li>默认的toJson方法不处理循环引用，如果对象中存在循环引用，将导致StackOverflowError
 *   <li>如果需要处理循环引用，请使用toJsonSafe方法
 *   <li>循环引用处理会在JSON中添加特殊标记，使得循环引用的对象可以被正确识别
 * </ul>
 */
public class JsonUtils {

    private static final ObjectMapper objectMapper = ObjectMapperFactory.getDefaultObjectMapper();

    static {
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
    }

    /**
     * 获取默认的ObjectMapper
     *
     * @return 默认的ObjectMapper实例
     */
    public static ObjectMapper getObjectMapper() {
        return objectMapper;
    }

    /**
     * 将对象转换为JSON字符串（不处理循环引用）
     *
     * <p>警告：如果对象中存在循环引用，此方法将导致StackOverflowError
     *
     * <p>如果不确定对象是否包含循环引用，请使用{@link #toJsonSafe(Object)}方法
     *
     * @param value 要转换的对象
     * @return JSON字符串
     * @throws IllegalStateException 如果JSON转换失败
     */
    public static String toJson(Object value) {
        try {
            return objectMapper.writeValueAsString(value);
        } catch (JsonProcessingException e) {
            throw new IllegalStateException("JSON序列化失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将对象转换为JSON字符串，安全处理循环引用问题
     *
     * <p>该方法使用专门配置的CircularReferenceHandler来处理循环引用，避免出现StackOverflowError
     *
     * <p>当遇到循环引用时，使用对象的唯一标识替代完整对象，并修改属性名，添加"_ref"后缀
     *
     * @param value 要转换的对象
     * @return JSON字符串
     */
    public static String toJsonSafe(Object value) {
        return CircularReferenceHandler.toJson(value);
    }

    /**
     * 将JSON字符串转换为对象
     *
     * @param json JSON字符串
     * @param clazz 目标类型
     * @param <T> 目标类型
     * @return 转换后的对象
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        try {
            return objectMapper.readValue(json, clazz);
        } catch (IOException e) {
            throw new IllegalStateException("from Json error", e);
        }
    }

    /**
     * 将JSON字符串转换为对象
     *
     * @param json JSON字符串
     * @param typeReference 目标类型引用
     * @param <T> 目标类型
     * @return 转换后的对象
     */
    public static <T> T fromJson(String json, TypeReference<T> typeReference) {
        try {
            return objectMapper.readValue(json, typeReference);
        } catch (IOException e) {
            throw new IllegalStateException("from Json error", e);
        }
    }

    /**
     * 读取JSON字符串为指定类型的对象
     *
     * @param json JSON字符串
     * @param typeReference 类型引用
     * @param <T> 目标类型
     * @return 转换后的对象
     */
    public static <T> T readObject(String json, TypeReference<T> typeReference) {
        return readObject(json, objectMapper.getTypeFactory().constructType(typeReference));
    }

    /**
     * 读取JSON字符串为指定类型的对象
     *
     * @param json JSON字符串
     * @param clazz 目标类型
     * @param <T> 目标类型
     * @return 转换后的对象
     */
    public static <T> T readObject(String json, Class<T> clazz) {
        return readObject(json, objectMapper.getTypeFactory().constructType(clazz));
    }

    /**
     * 读取JSON字节数组为指定类型的对象
     *
     * @param json JSON字节数组
     * @param clazz 目标类型
     * @param <T> 目标类型
     * @return 转换后的对象
     */
    public static <T> T readObject(byte[] json, Class<T> clazz) {
        try {
            return objectMapper.readValue(json, clazz);
        } catch (IOException e) {
            throw new IllegalStateException("read Json error", e);
        }
    }

    /**
     * 读取JSON字符串为指定JavaType的对象
     *
     * @param json JSON字符串
     * @param javaType JavaType
     * @param <T> 目标类型
     * @return 转换后的对象
     */
    public static <T> T readObject(String json, JavaType javaType) {
        if (isBlank(json)) {
            return null;
        }
        try {
            return objectMapper.readValue(json, javaType);
        } catch (IOException e) {
            throw new IllegalStateException("read Json error", e);
        }
    }

    /**
     * 获取JSON字符串中指定路径的JsonNode
     *
     * @param json JSON字符串
     * @param path 路径
     * @return JsonNode
     */
    public static JsonNode path(String json, String path) {
        if (isBlank(json)) {
            return MissingNode.getInstance();
        }
        try {
            String atPath = path;
            if (!path.startsWith("/")) {
                atPath = "/" + path;
            }
            return objectMapper.readTree(json).at(atPath);
        } catch (IOException e) {
            throw new IllegalStateException("read Json error", e);
        }
    }

    /**
     * 判断字符串是否为空白
     *
     * @param str 字符串
     * @return 是否为空白
     */
    private static boolean isBlank(String str) {
        int strLen;
        if (str == null || (strLen = str.length()) == 0) {
            return true;
        }
        for (int i = 0; i < strLen; i++) {
            if ((!Character.isWhitespace(str.charAt(i)))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 读取JSON字符串中指定路径的值为指定类型的对象
     *
     * @param json JSON字符串
     * @param path 路径
     * @param typeReference 类型引用
     * @param <T> 目标类型
     * @return 可选的转换后的对象
     */
    public static <T> Optional<T> readPath(
            String json, String path, TypeReference<T> typeReference) {
        return readPath(json, path, objectMapper.getTypeFactory().constructType(typeReference));
    }

    /**
     * 读取JSON字符串中指定路径的值为指定类型的对象
     *
     * @param json JSON字符串
     * @param path 路径
     * @param clazz 目标类型
     * @param <T> 目标类型
     * @return 可选的转换后的对象
     */
    public static <T> Optional<T> readPath(String json, String path, Class<T> clazz) {
        JsonNode jsonNode = path(json, path);
        if (jsonNode.isMissingNode()) {
            return Optional.empty();
        }
        try {
            return Optional.of(objectMapper.treeToValue(jsonNode, clazz));
        } catch (JsonProcessingException e) {
            throw new IllegalStateException(e);
        }
    }

    /**
     * 读取JSON字符串中指定路径的值为指定JavaType的对象
     *
     * @param json JSON字符串
     * @param path 路径
     * @param javaType JavaType
     * @param <T> 目标类型
     * @return 可选的转换后的对象
     */
    public static <T> Optional<T> readPath(String json, String path, JavaType javaType) {
        JsonNode jsonNode = path(json, path);
        if (jsonNode.isMissingNode()) {
            return Optional.empty();
        }
        try {
            return Optional.of(
                    objectMapper.readValue(objectMapper.treeAsTokens(jsonNode), javaType));
        } catch (IOException e) {
            throw new IllegalStateException(e);
        }
    }

    /**
     * 比较两个JSON字符串，返回变化的顶级字段名列表
     *
     * <p>该方法用于比较两个JSON对象，找出在顶级层面发生变化的字段名。 适用于操作日志、数据审计等场景，可以快速识别哪些主要字段发生了变化。
     *
     * <p>示例：
     *
     * <pre>
     * String before = "{\"name\":\"张三\",\"age\":30,\"address\":{\"city\":\"北京\"}}";
     * String after = "{\"name\":\"张三\",\"age\":31,\"address\":{\"city\":\"上海\"}}";
     * List&lt;String&gt; changes = JsonUtils.getChangedTopLevelFields(before, after);
     * // 返回: ["age", "address"]
     * </pre>
     *
     * @param beforeJson 变化前的JSON字符串
     * @param afterJson 变化后的JSON字符串
     * @return 变化的顶级字段名列表，如果JSON为空或解析失败则返回空列表
     */
    public static List<String> getChangedTopLevelFields(String beforeJson, String afterJson) {
        return JsonCompareUtils.getChangedTopLevelFields(beforeJson, afterJson);
    }

    /**
     * 比较两个JSON字符串，返回变化的所有字段路径列表
     *
     * <p>该方法用于比较两个JSON对象，找出所有发生变化的字段路径，包括嵌套字段。 适用于需要详细追踪所有变化的场景。
     *
     * <p>示例：
     *
     * <pre>
     * String before = "{\"name\":\"张三\",\"age\":30,\"address\":{\"city\":\"北京\",\"street\":\"朝阳区\"}}";
     * String after = "{\"name\":\"张三\",\"age\":31,\"address\":{\"city\":\"上海\",\"street\":\"朝阳区\"}}";
     * List&lt;String&gt; changes = JsonUtils.getChangedFieldPaths(before, after);
     * // 返回: ["age", "address.city"]
     * </pre>
     *
     * @param beforeJson 变化前的JSON字符串
     * @param afterJson 变化后的JSON字符串
     * @return 变化的字段路径列表，如果JSON为空或解析失败则返回空列表
     */
    public static List<String> getChangedFieldPaths(String beforeJson, String afterJson) {
        return JsonCompareUtils.getChangedFieldPaths(beforeJson, afterJson);
    }
}
