package com.pulse.pulse.common.utils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/** zhoujiajian */
public class TreeUtil {

    /**
     * 通用方法：按父子关系分组（支持多级嵌套）
     *
     * @param items 待分组的对象列表
     * @param getIdFn 获取对象ID的函数
     * @param getParentIdFn 获取父ID的函数
     * @return Map<父ID, 所有子ID列表（包括多级嵌套）>
     */
    public static <T> Map<String, List<String>> groupByParent(
            List<T> items, Function<T, String> getIdFn, Function<T, String> getParentIdFn) {

        // 1. 构建父子关系映射表
        Map<String, List<T>> parentChildMap =
                items.stream()
                        .filter(item -> getParentIdFn.apply(item) != null)
                        .collect(Collectors.groupingBy(getParentIdFn));

        // 2. 获取所有根节点（parentId为null的节点）
        List<T> roots =
                items.stream()
                        .filter(item -> getParentIdFn.apply(item) == null)
                        .collect(Collectors.toList());

        // 3. 递归收集所有子节点
        Map<String, List<String>> result = new LinkedHashMap<>();
        for (T root : roots) {
            List<String> allChildren = new ArrayList<>();
            collectChildrenIds(root, getIdFn, parentChildMap, allChildren);
            result.put(getIdFn.apply(root), allChildren);
        }

        // 4. 处理孤立节点（非根节点但未被包含在树中的情况）
        parentChildMap.forEach(
                (parentId, children) -> {
                    if (!result.containsKey(parentId)) {
                        result.put(
                                parentId,
                                children.stream().map(getIdFn).collect(Collectors.toList()));
                    }
                });

        return result;
    }

    private static <T> void buildSubTree(
            TreeNode<T> parent,
            Map<String, List<T>> parentChildMap,
            Map<String, TreeNode<T>> nodeCache,
            Function<T, String> getIdFn) {
        List<T> children = parentChildMap.get(getIdFn.apply(parent.data));
        if (children == null) return;

        for (T childData : children) {
            TreeNode<T> childNode = nodeCache.get(getIdFn.apply(childData));
            parent.addChild(childNode);
            buildSubTree(childNode, parentChildMap, nodeCache, getIdFn); // 递归处理子节点
        }
    }

    //
    /**
     * 构建完整树形结构（修复深层嵌套问题）
     *
     * @param items 原始数据列表
     * @param getIdFn 获取节点ID的函数
     * @param getParentIdFn 获取父节点ID的函数
     * @return 根节点列表（包含完整子节点树）
     */
    public static <T> List<TreeNode<T>> buildTree(
            List<T> items, Function<T, String> getIdFn, Function<T, String> getParentIdFn) {

        // 1. 构建父子关系映射表（预分组优化性能）
        Map<String, List<T>> parentChildMap =
                items.stream()
                        .filter(item -> getParentIdFn.apply(item) != null)
                        .collect(Collectors.groupingBy(getParentIdFn));

        // 2. 创建所有节点的快速查找表（避免递归中反复实例化）
        Map<String, TreeNode<T>> nodeCache = new HashMap<>();
        items.forEach(item -> nodeCache.put(getIdFn.apply(item), new TreeNode<>(item)));

        // 3. 递归构建树形结构
        List<TreeNode<T>> roots = new ArrayList<>();
        items.forEach(
                item -> {
                    String parentId = getParentIdFn.apply(item);
                    TreeNode<T> currentNode = nodeCache.get(getIdFn.apply(item));

                    if (parentId == null) {
                        roots.add(currentNode); // 根节点直接加入结果
                    } else {
                        TreeNode<T> parent = nodeCache.get(parentId);
                        if (parent != null) {
                            parent.addChild(currentNode);
                            // 递归构建子节点的子列表
                            buildSubTree(currentNode, parentChildMap, nodeCache, getIdFn);
                        }
                    }
                });

        return roots;
    }

    private static <T> void collectChildrenIds(
            T node,
            Function<T, String> getIdFn,
            Map<String, List<T>> parentChildMap,
            List<String> collector) {
        List<T> children = parentChildMap.get(getIdFn.apply(node));
        if (children == null) return;

        for (T child : children) {
            collector.add(getIdFn.apply(child));
            collectChildrenIds(child, getIdFn, parentChildMap, collector);
        }
    }

    public static class TreeNode<T> {
        private T data;
        private List<TreeNode<T>> children;

        public TreeNode(T data) {
            this.data = data;
            this.children = new ArrayList<>();
        }

        public T getData() {
            return data;
        }

        public List<TreeNode<T>> getChildren() {
            return children;
        }

        public void addChild(TreeNode<T> child) {
            children.add(child);
        }

        public void addChild(List<TreeNode<T>> child) {
            children.addAll(child);
        }
    }
}
