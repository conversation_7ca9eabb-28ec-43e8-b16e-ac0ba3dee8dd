package com.pulse.pulse.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import javax.annotation.Resource;

import com.pulse.pulse.common.config.MqConfig;
import com.pulse.pulse.common.rocketmq.RocketMQService;
import com.pulse.pulse.common.rocketmq.RocketMQServiceImpl;

@Configuration
public class RocketMqAutoConfiguration {
    @Resource
    private MqConfig mqConfig;
    @Bean
    RocketMQService createRocketMQService() {
        RocketMQServiceImpl instance = new RocketMQServiceImpl(mqConfig.getNameServer());
        return instance;
    }
}