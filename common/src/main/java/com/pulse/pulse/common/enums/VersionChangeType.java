package com.pulse.pulse.common.enums;

/**
 * 版本变更类型枚举
 *
 * <p>定义语义化版本的三种变更类型，遵循语义化版本规范（Semantic Versioning 2.0.0）：
 *
 * <ul>
 *   <li><strong>MAJOR</strong>：主版本号，用于不兼容的API变更
 *   <li><strong>MINOR</strong>：次版本号，用于向下兼容的功能性新增
 *   <li><strong>PATCH</strong>：修订号，用于向下兼容的问题修正
 * </ul>
 *
 * <p>版本号格式：MAJOR.MINOR.PATCH（如：2.1.3）
 *
 * <p>变更规则：
 *
 * <ul>
 *   <li>递增MAJOR版本时，MINOR和PATCH版本重置为0
 *   <li>递增MINOR版本时，PATCH版本重置为0
 *   <li>递增PATCH版本时，仅PATCH版本递增
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 * @see <a href="https://semver.org/">Semantic Versioning 2.0.0</a>
 */
public enum VersionChangeType {

    /**
     * 主版本号（Major）
     *
     * <p>用于重大变更或不兼容更新，如：
     *
     * <ul>
     *   <li>API接口的不兼容变更
     *   <li>数据结构的重大调整
     *   <li>系统架构的重构
     * </ul>
     *
     * <p>示例：1.0.0 → 2.0.0
     */
    MAJOR("major", "主版本号", "用于重大变更或不兼容更新"),

    /**
     * 次版本号（Minor）
     *
     * <p>用于添加新功能或小规模变更，如：
     *
     * <ul>
     *   <li>新增API接口
     *   <li>新增业务功能
     *   <li>性能优化
     * </ul>
     *
     * <p>示例：1.0.0 → 1.1.0
     */
    MINOR("minor", "次版本号", "用于添加新功能或小规模变更"),

    /**
     * 修订号（Patch）
     *
     * <p>用于bug修复或小调整，如：
     *
     * <ul>
     *   <li>修复已知缺陷
     *   <li>代码优化
     *   <li>文档更新
     * </ul>
     *
     * <p>示例：1.0.0 → 1.0.1
     */
    PATCH("patch", "修订号", "用于bug修复或小调整");

    /** 变更类型代码 */
    private final String code;

    /** 变更类型名称 */
    private final String name;

    /** 变更类型描述 */
    private final String description;

    /**
     * 构造函数
     *
     * @param code 变更类型代码
     * @param name 变更类型名称
     * @param description 变更类型描述
     */
    VersionChangeType(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 获取变更类型代码
     *
     * @return 变更类型代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取变更类型名称
     *
     * @return 变更类型名称
     */
    public String getName() {
        return name;
    }

    /**
     * 获取变更类型描述
     *
     * @return 变更类型描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取变更类型
     *
     * <p>支持大小写不敏感的查找，会自动去除前后空格
     *
     * @param code 变更类型代码（如："major", "MINOR", "patch"）
     * @return 变更类型枚举，如果未找到则返回null
     */
    public static VersionChangeType fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }

        String trimmedCode = code.trim();
        for (VersionChangeType type : values()) {
            if (type.code.equalsIgnoreCase(trimmedCode)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据名称获取变更类型
     *
     * <p>精确匹配变更类型名称，会自动去除前后空格
     *
     * @param name 变更类型名称（如："主版本号", "次版本号", "修订号"）
     * @return 变更类型枚举，如果未找到则返回null
     */
    public static VersionChangeType fromName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return null;
        }

        String trimmedName = name.trim();
        for (VersionChangeType type : values()) {
            if (type.name.equals(trimmedName)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断是否为有效的变更类型代码
     *
     * @param code 变更类型代码
     * @return 如果是有效代码则返回true
     */
    public static boolean isValidCode(String code) {
        return fromCode(code) != null;
    }

    /**
     * 判断是否为有效的变更类型名称
     *
     * @param name 变更类型名称
     * @return 如果是有效名称则返回true
     */
    public static boolean isValidName(String name) {
        return fromName(name) != null;
    }

    @Override
    public String toString() {
        return String.format("%s(%s): %s", name, code, description);
    }
}
