package com.pulse.pulse.common.utils;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;

import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.Pipeline;
import redis.clients.jedis.Response;
import redis.clients.jedis.exceptions.JedisConnectionException;
import redis.clients.jedis.exceptions.JedisException;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Base64;
import java.util.Date;
import java.util.List;

public class IdGeneratorUtils {

    private static final String ID_KEY = "ID";

    /**
     * UUID
     *
     * @return
     */
    public static String generateUUID() {
        return java.util.UUID.randomUUID().toString();
    }

    /**
     * 使用redis生成唯一ID
     *
     * @param businessKey
     * @param propertyName
     * @param idLength
     * @return
     */
    public static String generateNextSequenceId(String businessKey, String propertyName, long idLength) {
        long idPrefixLength = DatePattern.PURE_DATETIME_PATTERN.length();
        if (idPrefixLength >= idLength) {
            throw new IgnoredException(
                    ErrorCode.WRONG_PARAMETER, "id长度设置不合理，应该大于" + idPrefixLength);
        }

        long suffixLength = idLength - idPrefixLength;
        String prefix = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        String key = StrUtil.format("{}_{}_{}", ID_KEY, businessKey, propertyName);

        try (Jedis jedis = SpringUtil.getBean(JedisPool.class).getResource()) {
            Pipeline pipeline = jedis.pipelined();
            Response<Long> incrResponse = pipeline.incr(key);
            pipeline.expire(key, 1); // 保存1秒
            pipeline.sync();

            long number = incrResponse.get();
            return prefix + String.format("%0" + suffixLength + "d", number);
        } catch (JedisConnectionException e) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "Redis连接失败");
        } catch (JedisException e) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "Redis操作失败");
        } catch (Exception e) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, e.getMessage());
        }
    }

    /** 支持的哈希算法 */
    public enum HashAlgorithm {
        MD5("MD5"),
        SHA1("SHA-1"),
        SHA256("SHA-256"),
        SHA512("SHA-512");

        private final String algorithmName;

        HashAlgorithm(String algorithmName) {
            this.algorithmName = algorithmName;
        }

        public String getAlgorithmName() {
            return algorithmName;
        }
    }

    /** 输出格式选项 */
    public enum OutputFormat {
        HEX, // 十六进制
        BASE64 // Base64编码
    }

    /**
     * 生成哈希ID
     *
     * @param fields 要哈希的字段列表
     * @param algorithm 哈希算法
     * @param outputFormat 输出格式
     * @param length 输出长度（0表示使用完整长度）
     * @param delimiter 字段间的分隔符
     * @return 生成的哈希ID
     * @throws IllegalArgumentException 参数验证失败时抛出
     */
    public static String generateHashId(
            List<String> fields,
            HashAlgorithm algorithm,
            OutputFormat outputFormat,
            int length,
            String delimiter) {

        // 参数验证
        if (fields == null || fields.isEmpty()) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "要哈希的字段列表不能为空");
        }
        if (algorithm == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "哈希算法不能为空");
        }
        if (delimiter == null) {
            delimiter = "|";
        }

        try {
            // 组合字段
            String rawData = String.join(delimiter, fields);

            // 生成哈希
            MessageDigest digest = MessageDigest.getInstance(algorithm.getAlgorithmName());
            byte[] hashBytes = digest.digest(rawData.getBytes(StandardCharsets.UTF_8));

            // 转换格式
            String result;
            if (outputFormat == OutputFormat.BASE64) {
                result = Base64.getEncoder().encodeToString(hashBytes);
            } else {
                // 默认使用十六进制
                StringBuilder hexString = new StringBuilder();
                for (byte b : hashBytes) {
                    hexString.append(String.format("%02x", b));
                }
                result = hexString.toString();
            }

            // 处理长度
            if (length > 0 && length < result.length()) {
                return result.substring(0, length);
            }
            return result;

        } catch (NoSuchAlgorithmException e) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "该哈希算法不可用");
        } catch (Exception e) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "哈希失败");
        }
    }

    /** 简化版本的哈希ID生成方法 */
    public static String generateHashId(String... fields) {
        return generateHashId(
                Arrays.asList(fields), HashAlgorithm.SHA256, OutputFormat.HEX, 32, "|");
    }

    /**
     * 比对哈希值
     *
     * @param inputString
     * @param storedHash
     * @return
     */
    public static boolean verifyHashField(String inputString, String storedHash) {
        String inputHash = generateHashId(inputString);
        return inputHash.equals(storedHash);
    }

    // 分布式ID生成不适用
    // 雪花算法hutool已包含
}
