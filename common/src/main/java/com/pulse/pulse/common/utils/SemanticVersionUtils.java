package com.pulse.pulse.common.utils;

import com.pulse.pulse.common.enums.VersionChangeType;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 语义化版本管理工具类
 *
 * <p>提供语义化版本的生成、验证、比较等功能，遵循语义化版本规范（Semantic Versioning 2.0.0）。
 *
 * <p>主要功能：
 *
 * <ul>
 *   <li><strong>版本号生成</strong>：创建新版本号、递增版本号
 *   <li><strong>版本号验证</strong>：验证版本号格式、有效性检查
 *   <li><strong>版本号比较</strong>：比较版本大小、兼容性判断
 *   <li><strong>版本号排序</strong>：对版本号列表进行排序
 *   <li><strong>版本号查询</strong>：获取最新版本、最旧版本等
 * </ul>
 *
 * <p>该工具类是线程安全的，所有方法都是静态方法。
 *
 * <p>使用示例：
 *
 * <pre>
 * // 创建版本号
 * SemanticVersion version = SemanticVersionUtils.createVersion(2, 1, 3);
 *
 * // 解析版本号
 * SemanticVersion parsed = SemanticVersionUtils.parseVersion("2.1.3");
 *
 * // 递增版本号
 * SemanticVersion newVersion = SemanticVersionUtils.incrementVersion("2.1.3", VersionChangeType.MINOR);
 *
 * // 验证版本号
 * boolean isValid = SemanticVersionUtils.isValidVersion("2.1.3");
 *
 * // 比较版本号
 * int result = SemanticVersionUtils.compareVersions("2.1.3", "2.1.2");
 *
 * // 版本排序
 * List&lt;String&gt; versions = Arrays.asList("2.1.3", "1.0.0", "2.2.0");
 * List&lt;String&gt; sorted = SemanticVersionUtils.sortVersionsAscending(versions);
 * </pre>
 *
 * <AUTHOR>
 * @since 1.0.0
 * @see <a href="https://semver.org/">Semantic Versioning 2.0.0</a>
 */
public class SemanticVersionUtils {

    /** 语义化版本号正则表达式 格式：MAJOR.MINOR.PATCH，其中每个部分都是非负整数 */
    private static final Pattern VERSION_PATTERN = Pattern.compile("^(\\d+)\\.(\\d+)\\.(\\d+)$");

    /**
     * 私有构造函数，防止实例化
     *
     * @throws UnsupportedOperationException 总是抛出此异常
     */
    private SemanticVersionUtils() {
        throw new UnsupportedOperationException("工具类不能被实例化");
    }

    // ==================== 版本号生成功能 ====================

    /**
     * 创建语义化版本号
     *
     * @param major 主版本号，必须为非负整数
     * @param minor 次版本号，必须为非负整数
     * @param patch 修订号，必须为非负整数
     * @return SemanticVersion对象
     * @throws IllegalArgumentException 如果版本号为负数
     */
    public static SemanticVersion createVersion(int major, int minor, int patch) {
        return new SemanticVersion(major, minor, patch);
    }

    /**
     * 创建初始版本号（1.0.0）
     *
     * @return 初始版本号
     */
    public static SemanticVersion createInitialVersion() {
        return SemanticVersion.initial();
    }

    /**
     * 创建零版本号（0.0.0）
     *
     * @return 零版本号
     */
    public static SemanticVersion createZeroVersion() {
        return SemanticVersion.zero();
    }

    /**
     * 解析版本号字符串
     *
     * @param version 版本号字符串，格式：MAJOR.MINOR.PATCH
     * @return SemanticVersion对象
     * @throws IllegalArgumentException 如果版本号格式不正确
     */
    public static SemanticVersion parseVersion(String version) {
        return SemanticVersion.parse(version);
    }

    /**
     * 根据变更类型递增版本号
     *
     * @param currentVersion 当前版本号字符串，不能为null或空
     * @param changeType 变更类型，不能为null
     * @return 新的版本号实例
     * @throws IllegalArgumentException 如果版本号格式不正确或变更类型为null
     */
    public static SemanticVersion incrementVersion(
            String currentVersion, VersionChangeType changeType) {
        if (changeType == null) {
            throw new IllegalArgumentException("变更类型不能为null");
        }
        SemanticVersion version = parseVersion(currentVersion);
        return version.increment(changeType);
    }

    /**
     * 递增主版本号
     *
     * @param currentVersion 当前版本号字符串
     * @return 新的版本号
     * @throws IllegalArgumentException 如果版本号格式不正确
     */
    public static SemanticVersion incrementMajorVersion(String currentVersion) {
        return incrementVersion(currentVersion, VersionChangeType.MAJOR);
    }

    /**
     * 递增次版本号
     *
     * @param currentVersion 当前版本号字符串
     * @return 新的版本号
     * @throws IllegalArgumentException 如果版本号格式不正确
     */
    public static SemanticVersion incrementMinorVersion(String currentVersion) {
        return incrementVersion(currentVersion, VersionChangeType.MINOR);
    }

    /**
     * 递增修订号
     *
     * @param currentVersion 当前版本号字符串
     * @return 新的版本号
     * @throws IllegalArgumentException 如果版本号格式不正确
     */
    public static SemanticVersion incrementPatchVersion(String currentVersion) {
        return incrementVersion(currentVersion, VersionChangeType.PATCH);
    }

    // ==================== 版本号验证功能 ====================

    /**
     * 验证版本号字符串格式是否正确
     *
     * @param version 版本号字符串
     * @return 如果格式正确则返回true
     */
    public static boolean isValidVersion(String version) {
        if (version == null || version.trim().isEmpty()) {
            return false;
        }

        try {
            parseVersion(version);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    /**
     * 验证版本号格式（仅检查正则表达式）
     *
     * @param version 版本号字符串
     * @return 如果格式符合正则表达式则返回true
     */
    public static boolean isValidVersionFormat(String version) {
        if (version == null || version.trim().isEmpty()) {
            return false;
        }
        return VERSION_PATTERN.matcher(version.trim()).matches();
    }

    /**
     * 验证版本号是否为预发布版本（主版本号为0）
     *
     * @param version 版本号字符串，不能为null或空
     * @return 如果是预发布版本则返回true
     * @throws IllegalArgumentException 如果版本号格式不正确
     */
    public static boolean isPreReleaseVersion(String version) {
        SemanticVersion semanticVersion = parseVersion(version);
        return semanticVersion.isPreRelease();
    }

    /**
     * 验证版本号是否为稳定版本（主版本号大于0）
     *
     * @param version 版本号字符串，不能为null或空
     * @return 如果是稳定版本则返回true
     * @throws IllegalArgumentException 如果版本号格式不正确
     */
    public static boolean isStableVersion(String version) {
        SemanticVersion semanticVersion = parseVersion(version);
        return semanticVersion.isStable();
    }

    // ==================== 版本号比较功能 ====================

    /**
     * 比较两个版本号
     *
     * @param version1 版本号1
     * @param version2 版本号2
     * @return 比较结果：负数表示version1小于version2，0表示相等，正数表示version1大于version2
     * @throws IllegalArgumentException 如果版本号格式不正确
     */
    public static int compareVersions(String version1, String version2) {
        SemanticVersion v1 = parseVersion(version1);
        SemanticVersion v2 = parseVersion(version2);
        return v1.compareTo(v2);
    }

    /**
     * 判断版本号是否相等
     *
     * @param version1 版本号1
     * @param version2 版本号2
     * @return 如果相等则返回true
     * @throws IllegalArgumentException 如果版本号格式不正确
     */
    public static boolean isVersionEqual(String version1, String version2) {
        return compareVersions(version1, version2) == 0;
    }

    /**
     * 判断版本号1是否大于版本号2
     *
     * @param version1 版本号1
     * @param version2 版本号2
     * @return 如果version1大于version2则返回true
     * @throws IllegalArgumentException 如果版本号格式不正确
     */
    public static boolean isVersionGreater(String version1, String version2) {
        return compareVersions(version1, version2) > 0;
    }

    /**
     * 判断版本号1是否小于版本号2
     *
     * @param version1 版本号1
     * @param version2 版本号2
     * @return 如果version1小于version2则返回true
     * @throws IllegalArgumentException 如果版本号格式不正确
     */
    public static boolean isVersionLess(String version1, String version2) {
        return compareVersions(version1, version2) < 0;
    }

    /**
     * 判断版本号1是否大于等于版本号2
     *
     * @param version1 版本号1
     * @param version2 版本号2
     * @return 如果version1大于等于version2则返回true
     * @throws IllegalArgumentException 如果版本号格式不正确
     */
    public static boolean isVersionGreaterOrEqual(String version1, String version2) {
        return compareVersions(version1, version2) >= 0;
    }

    /**
     * 判断版本号1是否小于等于版本号2
     *
     * @param version1 版本号1
     * @param version2 版本号2
     * @return 如果version1小于等于version2则返回true
     * @throws IllegalArgumentException 如果版本号格式不正确
     */
    public static boolean isVersionLessOrEqual(String version1, String version2) {
        return compareVersions(version1, version2) <= 0;
    }

    /**
     * 判断两个版本是否兼容 兼容性规则：主版本号相同且version1不低于version2
     *
     * @param version1 版本号1
     * @param version2 版本号2
     * @return 如果兼容则返回true
     * @throws IllegalArgumentException 如果版本号格式不正确
     */
    public static boolean isVersionCompatible(String version1, String version2) {
        SemanticVersion v1 = parseVersion(version1);
        SemanticVersion v2 = parseVersion(version2);
        return v1.isCompatibleWith(v2);
    }

    // ==================== 版本号排序功能 ====================

    /**
     * 对版本号列表进行升序排序
     *
     * @param versions 版本号字符串列表，可以为null或空
     * @return 排序后的版本号列表，如果输入为null或空则返回空列表
     * @throws IllegalArgumentException 如果版本号格式不正确
     */
    public static List<String> sortVersionsAscending(List<String> versions) {
        if (versions == null || versions.isEmpty()) {
            return new ArrayList<>();
        }

        // 使用Stream API优化性能
        return versions.stream()
                .map(SemanticVersionUtils::parseVersion)
                .sorted()
                .map(SemanticVersion::toString)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    /**
     * 对版本号列表进行降序排序
     *
     * @param versions 版本号字符串列表，可以为null或空
     * @return 排序后的版本号列表，如果输入为null或空则返回空列表
     * @throws IllegalArgumentException 如果版本号格式不正确
     */
    public static List<String> sortVersionsDescending(List<String> versions) {
        if (versions == null || versions.isEmpty()) {
            return new ArrayList<>();
        }

        // 使用Stream API优化性能，直接降序排序
        return versions.stream()
                .map(SemanticVersionUtils::parseVersion)
                .sorted(Collections.reverseOrder())
                .map(SemanticVersion::toString)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    /**
     * 从版本号列表中获取最新版本
     *
     * @param versions 版本号字符串列表，可以为null或空
     * @return 最新版本号，如果列表为null或空则返回null
     * @throws IllegalArgumentException 如果版本号格式不正确
     */
    public static String getLatestVersion(List<String> versions) {
        if (versions == null || versions.isEmpty()) {
            return null;
        }

        // 使用Stream API优化性能，直接找最大值
        return versions.stream()
                .map(SemanticVersionUtils::parseVersion)
                .max(SemanticVersion::compareTo)
                .map(SemanticVersion::toString)
                .orElse(null);
    }

    /**
     * 从版本号列表中获取最旧版本
     *
     * @param versions 版本号字符串列表，可以为null或空
     * @return 最旧版本号，如果列表为null或空则返回null
     * @throws IllegalArgumentException 如果版本号格式不正确
     */
    public static String getOldestVersion(List<String> versions) {
        if (versions == null || versions.isEmpty()) {
            return null;
        }

        // 使用Stream API优化性能，直接找最小值
        return versions.stream()
                .map(SemanticVersionUtils::parseVersion)
                .min(SemanticVersion::compareTo)
                .map(SemanticVersion::toString)
                .orElse(null);
    }

    // ==================== 版本号查询功能 ====================

    /**
     * 从版本号列表中筛选出指定主版本号的版本
     *
     * @param versions 版本号字符串列表，可以为null或空
     * @param majorVersion 主版本号
     * @return 筛选后的版本号列表
     * @throws IllegalArgumentException 如果版本号格式不正确或主版本号为负数
     */
    public static List<String> filterByMajorVersion(List<String> versions, int majorVersion) {
        if (majorVersion < 0) {
            throw new IllegalArgumentException("主版本号不能为负数: " + majorVersion);
        }
        if (versions == null || versions.isEmpty()) {
            return new ArrayList<>();
        }

        return versions.stream()
                .map(SemanticVersionUtils::parseVersion)
                .filter(v -> v.getMajor() == majorVersion)
                .map(SemanticVersion::toString)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    /**
     * 从版本号列表中筛选出预发布版本（主版本号为0）
     *
     * @param versions 版本号字符串列表，可以为null或空
     * @return 预发布版本列表
     * @throws IllegalArgumentException 如果版本号格式不正确
     */
    public static List<String> filterPreReleaseVersions(List<String> versions) {
        return filterByMajorVersion(versions, 0);
    }

    /**
     * 从版本号列表中筛选出稳定版本（主版本号大于0）
     *
     * @param versions 版本号字符串列表，可以为null或空
     * @return 稳定版本列表
     * @throws IllegalArgumentException 如果版本号格式不正确
     */
    public static List<String> filterStableVersions(List<String> versions) {
        if (versions == null || versions.isEmpty()) {
            return new ArrayList<>();
        }

        return versions.stream()
                .map(SemanticVersionUtils::parseVersion)
                .filter(SemanticVersion::isStable)
                .map(SemanticVersion::toString)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }
}
