package com.pulse.pulse.common.context;

import com.vs.common.util.rpc.context.Endpoint;
import com.vs.common.util.rpc.context.LoginContext;
import com.vs.common.util.rpc.context.LoginContextParam;
import com.vs.common.util.rpc.context.User;
import com.vs.ox.common.utils.JsonUtils;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

public class UserContext {
    /** 员工id */
    static final ThreadLocal<String> staffId = new ThreadLocal<>();

    static final ThreadLocal<String> userId = new ThreadLocal<>();
    static final ThreadLocal<String> appId = new ThreadLocal<>();
    static final ThreadLocal<String> ward = new ThreadLocal<>();
    static final ThreadLocal<String> department = new ThreadLocal<>();
    static final ThreadLocal<String> campus = new ThreadLocal<>();
    static final ThreadLocal<String> token = new ThreadLocal<>();
    static final ThreadLocal<String> sessionId = new ThreadLocal<>();

    /** 员工信息 */
    // static final ThreadLocal<Holder<StaffDto>> staff = new ThreadLocal<>();
    /** 用户信息 */
    // static final ThreadLocal<Holder<UserBaseDto>> user = new ThreadLocal<>();
    /** 用户会话 */
    // static final ThreadLocal<Holder<UserSeesionDto>> userSeesion = new ThreadLocal<>();
    /** 当前工作站id */
    static final ThreadLocal<String> applicationId = new ThreadLocal<>();

    static final ThreadLocal<ExtInfo> extInfo = new ThreadLocal<>();
    private static final Logger log = LoggerFactory.getLogger(LoginContext.class);
    private static ThreadLocal<Map<String, User>> currentUsers =
            ThreadLocal.withInitial(
                    () -> {
                        return new HashMap();
                    });
    private static ThreadLocal<String> domainId = new ThreadLocal();
    private static ThreadLocal<Endpoint> endpoint = new ThreadLocal();
    private static ThreadLocal<ClientPoint> clickpoint = new ThreadLocal();
    private static ThreadLocal<LoginContextParam> loginContextParam = new ThreadLocal();

    public UserContext() {}

    /**
     * 获取当前工作站id
     *
     * @return
     */
    public static String getApplicationId() {
        if (applicationId.get() == null) {}
        return applicationId.get();
    }

    public static void setApplicationId(String appId) {
        applicationId.set(appId);
    }

    public static String getStaffId() {
        if (staffId.get() == null) {}
        return staffId.get();
    }

    public static void setStaffId(String staff) {
        staffId.set(staff);
    }

    public static void setUserId(String user) {
        userId.set(user);
    }

    public static String getUserId() {
        if (userId.get() == null) {}
        return userId.get();
    }

    public static void setWard(String Ward) {
        ward.set(Ward);
    }

    public static String getWard() {
        if (ward.get() == null) {}
        return ward.get();
    }

    public static void setDepartment(String Department) {
        department.set(Department);
    }

    public static String getDepartment() {
        if (department.get() == null) {}
        return department.get();
    }

    public static void setCampus(String Campus) {
        campus.set(Campus);
    }

    public static String getCampus() {
        if (campus.get() == null) {}
        return campus.get();
    }

    public static void setToken(String Token) {
        token.set(Token);
    }

    public static String getToken() {
        if (token.get() == null) {}
        return token.get();
    }

    private static ExtInfo getExtInfo() {
        if (extInfo.get() == null) {
            extInfo.set(
                    JsonUtils.readObject(
                            Optional.ofNullable(LoginContext.getLoginContextParam())
                                    .map(LoginContextParam::getExtInfo)
                                    .orElse(null),
                            ExtInfo.class));
        }
        return extInfo.get();
    }

    public static Map<String, User> getCurrentUsers() {
        return (Map) currentUsers.get();
    }

    public static User getCurrentUser() {
        return getCurrentUser("any");
    }

    public static User getCurrentUser(String type) {
        if (currentUsers.get() == null) {
            return null;
        } else {
            return type.equals("any")
                    ? (User)
                            ((Map) currentUsers.get())
                                    .values().stream().findAny().orElse((Object) null)
                    : (User) ((Map) currentUsers.get()).get(type);
        }
    }

    public static void setCurrentUsers(Map<String, User> users) {
        currentUsers.set(users);
    }

    public static String getDomainId() {
        return (String) domainId.get();
    }

    public static void setDomainId(String domainId) {
        UserContext.domainId.set(domainId);
    }

    public static LoginContextParam getLoginContextParam() {
        return (LoginContextParam) loginContextParam.get();
    }

    public static void setLoginContextParam(LoginContextParam loginContextParam) {
        UserContext.loginContextParam.set(loginContextParam);
    }

    public static Endpoint getEndpoint() {
        return (Endpoint) endpoint.get();
    }

    public static void setEndpoint(Endpoint endpoint) {
        UserContext.endpoint.set(endpoint);
    }

    public static void setClickpoint(ClientPoint clickpoint) {
        UserContext.clickpoint.set(clickpoint);
    }

    public static void clear() {
        applicationId.remove();
        staffId.remove();
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExtInfo {
        private String applicationId;
        private String staffId;
        private String userId;
        private String department;
        private String campus;
        private String ward;
        private String token;

        public void setApplicationId(String applicationId) {
            applicationId = applicationId;
        }
    }
}
