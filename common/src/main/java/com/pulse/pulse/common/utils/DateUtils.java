package com.pulse.pulse.common.utils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Date;

/** 日期工具类，提供常见的日期解析、转换、比较和空值处理等功能。 */
public class DateUtils {
    /** 默认的日期格式字符串。 */
    private static final String DEFAULT_DATE_PATTERN = "yyyy-MM-dd HH:mm:ss";

    /** 日期格式化对象，用于将日期对象转换为字符串。 */
    private static final DateTimeFormatter formatter =
            DateTimeFormatter.ofPattern(DEFAULT_DATE_PATTERN);

    /** 默认最小有效时间(字符串常量)，默认为 1970 年 1 月 1 日 00:00:00。 */
    public static final String DEFAULT_MIN_DATE = "1970-01-01 00:00:00";

    /** 默认最大有效时间(字符串常量)，默认为 9999 年 12 月 31 日 23:59:59。 */
    public static final String DEFAULT_MAX_DATE = "9999-12-31 23:59:59";

    /**
     * 判断两个时间段是否存在重叠。
     *
     * <p>支持任意一方无结束时间的情况，此时只要另一方在范围内即视为重叠。
     *
     * @param startTime 时间段 A 的开始时间，允许为 null
     * @param endTime 时间段 A 的结束时间，允许为 null 表示无限延伸
     * @param anotherStartTime 时间段 B 的开始时间，允许为 null
     * @param anotherEndTime 时间段 B 的结束时间，允许为 null 表示无限延伸
     * @return 如果两个时间段存在重叠则返回 true，否则返回 false
     */
    public static boolean isOverlapping(
            Date startTime, Date endTime, Date anotherStartTime, Date anotherEndTime) {
        // a没有结束时间，b在a之后开始，必然重叠
        if (endTime == null && !anotherStartTime.before(startTime)) {
            return true;
        }

        // b没有结束时间，a在b之前开始，必然重叠
        if (anotherEndTime == null && !startTime.after(anotherStartTime)) {
            return true;
        }

        // 都有结束时间时的常规检查
        if (endTime != null && anotherEndTime != null) {
            return startTime.before(anotherEndTime) && anotherStartTime.before(endTime);
        }

        return false;
    }

    /**
     * 将 LocalDateTime 转换为 Date。
     *
     * <p>使用系统默认时区进行转换。
     *
     * @param localDateTime LocalDateTime 对象，允许为 null
     * @return 对应的 Date 对象；若输入为 null 则返回 null
     */
    public static Date toDate(LocalDateTime localDateTime) {
        return localDateTime == null
                ? null
                : Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 将 Date 转换为 LocalDateTime。
     *
     * <p>使用系统默认时区进行转换。
     *
     * @param date Date 对象，允许为 null
     * @return 对应的 LocalDateTime 对象；若输入为 null 则返回 null
     */
    public static LocalDateTime toLocalDateTime(Date date) {
        return date == null
                ? null
                : date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    /**
     * 将字符串形式的日期转成 LocalDateTime 对象，默认格式为 yyyy-MM-dd HH:mm:ss。
     *
     * <p>若字符串为空或解析失败，则返回 null。如需更严格的异常处理逻辑，可自定义封装方式。
     *
     * @param dateStr 待解析的日期字符串，允许为 null 或空白字符串
     * @return 解析后的 LocalDateTime 对象；若输入无效则返回 null
     * @throws IllegalArgumentException 若日期格式不符合要求且项目规范要求抛出异常
     */
    public static LocalDateTime parse(String dateStr) {
        String trimmed = (dateStr == null) ? null : dateStr.trim();
        if (trimmed == null || trimmed.isEmpty()) {
            return null;
        }
        try {
            return LocalDateTime.parse(trimmed, formatter);
        } catch (DateTimeParseException e) {
            // 可根据项目规范选择抛出异常或记录日志
            throw new IllegalArgumentException(
                    "Invalid date format: "
                            + trimmed
                            + ", expected format: "
                            + DEFAULT_DATE_PATTERN,
                    e);
        }
    }

    /**
     * 将字符串形式的日期转成 Date 对象，默认格式为 yyyy-MM-dd HH:mm:ss。
     *
     * <p>内部调用 {@link #parse(String)} 并通过系统默认时区转换为 Date。
     *
     * @param dateStr 待解析的日期字符串，允许为 null 或空白字符串
     * @return 解析后的 Date 对象；若输入无效则返回 null
     * @throws IllegalArgumentException 若日期格式不符合要求且项目规范要求抛出异常
     */
    public static Date parseDate(String dateStr) {
        LocalDateTime localDateTime = parse(dateStr);
        return localDateTime == null ? null : toDate(localDateTime);
    }

    /**
     * 检查传入的 LocalDateTime 是否为 null，若为 null 则尝试使用默认日期字符串解析并返回。
     *
     * @param date 被检查的 LocalDateTime 对象，可能为 null
     * @param defaultDate 默认日期字符串，用于解析生成默认值（若 date 为 null）
     * @return 原始 date 或解析后的默认日期对象
     * @see #parse(String)
     */
    public static LocalDateTime checkDate(LocalDateTime date, String defaultDate) {
        return date != null ? date : parse(defaultDate);
    }

    /**
     * 检查传入的 Date 是否为 null，若为 null 则尝试使用默认日期字符串解析并返回 Date 对象。
     *
     * <p>兼容性方法，适用于遗留代码中使用 Date 类型的场景。
     *
     * @param date 被检查的 Date 对象，可能为 null
     * @param defaultDate 默认日期字符串，用于解析生成默认值（若 date 为 null）
     * @return 原始 date 或解析后的默认日期对应的 Date 对象
     * @see #parse(String)
     * @see #toDate(LocalDateTime)
     */
    public static Date checkDate(Date date, String defaultDate) {
        LocalDateTime result = checkDate(toLocalDateTime(date), defaultDate);
        return toDate(result);
    }
}
