package com.pulse.pulse.common.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * JSON比较工具类
 *
 * <p>主要功能：
 *
 * <ul>
 *   <li>比较两个JSON字符串，找出变化的字段
 *   <li>支持嵌套对象和数组的比较
 *   <li>支持不同数据类型的比较
 *   <li>提供灵活的字段路径处理
 * </ul>
 *
 * <p>使用场景：
 *
 * <ul>
 *   <li>操作日志中比较操作前后的数据变化
 *   <li>数据审计中追踪字段变更
 *   <li>版本控制中比较数据差异
 * </ul>
 */
@Slf4j
public class JsonCompareUtils {

    /**
     * 比较两个JSON字符串，返回变化的顶级字段名列表
     *
     * @param beforeJson 变化前的JSON字符串
     * @param afterJson 变化后的JSON字符串
     * @return 变化的顶级字段名列表，保持插入顺序
     * @throws IgnoredException 当JSON解析失败时抛出
     * @throws IllegalArgumentException 当输入参数为null时抛出
     */
    public static List<String> getChangedTopLevelFields(String beforeJson, String afterJson) {
        return compareJsonStrings(beforeJson, afterJson, true);
    }

    /**
     * 比较两个JSON字符串，返回变化的所有字段路径列表
     *
     * @param beforeJson 变化前的JSON字符串
     * @param afterJson 变化后的JSON字符串
     * @return 变化的字段路径列表（包含嵌套路径，如 "user.address.city"），保持插入顺序
     * @throws IgnoredException 当JSON解析失败时抛出
     * @throws IllegalArgumentException 当输入参数为null时抛出
     */
    public static List<String> getChangedFieldPaths(String beforeJson, String afterJson) {
        return compareJsonStrings(beforeJson, afterJson, false);
    }

    /**
     * 比较两个JSON字符串的通用方法
     *
     * @param beforeJson 变化前的JSON字符串
     * @param afterJson 变化后的JSON字符串
     * @param onlyTopLevel 是否只返回顶级字段名
     * @return 变化的字段列表
     * @throws IgnoredException 当JSON解析失败时抛出
     * @throws IllegalArgumentException 当输入参数为null时抛出
     */
    private static List<String> compareJsonStrings(
            String beforeJson, String afterJson, boolean onlyTopLevel) {
        // 参数验证
        if (beforeJson == null || afterJson == null) {
            throw new IllegalArgumentException("JSON字符串不能为null");
        }

        // 使用LinkedHashSet保持插入顺序并去重
        Set<String> changedFields = new LinkedHashSet<>();

        // 如果前后信息有任一为空字符串，则无法比较
        if (StringUtils.isBlank(beforeJson) || StringUtils.isBlank(afterJson)) {
            return new ArrayList<>(changedFields);
        }

        try {
            // 获取ObjectMapper实例
            ObjectMapper mapper = JsonUtils.getObjectMapper();

            // 将JSON字符串转换为JsonNode对象
            JsonNode beforeNode = mapper.readTree(beforeJson);
            JsonNode afterNode = mapper.readTree(afterJson);

            // 比较两个JsonNode，找出变化的字段
            compareJsonNodes("", beforeNode, afterNode, changedFields, onlyTopLevel);

        } catch (JsonProcessingException e) {
            String errorMsg =
                    String.format(
                            "解析JSON时出错，beforeJson长度: %d, afterJson长度: %d, 错误: %s",
                            beforeJson.length(), afterJson.length(), e.getMessage());
            log.error(errorMsg, e);
            throw new IgnoredException(errorMsg, ErrorCode.SYS_ERROR, e, null);
        } catch (Exception e) {
            String errorMsg = String.format("比较JSON时发生未知错误: %s", e.getMessage());
            log.error(errorMsg, e);
            throw new IgnoredException(errorMsg, ErrorCode.SYS_ERROR, e, null);
        }

        return new ArrayList<>(changedFields);
    }

    /**
     * 递归比较两个JsonNode，找出变化的字段
     *
     * @param path 当前字段路径
     * @param beforeNode 变化前的JsonNode
     * @param afterNode 变化后的JsonNode
     * @param changedFields 变化的字段集合
     * @param onlyTopLevel 是否只返回顶级字段名
     */
    private static void compareJsonNodes(
            String path,
            JsonNode beforeNode,
            JsonNode afterNode,
            Set<String> changedFields,
            boolean onlyTopLevel) {
        // 处理null节点的情况
        if (beforeNode == null && afterNode == null) {
            return;
        }
        if (beforeNode == null || afterNode == null) {
            addChangedField(path, changedFields, onlyTopLevel);
            return;
        }

        // 如果节点类型不同，则认为字段已变化
        if (beforeNode.getNodeType() != afterNode.getNodeType()) {
            addChangedField(path, changedFields, onlyTopLevel);
            return;
        }

        // 根据节点类型进行不同的比较
        switch (beforeNode.getNodeType()) {
            case OBJECT:
                compareObjectNodes(path, beforeNode, afterNode, changedFields, onlyTopLevel);
                break;
            case ARRAY:
                compareArrayNodes(path, beforeNode, afterNode, changedFields, onlyTopLevel);
                break;
            default:
                // 对于基本类型，直接比较值是否相等
                if (!beforeNode.equals(afterNode)) {
                    addChangedField(path, changedFields, onlyTopLevel);
                }
                break;
        }
    }

    /** 比较两个对象类型的JsonNode */
    private static void compareObjectNodes(
            String path,
            JsonNode beforeNode,
            JsonNode afterNode,
            Set<String> changedFields,
            boolean onlyTopLevel) {
        // 使用Set收集所有字段名，提高效率
        Set<String> fieldNames = new HashSet<>();
        beforeNode.fieldNames().forEachRemaining(fieldNames::add);
        afterNode.fieldNames().forEachRemaining(fieldNames::add);

        // 比较每个字段
        for (String fieldName : fieldNames) {
            JsonNode beforeField = beforeNode.get(fieldName);
            JsonNode afterField = afterNode.get(fieldName);

            // 如果字段在任一对象中不存在，则认为字段已变化
            if (beforeField == null || afterField == null) {
                addChangedField(getFieldPath(path, fieldName), changedFields, onlyTopLevel);
                continue;
            }

            // 递归比较字段值
            compareJsonNodes(
                    getFieldPath(path, fieldName),
                    beforeField,
                    afterField,
                    changedFields,
                    onlyTopLevel);
        }
    }

    /** 比较两个数组类型的JsonNode */
    private static void compareArrayNodes(
            String path,
            JsonNode beforeNode,
            JsonNode afterNode,
            Set<String> changedFields,
            boolean onlyTopLevel) {
        // 如果数组长度不同，则认为字段已变化
        if (beforeNode.size() != afterNode.size()) {
            addChangedField(path, changedFields, onlyTopLevel);
            return;
        }

        // 比较数组中的每个元素
        for (int i = 0; i < beforeNode.size(); i++) {
            compareJsonNodes(
                    buildArrayPath(path, i),
                    beforeNode.get(i),
                    afterNode.get(i),
                    changedFields,
                    onlyTopLevel);
        }
    }

    /**
     * 获取字段的完整路径
     *
     * @param basePath 基础路径
     * @param fieldName 字段名
     * @return 完整的字段路径
     */
    private static String getFieldPath(String basePath, String fieldName) {
        if (StringUtils.isBlank(basePath)) {
            return fieldName;
        }
        return basePath + "." + fieldName;
    }

    /**
     * 构建数组元素的路径
     *
     * @param basePath 基础路径
     * @param index 数组索引
     * @return 数组元素的完整路径
     */
    private static String buildArrayPath(String basePath, int index) {
        return basePath + "[" + index + "]";
    }

    /**
     * 添加变化的字段到集合中
     *
     * @param path 字段路径
     * @param changedFields 变化的字段集合
     * @param onlyTopLevel 是否只添加顶级字段名
     */
    private static void addChangedField(
            String path, Set<String> changedFields, boolean onlyTopLevel) {
        if (StringUtils.isBlank(path)) {
            return;
        }

        String fieldToAdd = path;

        if (onlyTopLevel) {
            // 提取顶级字段名（路径中第一个点或方括号之前的部分）
            fieldToAdd = extractTopLevelFieldName(path);
        }

        // 使用Set自动去重，无需手动检查是否存在
        if (StringUtils.isNotBlank(fieldToAdd)) {
            changedFields.add(fieldToAdd);
        }
    }

    /**
     * 从字段路径中提取顶级字段名
     *
     * @param path 完整的字段路径
     * @return 顶级字段名
     */
    private static String extractTopLevelFieldName(String path) {
        if (StringUtils.isBlank(path)) {
            return path;
        }

        int dotIndex = path.indexOf('.');
        int bracketIndex = path.indexOf('[');

        // 找到第一个分隔符的位置
        int separatorIndex = -1;
        if (dotIndex > 0 && bracketIndex > 0) {
            separatorIndex = Math.min(dotIndex, bracketIndex);
        } else if (dotIndex > 0) {
            separatorIndex = dotIndex;
        } else if (bracketIndex > 0) {
            separatorIndex = bracketIndex;
        }

        return separatorIndex > 0 ? path.substring(0, separatorIndex) : path;
    }
}
