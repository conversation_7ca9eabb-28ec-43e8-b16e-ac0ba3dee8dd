spring.profiles.active=local
envs=local,remote,online,custom
spring.main.allow-bean-definition-overriding=true
project_id=${projectId}
project_name=pulse
check=true
spring.mvc.pathmatch.matching-strategy=ANT_PATH_MATCHER
#注销掉es的自动装配
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration
com.toco.agent.attach=true
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# nacos core config
nacos.core.auth.enabled=true
nacos.core.auth.default.token.expire.seconds=3600
nacos.core.auth.plugin.nacos.token.cache.enable=true