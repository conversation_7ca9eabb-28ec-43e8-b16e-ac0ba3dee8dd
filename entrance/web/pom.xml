<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.pulse</groupId>
    <artifactId>pulse-entrance</artifactId>
    <version>1.0-SNAPSHOT</version>
  </parent>

  <artifactId>pulse-entrance-web</artifactId>

  <dependencies>
    <dependency>
      <groupId>com.toco</groupId>
      <artifactId>vs-sqlmapper-spring</artifactId>
    </dependency>
    <dependency>
      <groupId>com.pulse</groupId>
      <artifactId>pulse-entrance-mq</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <layers>
            <enabled>true</enabled>
          </layers>
          <skip>false</skip>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
